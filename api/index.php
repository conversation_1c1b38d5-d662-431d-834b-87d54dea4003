<?php
/**
 * Point d'entrée API
 * 
 * @package TechCMS
 * @version 1.0.0
 */

define('IN_API', true);
define('ROOTPATH', dirname(__DIR__));



// Charger l'autoloader avant l'initialisation
require_once ROOTPATH . '/includes/autoload.php';
require_once ROOTPATH . '/includes/init.php';

use TechCMS\Common\Core\Router;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\Response;
use TechCMS\Common\Core\Database;
use TechCMS\Api\V1\Middleware\ApiAuth;
use TechCMS\Api\V1\Middleware\ApiLogger;

// Configuration headers API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Gestion OPTIONS pour CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {

    http_response_code(200);
    exit;
}

try {
    // Initialisation des middlewares
    $apiAuth = new ApiAuth();
    $apiLogger = new ApiLogger();

    // Router API
    $router = Router::getInstance();
    // Désactiver les logs de débogage
    // Logger::channel('api')->debug('[API] Router initialisé');

    // Application des middlewares
    $router->before(function() use ($apiAuth, $apiLogger) {
        try {
            // Désactiver les logs de débogage
            // Logger::channel('api')->debug('[API] Exécution des middlewares');
            
            // 1. Logger pour tracer toutes les requêtes
            $apiLogger->handle();
            
            // 2. Auth pour les routes protégées
            $path = $_SERVER['REQUEST_URI'];

            // Debug : logger le chemin pour diagnostiquer
            Logger::channel('api')->debug('[API] Vérification chemin', [
                'path' => $path,
                'is_excluded' => $apiAuth->isExcludedPath($path)
            ]);

            if (!$apiAuth->isExcludedPath($path)) {
                if (!$apiAuth->check()) {
                    Logger::channel('api')->warning('Authentification requise', ['path' => $path]);
                    http_response_code(401);
                    echo json_encode(['error' => 'Unauthorized']);
                    exit;
                }
            }
            
            // Désactiver les logs de débogage
            // Logger::channel('api')->debug('[API] Middlewares exécutés avec succès');
        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur dans les middlewares', ['error' => $e->getMessage()]);
            http_response_code(500);
            echo json_encode(['error' => 'Internal Server Error']);
            exit;
        }
    });

    // Chargement des routes
    require_once __DIR__ . '/v1/routes.php';
    
    // Exécution du routeur
    $router->run();
    
} catch (\Exception $e) {
    Logger::channel('api')->error('Erreur globale', ['error' => $e->getMessage()]);
    http_response_code(500);
    echo json_encode(['error' => 'Internal Server Error']);
}
