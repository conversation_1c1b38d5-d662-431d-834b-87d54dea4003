<?php
/**
 * Routes client principale
 * 
 * Ce fichier importe toutes les sous-routes client organisées par domaine fonctionnel
 * et définit le groupe de routes client avec middleware d'authentification client.
 * 
 * @package TechCMS
 * @version 1.0.0
 */

use TechCMS\Common\Core\Router;
use TechCMS\Api\V1\Middleware\ApiAuth;
use TechCMS\Api\V1\Middleware\Client\ClientAuthMiddleware;
use TechCMS\Api\V1\Controllers\Client\ClientLogController;

// Obtenir l'instance du routeur
$router = Router::getInstance();

// Récupérer les middlewares globaux
$apiAuthMiddleware = new ApiAuth();
$clientAuthMiddleware = new ClientAuthMiddleware();

// Route publique de logging client (accessible sans authentification)
$logController = new ClientLogController();
$router->post('/client/log', [$logController, 'log']);

// Route pour le token Ably temps réel client (authentification client requise)
$clientAuthController = new \TechCMS\Api\V1\Controllers\Client\ClientAuthController();
$router->get('/client/realtime/token', [$clientAuthController, 'getRealtimeToken']);

// Groupe principal pour les routes client avec authentification client uniquement
$router->group('/client', ['middleware' => [[$clientAuthMiddleware, '__invoke']]], function() use ($router) {

    // Importer les routes spécifiques à chaque domaine fonctionnel client
    require_once __DIR__ . '/dashboard/dashboard_routes.php';
    require_once __DIR__ . '/service/service_routes.php';
    require_once __DIR__ . '/invoice/invoice_routes.php';
    require_once __DIR__ . '/ticket/ticket_routes.php';
    require_once __DIR__ . '/profile/profile_routes.php';
    require_once __DIR__ . '/department/department_routes.php';
    require_once __DIR__ . '/license/license_routes.php';
    require_once __DIR__ . '/updates/updates_routes.php';

});