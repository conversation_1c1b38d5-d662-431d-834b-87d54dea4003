<?php
/**
 * Routes API
 * 
 * Ce fichier sert de point d'entrée principal pour toutes les routes de l'API.
 * Il chargera progressivement les routes modulaires à mesure que nous les migrerons.
 * 
 * @package TechCMS
 * @version 1.0.0
 */

// Ces routes seront chargées à l'intérieur du groupe /api/v1 plus bas
// pour assurer qu'elles héritent du préfixe /api/v1


// Contrôleurs API standards
use TechCMS\Api\V1\Controllers\Admin\AdminAuthController;
use TechCMS\Api\V1\Controllers\Client\ClientAuthController;
use TechCMS\Api\V1\Controllers\TokenController;

// Contrôleurs admin
use TechCMS\Api\V1\Controllers\Admin\Client\AdminClientController;
use TechCMS\Api\V1\Controllers\Admin\Invoice\AdminInvoiceController;
use TechCMS\Api\V1\Controllers\Admin\Payment\AdminPaymentController;

use TechCMS\Api\V1\Controllers\Admin\Ticket\AdminTicketController;
use TechCMS\Api\V1\Controllers\Admin\Ticket\AdminTicketDepartmentController;
use TechCMS\Api\V1\Controllers\Admin\Ticket\AdminTicketAssignmentController;
use TechCMS\Api\V1\Controllers\Admin\System\AdminSettingsController;

use TechCMS\Api\V1\Controllers\Admin\System\AdminDashboardController;
use TechCMS\Api\V1\Controllers\Admin\System\AdminNotificationController;
use TechCMS\Api\V1\Controllers\Admin\System\AdminLogController;
use TechCMS\Api\V1\Controllers\Admin\System\AdminAutomationController;
use TechCMS\Api\V1\Controllers\Admin\System\AdminUserController;

use TechCMS\Common\Core\Router;
use TechCMS\Api\V1\Core\JWT;
use TechCMS\Api\V1\Middleware\ApiAuth;
use TechCMS\Api\V1\Middleware\Admin\JWTAuthMiddleware;

// Utiliser l'instance existante du Router
$router = Router::getInstance();

// Instancier les contrôleurs

// API Standards
$authController = new AdminAuthController();
$clientAuthController = new ClientAuthController();
$tokenController = new TokenController();

// Contrôleurs admin
$clientController = new AdminClientController();
$invoiceController = new AdminInvoiceController();
$paymentController = new AdminPaymentController();

$ticketController = new AdminTicketController();
$ticketDepartmentController = new AdminTicketDepartmentController();
$ticketAssignmentController = new AdminTicketAssignmentController();
$settingsController = new AdminSettingsController();

$dashboardController = new AdminDashboardController();
$notificationController = new AdminNotificationController();
$logController = new AdminLogController();
$automationController = new AdminAutomationController();

$adminController = new AdminUserController();

// Instancier les middlewares
$apiAuthMiddleware = new ApiAuth();
$jwtAuthMiddleware = new JWTAuthMiddleware();

// Routes API v1
$router->group('', ['prefix' => '/api/v1'], function($router) use ($authController, $clientAuthController, $tokenController, $clientController, $invoiceController, $paymentController, $ticketController, $dashboardController, $notificationController, $settingsController, $ticketDepartmentController, $ticketAssignmentController, $logController, $adminController, $apiAuthMiddleware, $jwtAuthMiddleware, $automationController) {
    // Chargement des routes modulaires avec préfixe /api/v1
    require_once __DIR__ . '/routes/admin/admin_routes.php';
    require_once __DIR__ . '/routes/client/client_routes.php';
    require_once __DIR__ . '/routes/website/website_routes.php';
    
    // Routes d'authentification admin (pas de middleware auth)
    $router->post('/auth/token', [$tokenController, 'create']);
    $router->post('/auth/validate', [$tokenController, 'validate']);
    $router->post('/auth/login', [$authController, 'login']);
    $router->post('/auth/forgot-password', [$authController, 'forgotPassword']);
    $router->post('/auth/reset-password', [$authController, 'resetPassword']);
    $router->get('/auth/check', [$authController, 'check']);

    // Routes d'authentification client (pas de middleware auth)
    $router->post('/client/auth/login', [$clientAuthController, 'login']);
    $router->post('/client/auth/register', [$clientAuthController, 'register']);
    $router->post('/client/auth/logout', [$clientAuthController, 'logout']);
    $router->get('/client/auth/me', [$clientAuthController, 'me']);

    // Routes d'authentification admin (pas de middleware auth)
    $router->get('/auth/me', [$authController, 'me']);
    $router->post('/auth/logout', [$authController, 'logout']);


    // Routes protégées par middleware
    $router->group('', ['middleware' => [[$apiAuthMiddleware, 'handle'], $jwtAuthMiddleware]], function($router) use ($authController, $tokenController, $clientController, $invoiceController, $paymentController, $ticketController, $dashboardController, $notificationController, $settingsController, $ticketDepartmentController, $ticketAssignmentController, $logController, $adminController, $automationController) {
        
        // Route pour le token Ably temps réel
        $router->get('/realtime/token', [$authController, 'getRealtimeToken']);

        // Routes de gestion des tokens
        $router->get('/auth/tokens', [$tokenController, 'index']);
        $router->delete('/auth/tokens/{id}', [$tokenController, 'revoke']);
        
        // Routes pour les administrateurs
        $router->get('/admins', [$adminController, 'index']);
        $router->get('/admins/{id}', [$adminController, 'show']);
    });
});

// Routes publiques pour la vérification des licences (sans authentification)
// Ces routes sont en dehors du groupe principal pour éviter les middlewares
$licenseApiController = new \TechCMS\Api\V1\Controllers\LicenseApiController();

// POST /api/v1/license/verify - Vérifie la validité d'une licence
$router->post('/api/v1/license/verify', [$licenseApiController, 'verify']);

// POST /api/v1/license/status - Récupère le statut d'une licence
$router->post('/api/v1/license/status', [$licenseApiController, 'status']);

// POST /api/v1/license/validate-installation - Valide une installation
$router->post('/api/v1/license/validate-installation', [$licenseApiController, 'validateInstallation']);

// GET /api/v1/license/verify - Alternative GET pour la vérification
$router->get('/api/v1/license/verify', [$licenseApiController, 'verify']);

// GET /api/v1/license/status - Alternative GET pour le statut
$router->get('/api/v1/license/status', [$licenseApiController, 'status']);

// Route de test pour vérifier que l'API de licence fonctionne
$router->get('/api/v1/license/ping', function() {
    http_response_code(200);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'API de licence opérationnelle',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
});

// Routes publiques pour le système de mise à jour automatique (sans authentification)
// Ces routes sont en dehors du groupe principal pour éviter les middlewares
$updateApiController = new \TechCMS\Api\V1\Controllers\UpdateApiController();

// GET/POST /api/v1/updates/check - Vérification des mises à jour disponibles
$router->get('/api/v1/updates/check', [$updateApiController, 'check']);
$router->post('/api/v1/updates/check', [$updateApiController, 'check']);

// GET/HEAD /api/v1/updates/download/{token} - Téléchargement sécurisé des mises à jour
$router->get('/api/v1/updates/download/{token}', [$updateApiController, 'download']);
$router->head('/api/v1/updates/download/{token}', [$updateApiController, 'download']);

// POST /api/v1/updates/status - Rapport de statut d'installation
$router->post('/api/v1/updates/status', [$updateApiController, 'status']);

// Route de test pour vérifier que l'API de mise à jour fonctionne
$router->get('/api/v1/updates/ping', function() {
    http_response_code(200);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'message' => 'API de mise à jour opérationnelle',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
});
