<?php
/**
 * Middleware d'authentification JWT
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Api\V1\Middleware\Admin;

use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\Request;
use TechCMS\Common\Core\Response;
use TechCMS\Api\V1\Core\JWT;

class JWTAuthMiddleware {
    /**
     * Vérifie si le token JWT est valide
     *
     * @param Request $request
     * @return Response|null
     */
    public function __invoke(Request $request) {
        // Si l'utilisateur est déjà authentifié par un autre middleware, ne rien faire
        if ($request->getUser() !== null) {
            return null;
        }
        
        // Récupérer le token depuis le header Authorization ou le cookie
        $token = $this->getToken($request);
        
        if (!$token) {
            Logger::channel('api')->warning('Token non trouvé dans la requête');
            return (new Response())
                ->setStatusCode(401)
                ->json(['message' => 'Token manquant']);
        }

        try {
            // Vérifier et décoder le token
            $payload = JWT::verify($token);
            
            // Ajouter les informations de l'utilisateur à la requête
            $request->setUser($payload);
            
            return null;
        } catch (\Exception $e) {
            Logger::channel('api')->warning('Tentative d\'accès avec token invalide', ['error' => $e->getMessage()]);
            return (new Response())
                ->setStatusCode(401)
                ->json(['message' => 'Token invalide']);
        }
    }
    
    /**
     * Récupère le token depuis l'en-tête Authorization ou les cookies (admin et client)
     */
    private function getToken(Request $request) {
        // Essayer d'abord l'en-tête Authorization
        $authHeader = $request->getHeader('Authorization');
        if ($authHeader && preg_match('/^Bearer\s+(.+)$/i', $authHeader, $matches)) {
            return $matches[1];
        }

        // Ensuite, vérifier les cookies admin
        if (isset($_COOKIE['token'])) {
            return $_COOKIE['token'];
        }

        // Enfin, vérifier les cookies client
        if (isset($_COOKIE['client_token'])) {
            return $_COOKIE['client_token'];
        }

        return null;
    }
}
