<?php
/**
 * Contrôleur d'authentification client
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Api\V1\Controllers\Client;

use TechCMS\Api\V1\Controllers\ApiBaseController;
use TechCMS\Api\V1\Core\JWT;
use TechCMS\Common\Core\Database;
use TechCMS\Common\Models\ClientModel;
use TechCMS\Api\V1\Models\ApiTokenModel;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\RealTime;

class ClientAuthController extends ApiBaseController {
    protected $db;
    protected $clientModel;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->clientModel = new ClientModel();
    }

    /**
     * Connexion client
     */
    public function login() {
        $data = json_decode(file_get_contents('php://input'), true);

        Logger::channel('api')->info('[ClientAuth] Données de connexion reçues', [
            'data_received' => $data ? 'yes' : 'no',
            'email_present' => isset($data['email']) ? 'yes' : 'no',
            'password_present' => isset($data['password']) ? 'yes' : 'no'
        ]);

        if (!isset($data['email']) || !isset($data['password'])) {
            return $this->sendError('Email et mot de passe requis', 400);
        }

        $email = $data['email'];
        $password = $data['password'];

        Logger::channel('api')->info('[ClientAuth] Tentative de connexion', [
            'email' => $email,
            'password_length' => strlen($password)
        ]);

        try {
            // Vérifier d'abord si le client existe
            $clientExists = $this->clientModel->findByEmail($email);
            Logger::channel('api')->info('[ClientAuth] Recherche client par email', [
                'email' => $email,
                'client_found' => $clientExists ? 'yes' : 'no',
                'client_status' => $clientExists ? $clientExists['status'] : 'n/a',
                'client_id' => $clientExists ? $clientExists['id'] : 'n/a'
            ]);

            // Authentifier le client
            $client = $this->clientModel->authenticate($email, $password);

            if (!$client) {
                Logger::channel('api')->warning('[ClientAuth] Tentative de connexion échouée', [
                    'email' => $email,
                    'client_exists' => $clientExists ? 'yes' : 'no',
                    'reason' => $clientExists ? 'password_mismatch' : 'client_not_found'
                ]);
                return $this->sendError('Identifiants invalides', 401);
            }

            // Vérifier le statut du client
            if ($client['status'] !== 'active') {
                Logger::channel('api')->warning('[ClientAuth] Tentative de connexion avec compte inactif', ['client_id' => $client['id'], 'status' => $client['status']]);
                return $this->sendError('Compte client inactif ou suspendu', 403);
            }

            // Générer le token JWT
            $payload = [
                'sub' => $client['id'],
                'email' => $client['email'],
                'name' => $client['firstname'] . ' ' . $client['lastname'],
                'role' => 'client',
                'iat' => time(),
                'exp' => time() + (24 * 60 * 60) // 24 heures
            ];

            $token = JWT::generate($payload);
            $refreshToken = JWT::generate([
                'sub' => $client['id'],
                'type' => 'refresh'
            ], true);

            // Configuration des cookies sécurisés
            $isSecure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
            $domain = $_SERVER['HTTP_HOST'] ?? '';
            $path = '/';
            $httpOnly = true;
            $sameSite = 'Strict';

            // Définir les cookies
            setcookie('client_token', $token, [
                'expires' => time() + 86400, // 24 heures
                'path' => $path,
                'domain' => $domain,
                'secure' => $isSecure,
                'httponly' => $httpOnly,
                'samesite' => $sameSite
            ]);

            setcookie('client_refresh_token', $refreshToken, [
                'expires' => time() + (7 * 86400), // 7 jours
                'path' => $path,
                'domain' => $domain,
                'secure' => $isSecure,
                'httponly' => $httpOnly,
                'samesite' => $sameSite
            ]);

            // Créer un token API pour le client
            $apiTokenModel = new ApiTokenModel();
            $apiToken = $apiTokenModel->createToken(
                $client['id'],
                'Client Session Token',
                ['client'],
                date('Y-m-d H:i:s', time() + (24 * 60 * 60))
            );

            Logger::channel('api')->info('[ClientAuth] Connexion client réussie', ['client_id' => $client['id']]);

            return $this->sendResponse([
                'user' => [
                    'id' => $client['id'],
                    'email' => $client['email'],
                    'firstName' => $client['firstname'],
                    'lastName' => $client['lastname'],
                    'company' => $client['company'],
                    'role' => 'client'
                ],
                'success' => true,
                'message' => 'Connexion réussie'
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientAuth] Erreur lors de la connexion client', ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors de la connexion', 500);
        }
    }

    /**
     * Inscription client
     */
    public function register() {
        $data = json_decode(file_get_contents('php://input'), true);

        Logger::channel('api')->info('[ClientAuth] Données d\'inscription reçues', [
            'data_received' => $data ? 'yes' : 'no',
            'fields_present' => $data ? array_keys($data) : []
        ]);

        // Validation des champs requis
        $requiredFields = ['firstname', 'lastname', 'email', 'phone', 'address', 'postal_code', 'city', 'country', 'password'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                Logger::channel('api')->warning('[ClientAuth] Champ requis manquant', ['field' => $field]);
                return $this->sendError("Le champ {$field} est requis", 400);
            }
        }

        // Validation de l'email
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return $this->sendError('Format d\'email invalide', 400);
        }

        // Validation du mot de passe
        if (strlen($data['password']) < 8) {
            return $this->sendError('Le mot de passe doit contenir au moins 8 caractères', 400);
        }

        try {
            // Vérifier si l'email existe déjà
            $existingClient = $this->clientModel->findByEmail($data['email']);
            Logger::channel('api')->info('[ClientAuth] Vérification email existant', [
                'email' => $data['email'],
                'exists' => $existingClient ? 'yes' : 'no'
            ]);

            if ($existingClient) {
                return $this->sendError('Cette adresse email est déjà utilisée', 409);
            }

            // Préparer les données du client
            $clientData = [
                'firstname' => trim($data['firstname']),
                'lastname' => trim($data['lastname']),
                'email' => strtolower(trim($data['email'])),
                'password' => $data['password'], // Le modèle se charge du hachage
                'company' => isset($data['company']) ? trim($data['company']) : null,
                'phone' => trim($data['phone']),
                'address' => trim($data['address']),
                'postal_code' => trim($data['postal_code']),
                'city' => trim($data['city']),
                'country' => trim($data['country']),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            Logger::channel('api')->info('[ClientAuth] Données client préparées', [
                'email' => $clientData['email'],
                'firstname' => $clientData['firstname'],
                'lastname' => $clientData['lastname'],
                'company' => $clientData['company'],
                'phone' => $clientData['phone'],
                'address' => $clientData['address'],
                'postal_code' => $clientData['postal_code'],
                'city' => $clientData['city'],
                'country' => $clientData['country']
            ]);

            // Créer le client
            $clientId = $this->clientModel->create($clientData);

            Logger::channel('api')->info('[ClientAuth] Résultat création client', [
                'client_id' => $clientId,
                'success' => $clientId ? 'yes' : 'no'
            ]);

            if (!$clientId) {
                return $this->sendError('Erreur lors de la création du compte', 500);
            }

            Logger::channel('api')->info('[ClientAuth] Nouveau client inscrit', ['client_id' => $clientId, 'email' => $data['email']]);

            return $this->sendResponse([
                'success' => true,
                'message' => 'Compte créé avec succès',
                'client_id' => $clientId
            ], 201);

        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientAuth] Erreur lors de l\'inscription client', ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors de la création du compte', 500);
        }
    }

    /**
     * Déconnexion client
     */
    public function logout() {
        try {
            // Configuration des cookies (cohérente avec WebsiteAuthController)
            $domain = $_SERVER['HTTP_HOST'] ?? '';
            $path = '/';
            $isSecure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

            // Supprimer les cookies client
            setcookie('client_token', '', [
                'expires' => time() - 3600,
                'path' => $path,
                'domain' => $domain,
                'secure' => $isSecure,
                'httponly' => true,
                'samesite' => 'Strict'
            ]);

            setcookie('client_refresh_token', '', [
                'expires' => time() - 3600,
                'path' => $path,
                'domain' => $domain,
                'secure' => $isSecure,
                'httponly' => true,
                'samesite' => 'Strict'
            ]);

            Logger::channel('api')->info('[ClientAuth] Déconnexion client');
            return $this->sendResponse(null, 200, 'Déconnexion réussie');

        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientAuth] Erreur lors de la déconnexion', ['error' => $e->getMessage()]);
            return $this->sendError('Erreur lors de la déconnexion', 500);
        }
    }

    /**
     * Vérification de l'authentification client
     */
    public function me() {
        // Récupérer le token client
        $token = $this->getClientToken();
        if (!$token) {
            // Ne pas logger d'erreur - normal sur pages publiques
            Logger::channel('api')->info('[ClientAuth] Aucun token client - utilisateur non connecté');
            return $this->sendError('Token client non fourni', 401);
        }

        try {
            Logger::channel('api')->info('[ClientAuth] Vérification token', ['token_length' => strlen($token)]);
            $payload = JWT::verify($token);
            Logger::channel('api')->info('[ClientAuth] Token vérifié', ['payload' => $payload]);

            // Vérifier que c'est bien un token client
            if (!isset($payload['role']) || $payload['role'] !== 'client') {
                Logger::channel('api')->warning('[ClientAuth] Token invalide - role incorrect', ['payload' => $payload]);
                return $this->sendError('Token invalide pour client', 401);
            }

            // Récupérer les données client
            $client = $this->clientModel->find($payload['sub']);
            if (!$client || $client['status'] !== 'active') {
                return $this->sendError('Client non trouvé ou inactif', 404);
            }

            return $this->sendResponse([
                'user' => [
                    'id' => $client['id'],
                    'email' => $client['email'],
                    'firstName' => $client['firstname'],
                    'lastName' => $client['lastname'],
                    'company' => $client['company'],
                    'role' => 'client'
                ]
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->warning('[ClientAuth] Token client invalide', ['error' => $e->getMessage()]);
            return $this->sendError('Token invalide', 401);
        }
    }

    /**
     * Récupère le token client depuis les cookies ou headers
     */
    private function getClientToken() {
        // Vérifier le cookie client_token
        if (isset($_COOKIE['client_token'])) {
            return $_COOKIE['client_token'];
        }

        // Vérifier l'en-tête Authorization
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (preg_match('/^Bearer\s+(.+)$/i', $authHeader, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Génère et renvoie un token Ably pour l'authentification temps réel du client
     *
     * @api {get} /api/v1/client/realtime/token Obtenir un token Ably client
     * @apiName GetClientRealtimeToken
     * @apiGroup ClientAuth
     * @apiVersion 1.0.0
     *
     * @apiSuccess {String} token Token d'authentification Ably pour le client
     */
    public function getRealtimeToken() {
        try {
            // Vérifier l'authentification client
            $clientId = $this->getAuthenticatedClientId();
            if (!$clientId) {
                Logger::channel('api')->warning('[ClientAuth] Tentative d\'accès au token Ably sans authentification');
                return $this->sendError('Authentification requise', 401);
            }

            // Vérifier que le client existe et est actif
            $client = $this->clientModel->find($clientId);
            if (!$client || $client['status'] !== 'active') {
                Logger::channel('api')->warning('[ClientAuth] Tentative d\'accès au token Ably avec compte inactif', [
                    'client_id' => $clientId,
                    'status' => $client['status'] ?? 'not_found'
                ]);
                return $this->sendError('Compte client inactif ou non trouvé', 403);
            }

            // Générer le token Ably
            $realtime = RealTime::getInstance();
            $token = $realtime->generateToken($clientId);

            if (!$token) {
                Logger::channel('api')->error('[ClientAuth] Échec de la génération du token Ably', ['client_id' => $clientId]);
                return $this->sendError('Erreur lors de la génération du token temps réel', 500);
            }

            Logger::channel('api')->info('[ClientAuth] Token Ably généré avec succès', ['client_id' => $clientId]);

            return $this->sendResponse([
                'token' => $token->token,
                'client_id' => $clientId
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientAuth] Erreur dans getRealtimeToken', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->sendError('Erreur lors de la génération du token temps réel', 500);
        }
    }

    /**
     * Récupère l'ID du client authentifié depuis la session ou le token API
     */
    private function getAuthenticatedClientId() {
        // Méthode 1: Vérifier le token API dans les headers
        $token = $this->getClientToken();
        if ($token) {
            try {
                $apiTokenModel = new ApiTokenModel();
                $tokenData = $apiTokenModel->validateToken($token);

                if ($tokenData && isset($tokenData['user_id'])) {
                    // Vérifier que c'est bien un token client (pas admin)
                    $permissions = json_decode($tokenData['permissions'] ?? '[]', true);
                    if (in_array('client', $permissions)) {
                        return $tokenData['user_id'];
                    }
                }
            } catch (\Exception $e) {
                Logger::channel('api')->debug('[ClientAuth] Erreur validation token API', ['error' => $e->getMessage()]);
            }
        }

        // Méthode 2: Vérifier la session client
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['client_id']) && !empty($_SESSION['client_id'])) {
            return $_SESSION['client_id'];
        }

        // Méthode 3: Vérifier le JWT dans les cookies
        if (isset($_COOKIE['client_token'])) {
            try {
                $payload = JWT::decode($_COOKIE['client_token']);
                if (isset($payload->sub) && isset($payload->role) && $payload->role === 'client') {
                    return $payload->sub;
                }
            } catch (\Exception $e) {
                Logger::channel('api')->debug('[ClientAuth] Erreur décodage JWT cookie', ['error' => $e->getMessage()]);
            }
        }

        return null;
    }
}
