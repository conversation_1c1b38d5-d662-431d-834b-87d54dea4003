<?php

namespace TechCMS\Api\V1\Controllers;

use TechCMS\Common\Core\Logger;

class ApiBaseController {
    public function __construct() {
        // Initialisation de base si nécessaire
    }

    /**
     * Prépare et envoie une réponse JSON
     *
     * @param mixed $data Les données à envoyer
     * @param int $status Le code de statut HTTP
     * @param string $message Un message optionnel
     * @return void
     */
    protected function sendResponse($data, $status = 200, $message = '') {
        header('Access-Control-Allow-Origin: https://cms.tech-cms.com');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-XSRF-TOKEN');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');
        header('Vary: Origin');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
        
        http_response_code($status);
        header('Content-Type: application/json; charset=utf-8');
        
        // Construire la réponse sans le wrapper data
        $response = $data;
        
        if (!empty($message)) {
            $response['message'] = $message;
        }
        
        echo json_encode($response);
        exit;
    }

    /**
     * Envoie une réponse d'erreur
     *
     * @param string $message Le message d'erreur
     * @param int $status Le code de statut HTTP
     * @param mixed $errors Des erreurs additionnelles
     * @return void
     */
    protected function sendError($message, $status = 400, $errors = null) {
        Logger::channel('api')->debug('Envoi des headers CORS pour une réponse d\'erreur.');
        
        // Headers CORS
        header('Access-Control-Allow-Origin: https://cms.tech-cms.com');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-XSRF-TOKEN');
        header('Access-Control-Allow-Credentials: true');
        header('Access-Control-Max-Age: 86400');
        header('Vary: Origin');
        
        Logger::channel('api')->debug('Headers CORS envoyés pour une réponse d\'erreur.');
        
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
        
        http_response_code($status);
        header('Content-Type: application/json; charset=utf-8');
        
        $response = [
            'error' => true,
            'message' => $message
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        // Log de la réponse d'erreur (401 = info, autres = error)
        if ($status === 401) {
            Logger::channel('api')->info('Réponse d\'authentification envoyée.', ['status' => $status, 'response' => $response]);
        } else {
            Logger::channel('api')->error('Réponse d\'erreur envoyée.', ['status' => $status, 'response' => $response]);
        }

        echo json_encode($response);
        exit;
    }

    protected function sendCorsHeaders() {
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');
    }
}
