<?php

namespace TechCMS\Api\V1\Controllers\website;

use TechCMS\Api\V1\Controllers\Website\WebsiteBaseController;
use TechCMS\Common\Models\ClientModel;
use TechCMS\Common\Core\Logger;
use TechCMS\Api\V1\Core\JWT;

/**
 * Contrôleur d'authentification pour le website/boutique
 */
class WebsiteAuthController extends WebsiteBaseController
{
    private $clientModel;

    public function __construct()
    {
        parent::__construct();
        $this->clientModel = new ClientModel();
    }

    /**
     * Connexion client
     */
    public function login()
    {
        try {
            $data = $this->getRequestData();
            
            // Validation des données
            if (empty($data['email']) || empty($data['password'])) {
                $this->sendError('Email et mot de passe requis', 400);
                return;
            }

            // Tentative de connexion
            $client = $this->clientModel->authenticate($data['email'], $data['password']);
            
            if (!$client) {
                Logger::channel('api')->warning('Tentative de connexion échouée', [
                    'email' => $data['email'],
                    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
                ]);
                
                $this->sendError('Email ou mot de passe incorrect', 401);
                return;
            }

            // Générer le token JWT
            $tokenPayload = [
                'user_id' => $client['id'],
                'email' => $client['email'],
                'type' => 'client',
                'iat' => time(),
                'exp' => time() + (24 * 60 * 60) // 24 heures
            ];
            
            $token = JWT::generate($tokenPayload);

            // Préparer les données utilisateur pour le frontend
            $userData = [
                'id' => (int)$client['id'],
                'email' => $client['email'],
                'first_name' => $client['firstname'],
                'last_name' => $client['lastname'],
                'company' => $client['company'],
                'phone' => $client['phone'],
                'address_line_1' => $client['address'],
                'city' => $client['city'],
                'postal_code' => $client['postal_code'],
                'country' => $client['country']
            ];

            Logger::channel('api')->info('Connexion client réussie', [
                'client_id' => $client['id'],
                'email' => $client['email']
            ]);

            $this->sendResponse([
                'success' => true,
                'user' => $userData,
                'token' => $token,
                'message' => 'Connexion réussie'
            ], 200);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la connexion', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError('Erreur lors de la connexion', 500);
        }
    }

    /**
     * Inscription client
     */
    public function register()
    {
        try {
            $data = $this->getRequestData();
            
            // Validation des données requises
            $required = ['email', 'password', 'firstname', 'lastname', 'phone', 'address', 'city', 'postal_code', 'country'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    $this->sendError("Le champ {$field} est requis", 400);
                    return;
                }
            }

            // Validation email
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $this->sendError('Email invalide', 400);
                return;
            }

            // Vérifier si l'email existe déjà
            if ($this->clientModel->emailExists($data['email'])) {
                $this->sendError('Un compte existe déjà avec cet email', 409);
                return;
            }

            // Validation mot de passe
            if (strlen($data['password']) < 6) {
                $this->sendError('Le mot de passe doit contenir au moins 6 caractères', 400);
                return;
            }

            // Préparer les données pour la création
            $clientData = [
                'email' => $data['email'],
                'password' => password_hash($data['password'], PASSWORD_DEFAULT),
                'firstname' => $data['firstname'],
                'lastname' => $data['lastname'],
                'company' => $data['company'] ?? '',
                'phone' => $data['phone'],
                'address' => $data['address'],
                'city' => $data['city'],
                'postal_code' => $data['postal_code'],
                'country' => $data['country'],
                'status' => 'active',
                'email_verified' => 0
            ];

            // Créer le client
            $clientId = $this->clientModel->createClient($clientData);
            
            if (!$clientId) {
                $this->sendError('Erreur lors de la création du compte', 500);
                return;
            }

            // Récupérer le client créé
            $client = $this->clientModel->getClientById($clientId);

            // Générer le token JWT
            $tokenPayload = [
                'user_id' => $clientId,
                'email' => $client['email'],
                'type' => 'client',
                'iat' => time(),
                'exp' => time() + (24 * 60 * 60) // 24 heures
            ];
            
            $token = JWT::encode($tokenPayload);

            // Préparer les données utilisateur pour le frontend
            $userData = [
                'id' => (int)$clientId,
                'email' => $client['email'],
                'first_name' => $client['firstname'],
                'last_name' => $client['lastname'],
                'company' => $client['company'],
                'phone' => $client['phone'],
                'address_line_1' => $client['address'],
                'city' => $client['city'],
                'postal_code' => $client['postal_code'],
                'country' => $client['country']
            ];

            Logger::channel('api')->info('Inscription client réussie', [
                'client_id' => $clientId,
                'email' => $client['email']
            ]);

            $this->sendResponse([
                'success' => true,
                'user' => $userData,
                'token' => $token,
                'message' => 'Compte créé avec succès'
            ], 200);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de l\'inscription', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->sendError('Erreur lors de la création du compte', 500);
        }
    }

    /**
     * Récupérer les informations de l'utilisateur connecté
     */
    public function me()
    {
        try {
            // Récupérer le token depuis l'header Authorization
            $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                $this->sendError('Token manquant', 401);
                return;
            }

            $token = $matches[1];
            $payload = JWT::verify($token);
            
            if (!$payload || $payload['type'] !== 'client') {
                $this->sendError('Token invalide', 401);
                return;
            }

            // Récupérer les informations du client
            $client = $this->clientModel->getClientById($payload['user_id']);
            
            if (!$client) {
                $this->sendError('Utilisateur non trouvé', 404);
                return;
            }

            $userData = [
                'id' => (int)$client['id'],
                'email' => $client['email'],
                'first_name' => $client['firstname'],
                'last_name' => $client['lastname'],
                'company' => $client['company'],
                'phone' => $client['phone'],
                'address_line_1' => $client['address'],
                'city' => $client['city'],
                'postal_code' => $client['postal_code'],
                'country' => $client['country']
            ];

            $this->sendResponse([
                'success' => true,
                'user' => $userData
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la récupération du profil', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la récupération du profil', 500);
        }
    }

    /**
     * Déconnexion
     */
    public function logout()
    {
        try {
            // Pour JWT, la déconnexion côté serveur n'est pas nécessaire
            // Le token sera supprimé côté client
            
            Logger::channel('api')->info('Déconnexion client');
            
            $this->sendSuccess([
                'message' => 'Déconnexion réussie'
            ]);

        } catch (\Exception $e) {
            Logger::channel('api')->error('Erreur lors de la déconnexion', [
                'error' => $e->getMessage()
            ]);
            $this->sendError('Erreur lors de la déconnexion', 500);
        }
    }

    /**
     * Récupère les données de la requête (JSON, POST ou GET)
     */
    private function getRequestData() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        
        // Fallback vers $_POST si pas de JSON
        if (!$data) {
            $data = $_POST;
        }
        
        // Fallback vers $_GET pour certaines requêtes
        if (!$data) {
            $data = $_GET;
        }
        
        return $data ?: [];
    }

    /**
     * Génère et renvoie un token Ably pour l'authentification temps réel (publique)
     */
    public function getRealtimeToken()
    {
        try {
            $realtime = \TechCMS\Common\Core\RealTime::getInstance();
            $token = $realtime->generateToken();

            return $this->sendResponse(['token' => $token->token]);
        } catch (\Exception $e) {
            Logger::channel('api')->error("[WebsiteAuth] Erreur dans getRealtimeToken", ['error' => $e->getMessage()]);
            return $this->sendError("Erreur lors de la génération du token", 500);
        }
    }
}
