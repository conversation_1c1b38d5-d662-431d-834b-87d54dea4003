<?php

namespace TechCMS\Api\V1\Controllers\Admin\Client;

use TechCMS\Common\Models\ClientModel;
use TechCMS\Api\V1\Controllers\Admin\AdminBaseController;
use TechCMS\Common\Core\Request;
use TechCMS\Common\Core\Validator;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\StatsRealTime;

class AdminClientController extends AdminBaseController
{
    /**
     * Afficher la liste des clients
     */
    public function index()
    {
        $clientModel = new ClientModel();
        $clients = $clientModel->all();
        return $this->sendResponse(['data' => ['clients' => $clients]]);
    }

    /**
     * Créer un nouveau client
     */
    public function store(Request $request = null)
    {
        if ($request === null) {
            $request = new Request();
        }
        
        // Récupérer les données JSON du corps de la requête
        $data = $request->isJson() ? $request->getJsonBody() : $request->getPost();
        
        $validator = Validator::make($data, [
            'firstname' => 'required|string|max:255',
            'lastname' => 'required|string|max:255',
            'email' => 'required|email|unique:clients',
            'phone' => 'nullable|string|max:20',
            'status' => 'required|in:active,inactive,suspended'
        ]);

        if ($validator->fails()) {
            // Envoyer un toast d'erreur pour les erreurs de validation
            sendErrorToast(
                'Erreurs de validation lors de la création du client',
                'Validation échouée'
            );
            return $this->sendResponse(['errors' => $validator->errors()], 422);
        }

        // Si aucun mot de passe n'est fourni, générer un mot de passe aléatoire
        if (!isset($data['password']) || empty($data['password'])) {
            // Générer un mot de passe aléatoire si aucun n'est fourni
            $data['password'] = bin2hex(random_bytes(8));
        }
        // Note: Le hashage est fait dans ClientModel::create()

        $clientModel = new ClientModel();
        $client = $clientModel->create($data);
        
        // Supprimer le mot de passe de la réponse
        if (isset($client['password'])) {
            unset($client['password']);
        }
        
        // Mettre à jour les statistiques en temps réel
        try {
            $statsRealTime = StatsRealTime::getInstance();
            $statsRealTime->updateClientsCount();
            
            // Publier l'événement de création pour le tableau de bord
            $statsRealTime->publishClientUpdate($client, 'create');
        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientsController] Erreur lors de la mise à jour des statistiques temps réel', [
                'exception' => $e->getMessage()
            ]);
        }
        
        // Envoyer un toast de succès
        sendSuccessToast(
            "Le client {$data['firstname']} {$data['lastname']} a été créé avec succès",
            "Client ajouté"
        );
        
        return $this->sendResponse(['data' => $client, 'message' => 'Client créé avec succès'], 201);
    }

    /**
     * Afficher les détails d'un client
     */
    public function show($id)
    {
        $clientModel = new ClientModel();
        $client = $clientModel->find($id);
        
        if (!$client) {
            // Envoyer un toast d'erreur si le client n'est pas trouvé
            sendErrorToast(
                "Impossible de trouver le client avec l'ID $id",
                "Client introuvable"
            );
            return $this->sendError('Client non trouvé', 404);
        }
        
        // Supprimer le mot de passe de la réponse
        if (isset($client['password'])) {
            unset($client['password']);
        }
        
        return $this->sendResponse(['data' => $client]);
    }

    /**
     * Mettre à jour un client existant
     */
    public function update($id, Request $request = null)
    {
        if ($request === null) {
            $request = new Request();
        }
        
        if (!$id) {
            // Envoyer un toast d'erreur si l'ID n'est pas fourni
            sendErrorToast(
                "L'ID du client n'a pas été fourni",
                "Erreur de requête"
            );
            return $this->sendError('ID du client non fourni', 400);
        }
        
        // S'assurer que l'ID est une chaîne de caractères et non un tableau
        if (is_array($id)) {
            $id = $id[0] ?? null;
        }
        
        if (!$id) {
            // Envoyer un toast d'erreur si l'ID est invalide
            sendErrorToast(
                "L'ID du client est invalide",
                "Erreur de requête"
            );
            return $this->sendError('ID du client invalide', 400);
        }
        
        // Récupérer les données JSON du corps de la requête
        $data = $request->isJson() ? $request->getJsonBody() : $request->getPost();
        
        $validator = Validator::make($data, [
            'firstname' => 'string|max:255',
            'lastname' => 'string|max:255',
            'email' => 'email|unique:clients,email,' . $id, // Format: unique:table,colonne,id_exclusion
            'phone' => 'nullable|string|max:20',
            'status' => 'in:active,inactive,suspended'
        ]);

        if ($validator->fails()) {
            Logger::channel('api')->warning('[ClientsController] Erreurs de validation lors de la mise à jour', ['id' => $id, 'errors' => $validator->errors()]);
            // Envoyer un toast d'erreur pour les erreurs de validation
            sendErrorToast(
                'Erreurs de validation lors de la mise à jour du client',
                'Validation échouée'
            );
            return $this->sendResponse(['errors' => $validator->errors()], 422);
        }

        // Si le mot de passe est vide, ne pas le modifier
        if (!isset($data['password']) || empty($data['password'])) {
            unset($data['password']);
        }
        // Note: Le hashage est fait dans ClientModel::update()

        $clientModel = new ClientModel();
        $client = $clientModel->find($id);
        
        if (!$client) {
            // Envoyer un toast d'erreur si le client n'est pas trouvé
            sendErrorToast(
                "Impossible de trouver le client avec l'ID $id",
                "Client introuvable"
            );
            return $this->sendError('Client non trouvé', 404);
        }
        
        // La méthode update() retourne un booléen, pas les données du client
        $updateResult = $clientModel->update($id, $data);
        
        if (!$updateResult) {
            Logger::channel('api')->error('[ClientsController] Échec de la mise à jour du client', [
                'client_id' => $id
            ]);
            return $this->sendError('Erreur lors de la mise à jour du client', 500);
        }
        
        // Récupérer explicitement les données complètes et à jour du client
        $client = $clientModel->find($id);
        
        // Supprimer le mot de passe de la réponse
        if (isset($client['password'])) {
            unset($client['password']);
        }
        
        // Mettre à jour les statistiques en temps réel si le statut a été modifié
        try {
            $statsRealTime = StatsRealTime::getInstance();
            
            // Mettre à jour les compteurs si le statut a changé
            if (isset($data['status'])) {
                $statsRealTime->updateClientsCount();
            }
            
            // Publier l'événement de mise à jour pour le tableau de bord
            $statsRealTime->publishClientUpdate($client, 'update');
        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientsController] Erreur lors de la mise à jour des statistiques temps réel', [
                'exception' => $e->getMessage()
            ]);
        }
        
        // Envoyer un toast de succès
        sendSuccessToast(
            "Le client " . ($data['firstname'] ?? $client['firstname']) . " " . ($data['lastname'] ?? $client['lastname']) . " a été mis à jour avec succès",
            "Client mis à jour"
        );
        
        return $this->sendResponse(['data' => $client, 'message' => 'Client mis à jour avec succès']);
    }

    /**
     * Supprimer un client
     */
    public function destroy($id, Request $request = null)
    {
        if ($request === null) {
            $request = new Request();
        }
        
        if (!$id) {
            // Envoyer un toast d'erreur si l'ID n'est pas fourni
            sendErrorToast(
                "L'ID du client n'a pas été fourni",
                "Erreur de requête"
            );
            return $this->sendError('ID du client non fourni', 400);
        }
        
        // S'assurer que l'ID est une chaîne de caractères et non un tableau
        if (is_array($id)) {
            $id = $id[0] ?? null;
        }
        
        if (!$id) {
            // Envoyer un toast d'erreur si l'ID est invalide
            sendErrorToast(
                "L'ID du client est invalide",
                "Erreur de requête"
            );
            return $this->sendError('ID du client invalide', 400);
        }
        
        $clientModel = new ClientModel();
        $client = $clientModel->find($id);
        
        if (!$client) {
            // Envoyer un toast d'erreur si le client n'est pas trouvé
            sendErrorToast(
                "Impossible de trouver le client avec l'ID $id",
                "Client introuvable"
            );
            return $this->sendError('Client non trouvé', 404);
        }
        
        // Conserver le nom du client pour le message
        $clientName = $client['firstname'] . ' ' . $client['lastname'];
        
        // Vérifier si le client a des services actifs
        $clientServices = $clientModel->getServices($id);
        
        $activeServices = array_filter($clientServices, function($service) {
            return $service['status'] === 'active' || $service['status'] === 'pending';
        });
        
        if (!empty($activeServices)) {
            // Envoyer un toast d'erreur si le client a des services actifs
            Logger::channel('api')->warning("Tentative de suppression d'un client avec services actifs", [
                'client_id' => $id,
                'client_name' => $clientName,
                'active_services_count' => count($activeServices)
            ]);
            
            sendErrorToast(
                "Le client $clientName possède des services actifs. Veuillez d'abord annuler ou transférer ses services.",
                "Suppression impossible"
            );
            return $this->sendError('Client avec services actifs', 400);
        }
        
        $clientModel->delete($id);
        
        // Mettre à jour les statistiques en temps réel
        try {
            $statsRealTime = StatsRealTime::getInstance();
            
            // Conserver une copie des données client pour l'événement temps réel avant suppression
            $clientData = $client;
            
            // Mettre à jour les compteurs
            $statsRealTime->updateClientsCount();
            
            // Publier l'événement de suppression pour le tableau de bord
            $statsRealTime->publishClientUpdate($clientData, 'delete');
            
        } catch (\Exception $e) {
            Logger::channel('api')->error('[ClientsController] Erreur lors de la mise à jour des statistiques temps réel après suppression', [
                'exception' => $e->getMessage()
            ]);
        }
        
        // Envoyer un toast de succès
        sendSuccessToast(
            "Le client $clientName a été supprimé avec succès",
            "Client supprimé"
        );
        
        return $this->sendResponse(['message' => 'Client supprimé avec succès']);
    }
}
