import{a,r as e,o as i,b as s,e as t,f as l,h as n,g as r,t as c,n as d,p as o,F as u,i as v,j as m,C as p,k as f}from"./vendor-De2bYOnT.js";import{u as y,a as I,A as b,l as h,_ as E}from"./index-OgqJxbPd.js";import"./utils-CG2kLDjF.js";const g={id:"invoice-detail"},D={class:"invoice-detail-header"},w={class:"header-content"},_={class:"invoice-title-section"},k={class:"invoice-info"},C={class:"invoice-meta"},N={class:"invoice-date"},T={class:"invoice-actions"},L=["disabled"],V={key:0,class:"loading-container"},A={key:1,class:"error-container"},O={class:"error-message"},F={key:2,class:"invoice-content"},R={class:"invoice-summary"},P={class:"summary-card"},j={class:"summary-grid"},S={class:"summary-item"},H={class:"invoice-number"},q={class:"summary-item"},$={class:"summary-item"},U={class:"summary-item"},x={class:"summary-item"},K={class:"total-amount"},z={key:0,class:"summary-item"},B={class:"invoice-items"},M={class:"items-card"},Q={class:"items-table"},G={class:"table-body"},J={class:"col-description"},W={class:"item-name"},X={key:0,class:"item-details"},Y={class:"col-period"},Z={class:"col-quantity"},aa={class:"col-price"},ea={class:"col-total"},ia={class:"table-footer"},sa={class:"footer-row"},ta={class:"footer-value"},la={class:"footer-row total-row"},na={class:"footer-value"},ra={key:0,class:"payment-history"},ca={class:"history-card"},da={class:"history-list"},oa={class:"payment-icon"},ua={class:"payment-details"},va={class:"payment-amount"},ma={class:"payment-method"},pa={class:"payment-date"},fa={class:"payment-status"},ya=E(a({__name:"InvoiceDetailView",setup(a){const E=p(),ya=m(),Ia=y(),ba=I(),ha=e(!0),Ea=e(null),ga=e(null),Da=e([]),wa=e([]),_a=e(!1),ka=()=>{ya.push("/billing")},Ca=async()=>{var a,e,i;try{ha.value=!0,Ea.value=null;const e=parseInt(E.params.id);if(!e)throw new Error("ID de facture invalide");const i=await b.routes.client.invoice.getById(e);ga.value=i.data,Da.value=[],wa.value=[],h.info("[INVOICE] Facture chargée avec succès",{invoiceId:null==(a=ga.value)?void 0:a.id})}catch(s){h.error("[INVOICE] Erreur lors du chargement de la facture",{error:s,invoiceId:E.params.id}),Ea.value=(null==(i=null==(e=s.response)?void 0:e.data)?void 0:i.message)||"Erreur lors du chargement de la facture"}finally{ha.value=!1}},Na=async()=>{var a,e;try{const e=parseInt(E.params.id),i=await b.routes.client.invoice.downloadPdf(e),s=window.URL.createObjectURL(new Blob([i.data])),t=document.createElement("a");t.href=s,t.setAttribute("download",`facture-${null==(a=ga.value)?void 0:a.number}.pdf`),document.body.appendChild(t),t.click(),t.remove(),window.URL.revokeObjectURL(s)}catch(i){h.error("[INVOICE] Erreur lors du téléchargement",{error:i,invoiceId:null==(e=ga.value)?void 0:e.id})}},Ta=()=>{var a;h.info("[INVOICE] Paiement facture demandé",{invoiceId:null==(a=ga.value)?void 0:a.id})},La=a=>({paid:"status-paid",unpaid:"status-unpaid",cancelled:"status-cancelled",overdue:"status-overdue"}[a||""]||"status-unknown"),Va=a=>({paid:"Payée",unpaid:"Impayée",cancelled:"Annulée",overdue:"En retard"}[a||""]||"Inconnu"),Aa=a=>({credit_card:"Carte de crédit",bank_transfer:"Virement bancaire",paypal:"PayPal",stripe:"Stripe"}[a]||a),Oa=a=>({completed:"Terminé",pending:"En attente",failed:"Échoué"}[a]||a),Fa=a=>a?new Date(a).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"}):"N/A",Ra=a=>a?new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(a):"0,00 €",Pa=(a,e)=>{if(!a||!e)return"N/A";return`${new Date(a).toLocaleDateString("fr-FR",{month:"short",year:"numeric"})} - ${new Date(e).toLocaleDateString("fr-FR",{month:"short",year:"numeric"})}`},ja=a=>{var e,i;const s=parseInt(E.params.id),t=parseInt(String((null==(i=null==(e=a.data)?void 0:e.invoice)?void 0:i.id)||"0"));if("invoice"===a.entity_type&&t===s){switch(h.info("[INVOICE DETAIL] Événement facture reçu",{event:a,invoiceId:s,action:a.action}),_a.value=!0,a.data.invoice&&(ga.value={...ga.value,...a.data.invoice},h.info("[INVOICE DETAIL] Facture mise à jour en temps réel",{invoiceId:s,updatedInvoice:ga.value})),a.action){case"invoice_delete":h.warn("[INVOICE DETAIL] Facture supprimée - redirection",{invoiceId:s}),ya.push("/billing");break;case"invoice_update":h.info("[INVOICE DETAIL] Facture mise à jour",{invoiceId:s});break;case"invoice_paid":h.info("[INVOICE DETAIL] Facture marquée comme payée",{invoiceId:s})}setTimeout(()=>{_a.value=!1},1e3)}};return i(async()=>{await Ca(),await(async()=>{var a;const e=null==(a=ba.user)?void 0:a.id,i=parseInt(E.params.id);if(!e)return void h.error("[INVOICE DETAIL] ID client manquant - abandon initialisation temps réel",{user:ba.user});if(!i)return void h.error("[INVOICE DETAIL] ID facture invalide - abandon initialisation temps réel",{invoiceId:E.params.id});const s=`invoice-detail-${i}`;if(Ia.initialized)await Ia.subscribeToDashboardEvents(e),Ia.registerDashboardHandler(s,ja),h.info("[INVOICE DETAIL] Handler temps réel enregistré",{handlerKey:s,clientId:e,invoiceId:i});else{const a=Ia.$subscribe((t,l)=>{l.initialized&&(Ia.subscribeToDashboardEvents(e).then(()=>{Ia.registerDashboardHandler(s,ja),h.info("[INVOICE DETAIL] Handler temps réel enregistré (après initialisation)",{handlerKey:s,clientId:e,invoiceId:i})}),a())})}})()}),s(()=>{(()=>{const a=parseInt(E.params.id);if(a){const e=`invoice-detail-${a}`;Ia.unregisterDashboardHandler(e),h.info("[INVOICE DETAIL] Handler temps réel supprimé",{handlerKey:e,invoiceId:a})}})()}),(a,e)=>{var i,s,m,p,y;return f(),t("div",g,[l("div",D,[l("div",w,[l("button",{class:"btn btn-outline btn-sm back-btn",onClick:ka},e[0]||(e[0]=[l("i",{class:"fas fa-arrow-left"},null,-1),r(" Retour à la facturation ")])),l("div",_,[e[1]||(e[1]=l("div",{class:"invoice-icon-large"},[l("i",{class:"fas fa-file-invoice"})],-1)),l("div",k,[l("h1",null,"Facture #"+c((null==(i=ga.value)?void 0:i.number)||"Chargement..."),1),l("div",C,[l("span",N,c(Fa(null==(s=ga.value)?void 0:s.created_at)),1),l("span",{class:d(La(null==(m=ga.value)?void 0:m.status))},c(Va(null==(p=ga.value)?void 0:p.status)),3)])])])]),l("div",T,[l("button",{class:"btn btn-outline btn-sm",onClick:Na,disabled:!ga.value},e[2]||(e[2]=[l("i",{class:"fas fa-download"},null,-1),r(" Télécharger PDF ")]),8,L),"unpaid"===(null==(y=ga.value)?void 0:y.status)?(f(),t("button",{key:0,class:"btn btn-primary",onClick:Ta},e[3]||(e[3]=[l("i",{class:"fas fa-credit-card"},null,-1),r(" Payer maintenant ")]))):n("",!0)])]),ha.value?(f(),t("div",V,e[4]||(e[4]=[l("div",{class:"loading-spinner"},[l("i",{class:"fas fa-spinner fa-spin"}),l("p",null,"Chargement des détails de la facture...")],-1)]))):Ea.value?(f(),t("div",A,[l("div",O,[e[6]||(e[6]=l("i",{class:"fas fa-exclamation-triangle"},null,-1)),e[7]||(e[7]=l("h3",null,"Erreur de chargement",-1)),l("p",null,c(Ea.value),1),l("button",{class:"btn btn-primary",onClick:Ca},e[5]||(e[5]=[l("i",{class:"fas fa-refresh"},null,-1),r(" Réessayer ")]))])])):ga.value?(f(),t("div",F,[l("div",R,[l("div",P,[e[14]||(e[14]=l("h2",null,[l("i",{class:"fas fa-info-circle"}),r(" Résumé de la facture ")],-1)),l("div",j,[l("div",S,[e[8]||(e[8]=l("label",null,"Numéro de facture",-1)),l("span",H,"#"+c(ga.value.number),1)]),l("div",q,[e[9]||(e[9]=l("label",null,"Date d'émission",-1)),l("span",null,c(Fa(ga.value.created_at)),1)]),l("div",$,[e[10]||(e[10]=l("label",null,"Date d'échéance",-1)),l("span",null,c(Fa(ga.value.due_date)),1)]),l("div",U,[e[11]||(e[11]=l("label",null,"Statut",-1)),l("span",{class:d(La(ga.value.status))},c(Va(ga.value.status)),3)]),l("div",x,[e[12]||(e[12]=l("label",null,"Montant total",-1)),l("span",K,c(Ra(ga.value.amount)),1)]),ga.value.paid_at?(f(),t("div",z,[e[13]||(e[13]=l("label",null,"Date de paiement",-1)),l("span",null,c(Fa(ga.value.paid_at)),1)])):n("",!0)])])]),l("div",B,[l("div",M,[e[19]||(e[19]=l("h2",null,[l("i",{class:"fas fa-list"}),r(" Détail des services ")],-1)),l("div",Q,[e[18]||(e[18]=o('<div class="table-header" data-v-3705118e><div class="col-description" data-v-3705118e>Description</div><div class="col-period" data-v-3705118e>Période</div><div class="col-quantity" data-v-3705118e>Quantité</div><div class="col-price" data-v-3705118e>Prix unitaire</div><div class="col-total" data-v-3705118e>Total</div></div>',1)),l("div",G,[(f(!0),t(u,null,v(Da.value,a=>(f(),t("div",{key:a.id,class:"table-row"},[l("div",J,[l("div",W,c(a.description),1),a.service_name?(f(),t("div",X," Service: "+c(a.service_name),1)):n("",!0)]),l("div",Y,c(Pa(a.period_start,a.period_end)),1),l("div",Z,c(a.quantity||1),1),l("div",aa,c(Ra(a.unit_price)),1),l("div",ea,c(Ra(a.total_price)),1)]))),128))]),l("div",ia,[l("div",sa,[e[15]||(e[15]=l("div",{class:"footer-label"},"Sous-total",-1)),l("div",ta,c(Ra(ga.value.amount)),1)]),e[17]||(e[17]=l("div",{class:"footer-row",style:{display:"none"}},[l("div",{class:"footer-label"},"TVA"),l("div",{class:"footer-value"},"0,00 €")],-1)),l("div",la,[e[16]||(e[16]=l("div",{class:"footer-label"},"Total",-1)),l("div",na,c(Ra(ga.value.amount)),1)])])])])]),wa.value.length>0?(f(),t("div",ra,[l("div",ca,[e[20]||(e[20]=l("h2",null,[l("i",{class:"fas fa-history"}),r(" Historique des paiements ")],-1)),l("div",da,[(f(!0),t(u,null,v(wa.value,a=>{return f(),t("div",{key:a.id,class:"history-item"},[l("div",oa,[l("i",{class:d((i=a.method,{credit_card:"fas fa-credit-card",bank_transfer:"fas fa-university",paypal:"fab fa-paypal",stripe:"fab fa-stripe"}[i]||"fas fa-money-bill"))},null,2)]),l("div",ua,[l("div",va,c(Ra(a.amount)),1),l("div",ma,c(Aa(a.method)),1),l("div",pa,c(Fa(a.created_at)),1)]),l("div",fa,[l("span",{class:d((e=a.status,{completed:"payment-completed",pending:"payment-pending",failed:"payment-failed"}[e]||"payment-unknown"))},c(Oa(a.status)),3)])]);var e,i}),128))])])])):n("",!0)])):n("",!0)])}}}),[["__scopeId","data-v-3705118e"]]);export{ya as default};
