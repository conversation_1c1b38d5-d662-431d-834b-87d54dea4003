const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/InvoiceDetailView-C9CSwHMy.js","assets/vendor-De2bYOnT.js","assets/utils-CG2kLDjF.js","assets/InvoiceDetailView-oG9dMU0-.css","assets/TicketDetailView-QDe1ZZQR.js","assets/TicketDetailView-GH8e5TaN.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty;import{a as t,c as s}from"./utils-CG2kLDjF.js";import{r as n,d as i,c as a,a as r,o,b as l,e as c,f as u,t as d,u as h,g as p,h as f,F as g,i as v,j as m,k as y,w as b,n as w,l as k,v as _,m as C,p as E,q as R,s as T,x as S,y as O,z as A,A as I,B as M,C as L,D as P,E as N,G as U,T as D,H as x,I as B,J as q,K as V,L as H,M as G}from"./vendor-De2bYOnT.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const j=t.create({baseURL:"",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:3e4});j.defaults.withCredentials=!0;const $={auth:{login:(e,t,s=!1)=>j.post("/api/v1/client/auth/login",{email:e,password:t,remember:s}),register:e=>j.post("/api/v1/client/auth/register",e),logout:()=>j.post("/api/v1/client/auth/logout"),me:()=>j.get("/api/v1/client/auth/me"),forgotPassword:e=>j.post("/api/v1/client/auth/forgot-password",{email:e}),resetPassword:(e,t,s)=>j.post("/api/v1/client/auth/reset-password",{token:e,password:t,password_confirmation:s})},client:{dashboard:{getStats:()=>j.get("/api/v1/client/dashboard/stats"),getOverview:()=>j.get("/api/v1/client/dashboard/overview")},service:{list:e=>j.get("/api/v1/client/services",{params:e}),get:e=>j.get(`/api/v1/client/services/${e}`),getById:e=>j.get(`/api/v1/client/services/${e}`),getHistory:e=>j.get(`/api/v1/client/services/${e}/history`),getUsage:e=>j.get(`/api/v1/client/services/${e}/usage`),getRecent:()=>j.get("/api/v1/client/services/recent")},invoice:{list:e=>j.get("/api/v1/client/invoices",{params:e}),get:e=>j.get(`/api/v1/client/invoices/${e}`),getById:e=>j.get(`/api/v1/client/invoices/${e}`),downloadPdf:e=>j.get(`/api/v1/client/invoices/${e}/pdf`,{responseType:"blob"}),getRecent:()=>j.get("/api/v1/client/invoices/recent"),getUnpaid:()=>j.get("/api/v1/client/invoices/unpaid")},ticket:{list:e=>j.get("/api/v1/client/tickets",{params:e}),get:e=>j.get(`/api/v1/client/tickets/${e}`),getById:e=>j.get(`/api/v1/client/tickets/${e}`),getMessages:(e,t)=>j.get(`/api/v1/client/tickets/${e}/messages`,{params:t}),create:e=>j.post("/api/v1/client/tickets",e),addReply:(e,t)=>j.post(`/api/v1/client/tickets/${e}/messages`,t),close:e=>j.put(`/api/v1/client/tickets/${e}/close`),reopen:e=>j.put(`/api/v1/client/tickets/${e}/reopen`),getRecent:()=>j.get("/api/v1/client/tickets/recent"),getOpen:()=>j.get("/api/v1/client/tickets/open")},license:{list:()=>j.get("/api/v1/client/licenses"),get:e=>j.get(`/api/v1/client/licenses/${e}`),verify:(e,t)=>j.post("/api/v1/license/verify",{license_key:e,domain:t}),getStatus:e=>j.post("/api/v1/license/status",{license_key:e})},shop:{getTemplates:e=>j.get("/api/v1/website/templates",{params:e}),getTemplate:e=>j.get(`/api/v1/website/templates/${e}`),createOrder:e=>j.post("/api/v1/client/orders",e),getOrders:e=>j.get("/api/v1/client/orders",{params:e}),getOrder:e=>j.get(`/api/v1/client/orders/${e}`)},updates:{getVersions:e=>j.get("/api/v1/client/updates/versions",{params:e}),getLicenseInfo:()=>j.get("/api/v1/client/updates/license-info"),getStats:()=>j.get("/api/v1/client/updates/stats"),check:()=>j.post("/api/v1/client/updates/check"),downloadToken:e=>j.post("/api/v1/client/updates/download-token",e)},profile:{get:()=>j.get("/api/v1/client/profile"),update:e=>j.put("/api/v1/client/profile",e),changePassword:e=>j.post("/api/v1/client/profile/password",e)},getRealtimeToken:()=>j.get("/api/v1/client/realtime/token"),department:{list:()=>j.get("/api/v1/client/departments")},logging:{log:(e,t,s)=>j.post("/api/v1/client/log",{level:e,message:t,context:s})}}};class F{static get client(){return j}static setAuthToken(e){e?j.defaults.headers.common.Authorization=`Bearer ${e}`:delete j.defaults.headers.common.Authorization}static setupInterceptors(){j.interceptors.response.use(e=>e,e=>{var t;return 401===(null==(t=e.response)?void 0:t.status)&&(window.location.href="/client/login"),Promise.reject(e)})}}var W;async function z(e,t,s={}){try{await F.routes.client.logging.log(e,t,{...s,url:window.location.href})}catch(n){}}((t,s,n)=>{s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[s]=n})(F,"symbol"!=typeof(W="routes")?W+"":W,$),F.setupInterceptors();const J={info:(e,t)=>{z("info",e,t)},debug:(e,t)=>{},warn:(e,t)=>{z("warning",e,t)},error:(e,t)=>{z("error",e,t)}},K=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,Y=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,Q=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function X(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t}function Z(e,t){if(null==e)return;let s=e;for(let n=0;n<t.length;n++){if(null==s||null==s[t[n]])return;s=s[t[n]]}return s}function ee(e,t,s){if(0===s.length)return t;const n=s[0];return s.length>1&&(t=ee("object"==typeof e&&null!==e&&Object.prototype.hasOwnProperty.call(e,n)?e[n]:Number.isInteger(Number(s[1]))?[]:{},t,Array.prototype.slice.call(s,1))),Number.isInteger(Number(n))&&Array.isArray(e)?e.slice()[n]:Object.assign({},e,{[n]:t})}function te(e,t){if(null==e||0===t.length)return e;if(1===t.length){if(null==e)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const s={};for(const t in e)s[t]=e[t];return delete s[t[0]],s}if(null==e[t[0]]){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const s={};for(const t in e)s[t]=e[t];return s}return ee(e,te(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function se(e,t){return t.map(e=>e.split(".")).map(t=>[t,Z(e,t)]).filter(e=>void 0!==e[1]).reduce((e,t)=>ee(e,t[1],t[0]),{})}function ne(e,t){return t.map(e=>e.split(".")).reduce((e,t)=>te(e,t),e)}function ie(e,{storage:t,serializer:s,key:n,debug:i,pick:a,omit:r,beforeHydrate:o,afterHydrate:l},c,u=!0){try{u&&(null==o||o(c));const i=t.getItem(n);if(i){const t=s.deserialize(i),n=a?se(t,a):t,o=r?ne(n,r):n;e.$patch(o)}u&&(null==l||l(c))}catch(d){}}function ae(e,{storage:t,serializer:s,key:n,debug:i,pick:a,omit:r}){try{const i=a?se(e,a):e,o=r?ne(i,r):i,l=s.serialize(o);t.setItem(n,l)}catch(o){}}var re=function(e={}){return function(t){!function(e,t,s){const{pinia:n,store:i,options:{persist:a=s}}=e;if(!a)return;if(!(i.$id in n.state.value)){const e=n._s.get(i.$id.replace("__hot:",""));return void(e&&Promise.resolve().then(()=>e.$persist()))}const r=(Array.isArray(a)?a:!0===a?[{}]:[a]).map(t);i.$hydrate=({runHooks:t=!0}={})=>{r.forEach(s=>{ie(i,s,e,t)})},i.$persist=()=>{r.forEach(e=>{ae(i.$state,e)})},r.forEach(t=>{ie(i,t,e),i.$subscribe((e,s)=>ae(s,t),{detached:!0})})}(t,s=>({key:(e.key?e.key:e=>e)(s.key??t.store.$id),debug:s.debug??e.debug??!1,serializer:s.serializer??e.serializer??{serialize:e=>JSON.stringify(e),deserialize:e=>function(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const s=e.trim();if(s.length<=9)switch(s.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!Q.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(K.test(e)||Y.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,X)}return JSON.parse(e)}catch(n){if(t.strict)throw n;return e}}(e)},storage:s.storage??e.storage??window.localStorage,beforeHydrate:s.beforeHydrate,afterHydrate:s.afterHydrate,pick:s.pick,omit:s.omit}),e.auto??!1)}}();const oe={},le=function(e,t,s){let n=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),i=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));n=e(t.map(e=>{if((e=function(e){return"/client/dist/"+e}(e))in oe)return;oe[e]=!0;const t=e.endsWith(".css"),s=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${s}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script"),n.crossOrigin="",n.href=e,i&&n.setAttribute("nonce",i),document.head.appendChild(n),t?new Promise((t,s)=>{n.addEventListener("load",t),n.addEventListener("error",()=>s(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then(t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)})};class ce{static async getStats(){var e,t;try{return(await F.routes.client.dashboard.getStats()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des statistiques")}}static async getOverview(){var e,t;try{return(await F.routes.client.dashboard.getOverview()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération de la vue d'ensemble")}}static async getRecentServices(){var e,t;try{return(await F.routes.client.service.getRecent()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des services récents")}}static async getRecentInvoices(){var e,t;try{return(await F.routes.client.invoice.getRecent()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des factures récentes")}}static async getUnpaidInvoices(){var e,t;try{return(await F.routes.client.invoice.getUnpaid()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des factures impayées")}}static async getRecentTickets(){var e,t;try{return(await F.routes.client.ticket.getRecent()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des tickets récents")}}static async getOpenTickets(){var e,t;try{return(await F.routes.client.ticket.getOpen()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des tickets ouverts")}}}var ue,de={exports:{}};
/*@license Copyright 2015-2022 Ably Real-time Ltd (ably.com)

Ably JavaScript Library v2.9.0
https://github.com/ably/ably-js

Released under the Apache Licence v2.0*/var he,pe=ue?de.exports:(ue=1,he=()=>{var e,t={},n={exports:t},i=Object.defineProperty,a=Object.defineProperties,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,h=(e,t,s)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,p=(e,t)=>{for(var s in t||(t={}))u.call(t,s)&&h(e,s,t[s]);if(c)for(var s of c(t))d.call(t,s)&&h(e,s,t[s]);return e},f=(e,t)=>a(e,o(t)),g=(e,t)=>{for(var s in t)i(e,s,{get:t[s],enumerable:!0})},v={};g(v,{ErrorInfo:()=>R,Realtime:()=>sn,Rest:()=>Rs,default:()=>ei,makeProtocolMessageFromDeserialized:()=>hs,msgpack:()=>Qn}),n.exports=(e=v,((e,t,s,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of l(t))u.call(e,a)||a===s||i(e,a,{get:()=>t[a],enumerable:!(n=r(t,a))||n.enumerable});return e})(i({},"__esModule",{value:!0}),e));var m=class{},y=void 0!==s?s:"undefined"!=typeof window?window:self;function b(e,t){return`${e}`.padStart(t?3:2,"0")}function w(e){return m.Config.logTimestamps?function(t){const s=new Date;e(b(s.getHours())+":"+b(s.getMinutes())+":"+b(s.getSeconds())+"."+b(s.getMilliseconds(),1)+" "+t)}:function(t){e(t)}}var k=class e{constructor(){this.deprecated=(e,t)=>{this.deprecationWarning(`${e} is deprecated and will be removed in a future version. ${t}`)},this.shouldLog=e=>e<=this.logLevel,this.setLog=(e,t)=>{void 0!==e&&(this.logLevel=e),void 0!==t&&(this.logHandler=this.logErrorHandler=t)},this.logLevel=e.defaultLogLevel,this.logHandler=e.defaultLogHandler,this.logErrorHandler=e.defaultLogErrorHandler}static initLogHandlers(){const[t,s]=(()=>{var e;let t,s;return"function"==typeof(null==(e=null==y?void 0:y.console)?void 0:e.log)?(t=function(...e){},s=console.warn?function(...e){}:t):t=s=function(){},[t,s].map(w)})();this.defaultLogHandler=t,this.defaultLogErrorHandler=s,this.defaultLogger=new e}static logActionNoStrip(e,t,s,n){e.logAction(t,s,n)}logAction(e,t,s){this.shouldLog(e)&&(1===e?this.logErrorHandler:this.logHandler)("Ably: "+t+": "+s,e)}renamedClientOption(e,t){this.deprecationWarning(`The \`${e}\` client option has been renamed to \`${t}\`. Please update your code to use \`${t}\` instead. \`${e}\` will be removed in a future version.`)}renamedMethod(e,t,s){this.deprecationWarning(`\`${e}\`’s \`${t}\` method has been renamed to \`${s}\`. Please update your code to use \`${s}\` instead. \`${t}\` will be removed in a future version.`)}deprecationWarning(e){this.shouldLog(1)&&this.logErrorHandler(`Ably: Deprecation warning - ${e}`,1)}};k.defaultLogLevel=1,k.LOG_NONE=0,k.LOG_ERROR=1,k.LOG_MAJOR=2,k.LOG_MINOR=3,k.LOG_MICRO=4,k.logAction=(e,t,s,n)=>{k.logActionNoStrip(e,t,s,n)};var _=k,C={};function E(e){let t="["+e.constructor.name;return e.message&&(t+=": "+e.message),e.statusCode&&(t+="; statusCode="+e.statusCode),e.code&&(t+="; code="+e.code),e.cause&&(t+="; cause="+Q(e.cause)),!e.href||e.message&&e.message.indexOf("help.ably.io")>-1||(t+="; see "+e.href+" "),t+="]",t}g(C,{Format:()=>W,allSame:()=>F,allToLowerCase:()=>re,allToUpperCase:()=>oe,arrChooseN:()=>se,arrDeleteValue:()=>V,arrEquals:()=>ge,arrIntersect:()=>B,arrIntersectOb:()=>q,arrPopRandomElement:()=>z,arrWithoutValue:()=>H,cheapRandStr:()=>ee,containsValue:()=>D,copy:()=>O,createMissingPluginError:()=>ve,dataSizeBytes:()=>Z,decodeBody:()=>ie,encodeBody:()=>ae,ensureArray:()=>A,forInOwnNonNullProperties:()=>$,getBackoffCoefficient:()=>le,getGlobalObject:()=>de,getJitterCoefficient:()=>ce,getRetryTime:()=>ue,inherits:()=>U,inspectBody:()=>X,inspectError:()=>Q,intersect:()=>x,isEmpty:()=>M,isErrorInfoOrPartialErrorInfo:()=>Y,isNil:()=>L,isObject:()=>I,keysArray:()=>G,matchDerivedChannel:()=>pe,mixin:()=>S,parseQueryString:()=>K,prototypicalClone:()=>N,randomString:()=>te,shallowClone:()=>P,shallowEquals:()=>he,throwMissingPluginError:()=>me,toBase64:()=>fe,toQueryString:()=>J,valuesArray:()=>j,whenPromiseSettles:()=>ne,withTimeoutAsync:()=>ye});var R=class e extends Error{constructor(t,s,n,i){super(t),void 0!==Object.setPrototypeOf&&Object.setPrototypeOf(this,e.prototype),this.code=s,this.statusCode=n,this.cause=i}toString(){return E(this)}static fromValues(t){const{message:s,code:n,statusCode:i}=t;if("string"!=typeof s||"number"!=typeof n||"number"!=typeof i)throw new Error("ErrorInfo.fromValues(): invalid values: "+m.Config.inspect(t));const a=Object.assign(new e(s,n,i),t);return a.code&&!a.href&&(a.href="https://help.ably.io/error/"+a.code),a}},T=class e extends Error{constructor(t,s,n,i){super(t),void 0!==Object.setPrototypeOf&&Object.setPrototypeOf(this,e.prototype),this.code=s,this.statusCode=n,this.cause=i}toString(){return E(this)}static fromValues(t){const{message:s,code:n,statusCode:i}=t;if("string"!=typeof s||!L(n)&&"number"!=typeof n||!L(i)&&"number"!=typeof i)throw new Error("PartialErrorInfo.fromValues(): invalid values: "+m.Config.inspect(t));const a=Object.assign(new e(s,n,i),t);return a.code&&!a.href&&(a.href="https://help.ably.io/error/"+a.code),a}};function S(e,...t){for(let s=0;s<t.length;s++){const n=t[s];if(!n)break;for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}function O(e){return S({},e)}function A(e){return L(e)?[]:Array.isArray(e)?e:[e]}function I(e){return"[object Object]"==Object.prototype.toString.call(e)}function M(e){for(const t in e)return!1;return!0}function L(e){return null==e}function P(e){const t=new Object;for(const s in e)t[s]=e[s];return t}function N(e,t){class s{}s.prototype=e;const n=new s;return t&&S(n,t),n}var U=function(e,t){m.Config.inherits?m.Config.inherits(e,t):(e.super_=t,e.prototype=N(t.prototype,{constructor:e}))};function D(e,t){for(const s in e)if(e[s]==t)return!0;return!1}function x(e,t){return Array.isArray(t)?B(e,t):q(e,t)}function B(e,t){const s=[];for(let n=0;n<e.length;n++){const i=e[n];-1!=t.indexOf(i)&&s.push(i)}return s}function q(e,t){const s=[];for(let n=0;n<e.length;n++){const i=e[n];i in t&&s.push(i)}return s}function V(e,t){const s=e.indexOf(t),n=-1!=s;return n&&e.splice(s,1),n}function H(e,t){const s=e.slice();return V(s,t),s}function G(e,t){const s=[];for(const n in e)t&&!Object.prototype.hasOwnProperty.call(e,n)||s.push(n);return s}function j(e,t){const s=[];for(const n in e)t&&!Object.prototype.hasOwnProperty.call(e,n)||s.push(e[n]);return s}function $(e,t){for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&e[s]&&t(s)}function F(e,t){if(0===e.length)return!0;const s=e[0][t];return e.every(function(e){return e[t]===s})}var W=(e=>(e.msgpack="msgpack",e.json="json",e))(W||{});function z(e){return e.splice((t=e,Math.floor(Math.random()*t.length)),1)[0];var t}function J(e){const t=[];if(e)for(const s in e)t.push(encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t.length?"?"+t.join("&"):""}function K(e){let t;const s=/([^?&=]+)=?([^&]*)/g,n={};for(;t=s.exec(e);)n[decodeURIComponent(t[1])]=decodeURIComponent(t[2]);return n}function Y(e){return"object"==typeof e&&null!==e&&(e instanceof R||e instanceof T)}function Q(e){var t,s;return e instanceof Error||"ErrorInfo"===(null==(t=null==e?void 0:e.constructor)?void 0:t.name)||"PartialErrorInfo"===(null==(s=null==e?void 0:e.constructor)?void 0:s.name)?e.toString():m.Config.inspect(e)}function X(e){return m.BufferUtils.isBuffer(e)?e.toString():"string"==typeof e?e:m.Config.inspect(e)}function Z(e){if(m.BufferUtils.isBuffer(e))return m.BufferUtils.byteLength(e);if("string"==typeof e)return m.Config.stringByteSize(e);if("number"==typeof e)return 8;if("boolean"==typeof e)return 1;throw new Error("Expected input of Utils.dataSizeBytes to be a string, a number, a boolean or a buffer, but was: "+typeof e)}function ee(){return String(Math.random()).substr(2)}var te=async e=>{const t=await m.Config.getRandomArrayBuffer(e);return m.BufferUtils.base64Encode(t)};function se(e,t){const s=Math.min(t,e.length),n=e.slice(),i=[];for(let a=0;a<s;a++)i.push(z(n));return i}function ne(e,t){e.then(e=>{null==t||t(null,e)}).catch(e=>{null==t||t(e)})}function ie(e,t,s){return"msgpack"==s?(t||me("MsgPack"),t.decode(e)):JSON.parse(String(e))}function ae(e,t,s){return"msgpack"==s?(t||me("MsgPack"),t.encode(e,!0)):JSON.stringify(e)}function re(e){return e.map(function(e){return e&&e.toLowerCase()})}function oe(e){return e.map(function(e){return e&&e.toUpperCase()})}function le(e){return Math.min((e+2)/3,2)}function ce(){return 1-.2*Math.random()}function ue(e,t){return e*le(t)*ce()}function de(){return void 0!==s?s:"undefined"!=typeof window?window:self}function he(e,t){return Object.keys(e).every(s=>e[s]===t[s])&&Object.keys(t).every(s=>t[s]===e[s])}function pe(e){const t=e.match(/^(\[([^?]*)(?:(.*))\])?(.+)$/);if(!t||!t.length||t.length<5)throw new R("regex match failed",400,40010);if(t[2])throw new R(`cannot use a derived option with a ${t[2]} channel`,400,40010);return{qualifierParam:t[3]||"",channelName:t[4]}}function fe(e){const t=m.BufferUtils,s=t.utf8Encode(e);return t.base64Encode(s)}function ge(e,t){return e.length===t.length&&e.every(function(e,s){return e===t[s]})}function ve(e){return new R(`${e} plugin not provided`,40019,400)}function me(e){throw ve(e)}async function ye(e,t=5e3,s="Timeout expired"){const n=new R(s,5e4,500);return Promise.race([e,new Promise((e,s)=>setTimeout(()=>s(n),t))])}var be="2.9.0",we={ENVIRONMENT:"",REST_HOST:"rest.ably.io",REALTIME_HOST:"realtime.ably.io",FALLBACK_HOSTS:["A.ably-realtime.com","B.ably-realtime.com","C.ably-realtime.com","D.ably-realtime.com","E.ably-realtime.com"],PORT:80,TLS_PORT:443,TIMEOUTS:{disconnectedRetryTimeout:15e3,suspendedRetryTimeout:3e4,httpRequestTimeout:1e4,httpMaxRetryDuration:15e3,channelRetryTimeout:15e3,fallbackRetryTimeout:6e5,connectionStateTtl:12e4,realtimeRequestTimeout:1e4,recvTimeout:9e4,webSocketConnectTimeout:1e4,webSocketSlowTimeout:4e3},httpMaxRetryCount:3,maxMessageSize:65536,version:be,protocolVersion:3,agent:"ably-js/"+be,getHost:ke,getPort:function(e,t){return t||e.tls?e.tlsPort:e.port},getHttpScheme:function(e){return e.tls?"https://":"http://"},environmentFallbackHosts:_e,getFallbackHosts:Ce,getHosts:function(e,t){const s=[e.restHost].concat(Ce(e));return t?s.map(t=>ke(e,t,!0)):s},checkHost:Ee,objectifyOptions:function(e,t,s,n,i){if(void 0===e){const e=t?`${s} must be initialized with either a client options object, an Ably API key, or an Ably Token`:`${s} must be initialized with a client options object`;throw _.logAction(n,_.LOG_ERROR,`${s}()`,e),new Error(e)}let a;if("string"==typeof e)if(-1==e.indexOf(":")){if(!t){const e=`${s} cannot be initialized with just an Ably Token; you must provide a client options object with a \`plugins\` property. (Set this Ably Token as the object’s \`token\` property.)`;throw _.logAction(n,_.LOG_ERROR,`${s}()`,e),new Error(e)}a={token:e}}else{if(!t){const e=`${s} cannot be initialized with just an Ably API key; you must provide a client options object with a \`plugins\` property. (Set this Ably API key as the object’s \`key\` property.)`;throw _.logAction(n,_.LOG_ERROR,`${s}()`,e),new Error(e)}a={key:e}}else a=e;return i&&(a=f(p({},a),{plugins:p(p({},i),a.plugins)})),a},normaliseOptions:function(e,t,s){const n=null!=s?s:_.defaultLogger;"function"==typeof e.recover&&!0===e.closeOnUnload&&(_.logAction(n,_.LOG_ERROR,"Defaults.normaliseOptions","closeOnUnload was true and a session recovery function was set - these are mutually exclusive, so unsetting the latter"),e.recover=void 0),"closeOnUnload"in e||(e.closeOnUnload=!e.recover),"queueMessages"in e||(e.queueMessages=!0);const i=e.environment&&String(e.environment).toLowerCase()||we.ENVIRONMENT,a=!i||"production"===i;e.fallbackHosts||e.restHost||e.realtimeHost||e.port||e.tlsPort||(e.fallbackHosts=a?we.FALLBACK_HOSTS:_e(i));const r=e.restHost||(a?we.REST_HOST:i+"-"+we.REST_HOST),o=function(e,t,s,n){return e.realtimeHost?e.realtimeHost:e.restHost?(_.logAction(n,_.LOG_MINOR,"Defaults.normaliseOptions",'restHost is set to "'+e.restHost+'" but realtimeHost is not set, so setting realtimeHost to "'+e.restHost+'" too. If this is not what you want, please set realtimeHost explicitly.'),e.restHost):t?we.REALTIME_HOST:s+"-"+we.REALTIME_HOST}(e,a,i,n);(e.fallbackHosts||[]).concat(r,o).forEach(Ee),e.port=e.port||we.PORT,e.tlsPort=e.tlsPort||we.TLS_PORT,"tls"in e||(e.tls=!0);const l=function(e){const t={};for(const s in we.TIMEOUTS)t[s]=e[s]||we.TIMEOUTS[s];return t}(e);e.useBinaryProtocol=!!t&&("useBinaryProtocol"in e?m.Config.supportsBinary&&e.useBinaryProtocol:m.Config.preferBinary);const c={};e.clientId&&(c["X-Ably-ClientId"]=m.BufferUtils.base64Encode(m.BufferUtils.utf8Encode(e.clientId))),"idempotentRestPublishing"in e||(e.idempotentRestPublishing=!0);let u=null,d=e.connectivityCheckUrl;if(e.connectivityCheckUrl){let[t,s]=e.connectivityCheckUrl.split("?");u=s?K(s):{},-1===t.indexOf("://")&&(t="https://"+t),d=t}let h=e.wsConnectivityCheckUrl;return h&&-1===h.indexOf("://")&&(h="wss://"+h),f(p({},e),{realtimeHost:o,restHost:r,maxMessageSize:e.maxMessageSize||we.maxMessageSize,timeouts:l,connectivityCheckParams:u,connectivityCheckUrl:d,wsConnectivityCheckUrl:h,headers:c})},defaultGetHeaders:function(e,{format:t=Oe.format,protocolVersion:s=Oe.protocolVersion}={}){return{accept:Se[t],"X-Ably-Version":s.toString(),"Ably-Agent":Re(e)}},defaultPostHeaders:function(e,{format:t=Oe.format,protocolVersion:s=Oe.protocolVersion}={}){let n;return{accept:n=Se[t],"content-type":n,"X-Ably-Version":s.toString(),"Ably-Agent":Re(e)}}};function ke(e,t,s){return t=s?t==e.restHost&&e.realtimeHost||t||e.realtimeHost:t||e.restHost}function _e(e){return[e+"-a-fallback.ably-realtime.com",e+"-b-fallback.ably-realtime.com",e+"-c-fallback.ably-realtime.com",e+"-d-fallback.ably-realtime.com",e+"-e-fallback.ably-realtime.com"]}function Ce(e){const t=e.fallbackHosts,s=void 0!==e.httpMaxRetryCount?e.httpMaxRetryCount:we.httpMaxRetryCount;return t?se(t,s):[]}function Ee(e){if("string"!=typeof e)throw new R("host must be a string; was a "+typeof e,4e4,400);if(!e.length)throw new R("host must not be zero-length",4e4,400)}function Re(e){let t=we.agent;if(e.agents)for(var s in e.agents)t+=" "+s+"/"+e.agents[s];return t}function Te(e,t,s){const n=s||{};if(n.cipher){e||me("Crypto");const s=e.getCipher(n.cipher,t);n.cipher=s.cipherParams,n.channelCipher=s.cipher}else"cipher"in n&&(n.cipher=void 0,n.channelCipher=null);return n}var Se={json:"application/json",xml:"application/xml",html:"text/html",msgpack:"application/x-msgpack",text:"text/plain"},Oe={format:"json",protocolVersion:we.protocolVersion},Ae=we,Ie=class e{constructor(e,t){this.logger=e,this.members=t||[]}call(e,t){for(const n of this.members)if(n)try{n(e,t)}catch(s){_.logAction(this.logger,_.LOG_ERROR,"Multicaster multiple callback handler","Unexpected exception: "+s+"; stack = "+s.stack)}}push(...e){this.members.push(...e)}createPromise(){return new Promise((e,t)=>{this.push((s,n)=>{s?t(s):e(n)})})}resolveAll(e){this.call(null,e)}rejectAll(e){this.call(e)}static create(t,s){const n=new e(t,s);return Object.assign((e,t)=>n.call(e,t),{push:e=>n.push(e),createPromise:()=>n.createPromise(),resolveAll:e=>n.resolveAll(e),rejectAll:e=>n.rejectAll(e)})}},Me=(e=>(e.Get="get",e.Delete="delete",e.Post="post",e.Put="put",e.Patch="patch",e))(Me||{}),Le=Me,Pe=(e=>(e[e.Success=200]="Success",e[e.NoContent=204]="NoContent",e[e.BadRequest=400]="BadRequest",e[e.Unauthorized=401]="Unauthorized",e[e.Forbidden=403]="Forbidden",e[e.RequestTimeout=408]="RequestTimeout",e[e.InternalServerError=500]="InternalServerError",e))(Pe||{}),Ne=Pe,Ue=Math.pow(2,17);function De(e){return Y(e)?(e.code||(403===e.statusCode?e.code=40300:(e.code=40170,e.statusCode=401)),e):new R(Q(e),e.code||40170,e.statusCode||401)}function xe(e){if(!e)return"";"string"==typeof e&&(e=JSON.parse(e));const t=Object.create(null),s=G(e,!0);if(!s)return"";s.sort();for(let n=0;n<s.length;n++)t[s[n]]=e[s[n]].sort();return JSON.stringify(t)}function Be(e,t){if(e.authCallback)_.logAction(t,_.LOG_MINOR,"Auth()","using token auth with authCallback");else if(e.authUrl)_.logAction(t,_.LOG_MINOR,"Auth()","using token auth with authUrl");else if(e.key)_.logAction(t,_.LOG_MINOR,"Auth()","using token auth with client-side signing");else{if(!e.tokenDetails){const e="authOptions must include valid authentication parameters";throw _.logAction(t,_.LOG_ERROR,"Auth()",e),new Error(e)}_.logAction(t,_.LOG_MINOR,"Auth()","using token auth with supplied token only")}}function qe(e){return e.useTokenAuth||!function(e){return"useTokenAuth"in e&&!e.useTokenAuth}(e)&&(e.authCallback||e.authUrl||e.token||e.tokenDetails)}var Ve=0,He=class{constructor(e,t){if(this.authOptions={},this.client=e,this.tokenParams=t.defaultTokenParams||{},this.currentTokenRequestId=null,this.waitingForTokenRequest=null,qe(t))(function(e){return!e.key&&!e.authCallback&&!e.authUrl})(t)&&_.logAction(this.logger,_.LOG_ERROR,"Auth()","Warning: library initialized with a token literal without any way to renew the token when it expires (no authUrl, authCallback, or key). See https://help.ably.io/error/40171 for help"),this._saveTokenOptions(t.defaultTokenParams,t),Be(this.authOptions,this.logger);else{if(!t.key){const e="No authentication options provided; need one of: key, authUrl, or authCallback (or for testing only, token or tokenDetails)";throw _.logAction(this.logger,_.LOG_ERROR,"Auth()",e),new R(e,40160,401)}_.logAction(this.logger,_.LOG_MINOR,"Auth()","anonymous, using basic auth"),this._saveBasicOptions(t)}}get logger(){return this.client.logger}async authorize(e,t){if(t&&t.key&&this.authOptions.key!==t.key)throw new R("Unable to update auth options with incompatible key",40102,401);try{let s=await this._forceNewToken(null!=e?e:null,null!=t?t:null);return this.client.connection?new Promise((e,t)=>{this.client.connection.connectionManager.onAuthUpdated(s,(s,n)=>s?t(s):e(n))}):s}catch(s){throw this.client.connection&&s.statusCode===Ne.Forbidden&&this.client.connection.connectionManager.actOnErrorFromAuthorize(s),s}}async _forceNewToken(e,t){this.tokenDetails=null,this._saveTokenOptions(e,t),Be(this.authOptions,this.logger);try{return this._ensureValidAuthCredentials(!0)}finally{delete this.tokenParams.timestamp,delete this.authOptions.queryTime}}async requestToken(e,t){const s=t||this.authOptions,n=e||O(this.tokenParams);let i,a=this.client;if(s.authCallback)_.logAction(this.logger,_.LOG_MINOR,"Auth.requestToken()","using token auth with authCallback"),i=s.authCallback;else if(s.authUrl)_.logAction(this.logger,_.LOG_MINOR,"Auth.requestToken()","using token auth with authUrl"),i=(e,t)=>{const n=S({accept:"application/json, text/plain"},s.authHeaders),i=s.authMethod&&"post"===s.authMethod.toLowerCase();let a;const r=s.authUrl.indexOf("?");r>-1&&(a=K(s.authUrl.slice(r)),s.authUrl=s.authUrl.slice(0,r),i||(s.authParams=S(a,s.authParams)));const o=S({},s.authParams||{},e),l=e=>{var s,n;let i=null!=(s=e.body)?s:null,a=null;if(e.error)_.logAction(this.logger,_.LOG_MICRO,"Auth.requestToken().tokenRequestCallback","Received Error: "+Q(e.error));else{const t=null!=(n=e.headers["content-type"])?n:null;a=Array.isArray(t)?t.join(", "):t,_.logAction(this.logger,_.LOG_MICRO,"Auth.requestToken().tokenRequestCallback","Received; content-type: "+a+"; body: "+X(i))}if(e.error)return void t(e.error,null);if(e.unpacked)return void t(null,i);if(m.BufferUtils.isBuffer(i)&&(i=i.toString()),!a)return void t(new R("authUrl response is missing a content-type header",40170,401),null);const r=a.indexOf("application/json")>-1,o=a.indexOf("text/plain")>-1||a.indexOf("application/jwt")>-1;if(r||o){if(r){if(i.length>Ue)return void t(new R("authUrl response exceeded max permitted length",40170,401),null);try{i=JSON.parse(i)}catch(l){return void t(new R("Unexpected error processing authURL response; err = "+l.message,40170,401),null)}}t(null,i,a)}else t(new R("authUrl responded with unacceptable content-type "+a+", should be either text/plain, application/jwt or application/json",40170,401),null)};if(_.logAction(this.logger,_.LOG_MICRO,"Auth.requestToken().tokenRequestCallback","Requesting token from "+s.authUrl+"; Params: "+JSON.stringify(o)+"; method: "+(i?"POST":"GET")),i){const e=n||{};e["content-type"]="application/x-www-form-urlencoded";const t=J(o).slice(1);ne(this.client.http.doUri(Le.Post,s.authUrl,e,t,a),(e,t)=>l(e||t))}else ne(this.client.http.doUri(Le.Get,s.authUrl,n||{},null,o),(e,t)=>l(e||t))};else{if(!s.key){const e="Need a new token, but authOptions does not include any way to request one (no authUrl, authCallback, or key)";throw _.logAction(this.logger,_.LOG_ERROR,"Auth()","library initialized with a token literal without any way to renew the token when it expires (no authUrl, authCallback, or key). See https://help.ably.io/error/40171 for help"),new R(e,40171,403)}_.logAction(this.logger,_.LOG_MINOR,"Auth.requestToken()","using token auth with client-side signing"),i=(e,t)=>{ne(this.createTokenRequest(e,s),(e,s)=>t(e,null!=s?s:null))}}"capability"in n&&(n.capability=xe(n.capability));const r=(e,t)=>{const n="/keys/"+e.keyName+"/requestToken",i=Ae.defaultPostHeaders(this.client.options);s.requestHeaders&&S(i,s.requestHeaders),_.logAction(this.logger,_.LOG_MICRO,"Auth.requestToken().requestToken","Sending POST to "+n+"; Token params: "+JSON.stringify(e)),ne(this.client.http.do(Le.Post,function(e){return a.baseUri(e)+n},i,JSON.stringify(e),null),(e,s)=>e?t(e):t(s.error,s.body,s.unpacked))};return new Promise((e,t)=>{let a=!1,o=this.client.options.timeouts.realtimeRequestTimeout,l=setTimeout(()=>{a=!0;const e="Token request callback timed out after "+o/1e3+" seconds";_.logAction(this.logger,_.LOG_ERROR,"Auth.requestToken()",e),t(new R(e,40170,401))},o);i(n,(n,i,o)=>{if(a)return;if(clearTimeout(l),n)return _.logAction(this.logger,_.LOG_ERROR,"Auth.requestToken()","token request signing call returned error; err = "+Q(n)),void t(De(n));if("string"==typeof i)return void(0===i.length?t(new R("Token string is empty",40170,401)):i.length>Ue?t(new R("Token string exceeded max permitted length (was "+i.length+" bytes)",40170,401)):"undefined"===i||"null"===i?t(new R("Token string was literal null/undefined",40170,401)):"{"!==i[0]||o&&o.indexOf("application/jwt")>-1?e({token:i}):t(new R("Token was double-encoded; make sure you're not JSON-encoding an already encoded token request or details",40170,401)));if("object"!=typeof i||null===i){const e="Expected token request callback to call back with a token string or token request/details object, but got a "+typeof i;return _.logAction(this.logger,_.LOG_ERROR,"Auth.requestToken()",e),void t(new R(e,40170,401))}const c=JSON.stringify(i).length;if(c>Ue&&!s.suppressMaxLengthCheck)t(new R("Token request/details object exceeded max permitted stringified size (was "+c+" bytes)",40170,401));else if("issued"in i)e(i);else{if(!("keyName"in i)){const e="Expected token request callback to call back with a token string, token request object, or token details object";return _.logAction(this.logger,_.LOG_ERROR,"Auth.requestToken()",e),void t(new R(e,40170,401))}r(i,(s,n,i)=>{if(s)return _.logAction(this.logger,_.LOG_ERROR,"Auth.requestToken()","token request API call returned error; err = "+Q(s)),void t(De(s));i||(n=JSON.parse(n)),_.logAction(this.logger,_.LOG_MINOR,"Auth.getToken()","token received"),e(n)})}})})}async createTokenRequest(e,t){t=t||this.authOptions,e=e||O(this.tokenParams);const s=t.key;if(!s)throw new R("No key specified",40101,403);const n=s.split(":"),i=n[0],a=n[1];if(!a)throw new R("Invalid key specified",40101,403);if(""===e.clientId)throw new R("clientId can’t be an empty string",40012,400);"capability"in e&&(e.capability=xe(e.capability));const r=S({keyName:i},e),o=e.clientId||"",l=e.ttl||"",c=e.capability||"";r.timestamp||(r.timestamp=await this._getTimestamp(t&&t.queryTime));const u=r.nonce||(r.nonce=("000000"+Math.floor(1e16*Math.random())).slice(-16)),d=r.timestamp,h=r.keyName+"\n"+l+"\n"+c+"\n"+o+"\n"+d+"\n"+u+"\n";return r.mac=r.mac||((e,t)=>{const s=m.BufferUtils,n=s.utf8Encode(e),i=s.utf8Encode(t),a=s.hmacSha256(n,i);return s.base64Encode(a)})(h,a),_.logAction(this.logger,_.LOG_MINOR,"Auth.getTokenRequest()","generated signed request"),r}async getAuthParams(){if("basic"==this.method)return{key:this.key};{let e=await this._ensureValidAuthCredentials(!1);if(!e)throw new Error("Auth.getAuthParams(): _ensureValidAuthCredentials returned no error or tokenDetails");return{access_token:e.token}}}async getAuthHeaders(){if("basic"==this.method)return{authorization:"Basic "+this.basicKey};{const e=await this._ensureValidAuthCredentials(!1);if(!e)throw new Error("Auth.getAuthParams(): _ensureValidAuthCredentials returned no error or tokenDetails");return{authorization:"Bearer "+fe(e.token)}}}_saveBasicOptions(e){this.method="basic",this.key=e.key,this.basicKey=fe(e.key),this.authOptions=e||{},"clientId"in e&&this._userSetClientId(e.clientId)}_saveTokenOptions(e,t){this.method="token",e&&(this.tokenParams=e),t&&(t.token&&(t.tokenDetails="string"==typeof t.token?{token:t.token}:t.token),t.tokenDetails&&(this.tokenDetails=t.tokenDetails),"clientId"in t&&this._userSetClientId(t.clientId),this.authOptions=t)}async _ensureValidAuthCredentials(e){const t=this.tokenDetails;if(t){if(this._tokenClientIdMismatch(t.clientId))throw new R("Mismatch between clientId in token ("+t.clientId+") and current clientId ("+this.clientId+")",40102,403);if(!this.client.isTimeOffsetSet()||!t.expires||t.expires>=this.client.getTimestampUsingOffset())return _.logAction(this.logger,_.LOG_MINOR,"Auth.getToken()","using cached token; expires = "+t.expires),t;_.logAction(this.logger,_.LOG_MINOR,"Auth.getToken()","deleting expired token"),this.tokenDetails=null}const s=(this.waitingForTokenRequest||(this.waitingForTokenRequest=Ie.create(this.logger))).createPromise();if(null!==this.currentTokenRequestId&&!e)return s;const n=this.currentTokenRequestId=Ve++;let i,a=null;try{i=await this.requestToken(this.tokenParams,this.authOptions)}catch(o){a=o}if(this.currentTokenRequestId>n)return _.logAction(this.logger,_.LOG_MINOR,"Auth._ensureValidAuthCredentials()","Discarding token request response; overtaken by newer one"),s;this.currentTokenRequestId=null;const r=this.waitingForTokenRequest;return this.waitingForTokenRequest=null,a?(null==r||r.rejectAll(a),s):(null==r||r.resolveAll(this.tokenDetails=i),s)}_userSetClientId(e){if("string"!=typeof e&&null!==e)throw new R("clientId must be either a string or null",40012,400);if("*"===e)throw new R('Can’t use "*" as a clientId as that string is reserved. (To change the default token request behaviour to use a wildcard clientId, instantiate the library with {defaultTokenParams: {clientId: "*"}}), or if calling authorize(), pass it in as a tokenParam: authorize({clientId: "*"}, authOptions)',40012,400);{const t=this._uncheckedSetClientId(e);if(t)throw t}}_uncheckedSetClientId(e){if(this._tokenClientIdMismatch(e)){const t="Unexpected clientId mismatch: client has "+this.clientId+", requested "+e,s=new R(t,40102,401);return _.logAction(this.logger,_.LOG_ERROR,"Auth._uncheckedSetClientId()",t),s}return this.clientId=this.tokenParams.clientId=e,null}_tokenClientIdMismatch(e){return!(!this.clientId||"*"===this.clientId||!e||"*"===e||this.clientId===e)}static isTokenErr(e){return e.code&&e.code>=40140&&e.code<40150}revokeTokens(e,t){return this.client.rest.revokeTokens(e,t)}async _getTimestamp(e){return this.client.getTimestamp(e||!!this.authOptions.queryTime)}};function Ge(e){const t=[];if(e)for(const s in e)t.push(s+"="+e[s]);return t.join("&")}function je(e,t){return e+(t?"?":"")+Ge(t)}var $e=class{constructor(e){this.client=e,this.platformHttp=new m.Http(e),this.checkConnectivity=this.platformHttp.checkConnectivity?()=>this.platformHttp.checkConnectivity():void 0}get logger(){var e,t;return null!=(t=null==(e=this.client)?void 0:e.logger)?t:_.defaultLogger}get supportsAuthHeaders(){return this.platformHttp.supportsAuthHeaders}get supportsLinkHeaders(){return this.platformHttp.supportsLinkHeaders}_getHosts(e){const t=e.connection,s=t&&t.connectionManager.host;return s?[s].concat(Ae.getFallbackHosts(e.options)):Ae.getHosts(e.options)}async do(e,t,s,n,i){try{const a=this.client;if(!a)return{error:new R("http.do called without client",5e4,500)};const r="function"==typeof t?t:function(e){return a.baseUri(e)+t},o=a._currentFallback;if(o){if(o.validUntil>Date.now()){const l=await this.doUri(e,r(o.host),s,n,i);return l.error&&this.platformHttp.shouldFallback(l.error)?(a._currentFallback=null,this.do(e,t,s,n,i)):l}a._currentFallback=null}const l=this._getHosts(a);if(1===l.length)return this.doUri(e,r(l[0]),s,n,i);let c=null;const u=async(t,o)=>{const l=t.shift();c=null!=c?c:new Date;const d=await this.doUri(e,r(l),s,n,i);return d.error&&this.platformHttp.shouldFallback(d.error)&&t.length?Date.now()-c.getTime()>a.options.timeouts.httpMaxRetryDuration?{error:new R(`Timeout for trying fallback hosts retries. Total elapsed time exceeded the ${a.options.timeouts.httpMaxRetryDuration}ms limit`,50003,500)}:u(t,!0):(o&&(a._currentFallback={host:l,validUntil:Date.now()+a.options.timeouts.fallbackRetryTimeout}),d)};return u(l)}catch(a){return{error:new R(`Unexpected error in Http.do: ${Q(a)}`,500,5e4)}}}async doUri(e,t,s,n,i){try{!function(e,t,s,n,i){i.shouldLog(_.LOG_MICRO)&&_.logActionNoStrip(i,_.LOG_MICRO,"Http."+e+"()","Sending; "+je(t,n)+"; Body"+(m.BufferUtils.isBuffer(s)?" (Base64): "+m.BufferUtils.base64Encode(s):": "+s))}(e,t,n,i,this.logger);const a=await this.platformHttp.doUri(e,t,s,n,i);return this.logger.shouldLog(_.LOG_MICRO)&&function(e,t,s,n,i){e.error?_.logActionNoStrip(i,_.LOG_MICRO,"Http."+t+"()","Received Error; "+je(s,n)+"; Error: "+Q(e.error)):_.logActionNoStrip(i,_.LOG_MICRO,"Http."+t+"()","Received; "+je(s,n)+"; Headers: "+Ge(e.headers)+"; StatusCode: "+e.statusCode+"; Body"+(m.BufferUtils.isBuffer(e.body)?" (Base64): "+m.BufferUtils.base64Encode(e.body):": "+e.body))}(a,e,t,i,this.logger),a}catch(a){return{error:new R(`Unexpected error in Http.doUri: ${Q(a)}`,500,5e4)}}}};function Fe(e,t,s){let n,i,a;for(let r=0;r<e.length;r++)if(n=e[r],s&&(n=n[s]),Array.isArray(n)){for(;-1!==(i=n.indexOf(t));)n.splice(i,1);s&&0===n.length&&delete e[r][s]}else if(I(n))for(a in n)Object.prototype.hasOwnProperty.call(n,a)&&Array.isArray(n[a])&&Fe([n],t,a)}var We=class{constructor(e){this.logger=e,this.any=[],this.events=Object.create(null),this.anyOnce=[],this.eventsOnce=Object.create(null)}on(...e){if(1===e.length){const t=e[0];if("function"!=typeof t)throw new Error("EventListener.on(): Invalid arguments: "+m.Config.inspect(e));this.any.push(t)}if(2===e.length){const[t,s]=e;if("function"!=typeof s)throw new Error("EventListener.on(): Invalid arguments: "+m.Config.inspect(e));if(L(t))this.any.push(s);else if(Array.isArray(t))t.forEach(e=>{this.on(e,s)});else{if("string"!=typeof t)throw new Error("EventListener.on(): Invalid arguments: "+m.Config.inspect(e));(this.events[t]||(this.events[t]=[])).push(s)}}}off(...e){if(0==e.length||L(e[0])&&L(e[1]))return this.any=[],this.events=Object.create(null),this.anyOnce=[],void(this.eventsOnce=Object.create(null));const[t,s]=e;let n=null,i=null;if(1!==e.length&&s){if("function"!=typeof s)throw new Error("EventEmitter.off(): invalid arguments:"+m.Config.inspect(e));[i,n]=[t,s]}else"function"==typeof t?n=t:i=t;if(n&&L(i))Fe([this.any,this.events,this.anyOnce,this.eventsOnce],n);else if(Array.isArray(i))i.forEach(e=>{this.off(e,n)});else{if("string"!=typeof i)throw new Error("EventEmitter.off(): invalid arguments:"+m.Config.inspect(e));n?Fe([this.events,this.eventsOnce],n,i):(delete this.events[i],delete this.eventsOnce[i])}}listeners(e){if(e){const t=this.events[e]||[];return this.eventsOnce[e]&&Array.prototype.push.apply(t,this.eventsOnce[e]),t.length?t:null}return this.any.length?this.any:null}emit(e,...t){const s={event:e},n=[];this.anyOnce.length&&(Array.prototype.push.apply(n,this.anyOnce),this.anyOnce=[]),this.any.length&&Array.prototype.push.apply(n,this.any);const i=this.eventsOnce[e];i&&(Array.prototype.push.apply(n,i),delete this.eventsOnce[e]);const a=this.events[e];a&&Array.prototype.push.apply(n,a),n.forEach(e=>{!function(e,t,s,n){try{s.apply(t,n)}catch(i){_.logAction(e,_.LOG_ERROR,"EventEmitter.emit()","Unexpected listener exception: "+i+"; stack = "+(i&&i.stack))}}(this.logger,s,e,t)})}once(...e){const t=e.length;if(0===t||1===t&&"function"!=typeof e[0]){const t=e[0];return new Promise(e=>{this.once(t,e)})}const[s,n]=e;if(1===e.length&&"function"==typeof s)this.anyOnce.push(s);else if(L(s)){if("function"!=typeof n)throw new Error("EventEmitter.once(): Invalid arguments:"+m.Config.inspect(e));this.anyOnce.push(n)}else if(Array.isArray(s)){const t=this,i=function(){const a=Array.prototype.slice.call(arguments);if(s.forEach(function(e){t.off(e,i)}),"function"!=typeof n)throw new Error("EventEmitter.once(): Invalid arguments:"+m.Config.inspect(e));n.apply(this,a)};s.forEach(function(e){t.on(e,i)})}else{if("string"!=typeof s)throw new Error("EventEmitter.once(): Invalid arguments:"+m.Config.inspect(e));const t=this.eventsOnce[s]||(this.eventsOnce[s]=[]);if(n){if("function"!=typeof n)throw new Error("EventEmitter.once(): Invalid arguments:"+m.Config.inspect(e));t.push(n)}}}async whenState(e,t){if("string"!=typeof e||"string"!=typeof t)throw new Error("whenState requires a valid state String argument");return e===t?null:this.once(e)}},ze={HEARTBEAT:0,ACK:1,NACK:2,CONNECT:3,CONNECTED:4,DISCONNECT:5,DISCONNECTED:6,CLOSE:7,CLOSED:8,ERROR:9,ATTACH:10,ATTACHED:11,DETACH:12,DETACHED:13,PRESENCE:14,MESSAGE:15,SYNC:16,AUTH:17,ACTIVATE:18,OBJECT:19,OBJECT_SYNC:20,ANNOTATION:21},Je=[];Object.keys(ze).forEach(function(e){Je[ze[e]]=e});var Ke={HAS_PRESENCE:1,HAS_BACKLOG:2,RESUMED:4,TRANSIENT:16,ATTACH_RESUME:32,HAS_OBJECTS:128,PRESENCE:65536,PUBLISH:1<<17,SUBSCRIBE:1<<18,PRESENCE_SUBSCRIBE:1<<19,ANNOTATION_PUBLISH:1<<21,ANNOTATION_SUBSCRIBE:1<<22,OBJECT_SUBSCRIBE:1<<24,OBJECT_PUBLISH:1<<25},Ye=Object.keys(Ke);Ke.MODE_ALL=Ke.PRESENCE|Ke.PUBLISH|Ke.SUBSCRIBE|Ke.PRESENCE_SUBSCRIBE|Ke.ANNOTATION_PUBLISH|Ke.ANNOTATION_SUBSCRIBE|Ke.OBJECT_SUBSCRIBE|Ke.OBJECT_PUBLISH;var Qe=["PRESENCE","PUBLISH","SUBSCRIBE","PRESENCE_SUBSCRIBE","ANNOTATION_PUBLISH","ANNOTATION_SUBSCRIBE","OBJECT_SUBSCRIBE","OBJECT_PUBLISH"];function Xe(e,t,s){if(s&&s.cipher){e||me("Crypto");const n=e.getCipher(s.cipher,t);return{cipher:n.cipherParams,channelCipher:n.cipher}}return null!=s?s:{}}async function Ze(e,t,s){let n=s.channelCipher,i=e,a=t?t+"/":"";m.BufferUtils.isBuffer(i)||(i=m.BufferUtils.utf8Encode(String(i)),a+="utf-8/");const r=await n.encrypt(i);return a=a+"cipher+"+n.algorithm,{data:r,encoding:a}}async function et(e,t){const s="string"==typeof e.data||m.BufferUtils.isBuffer(e.data)||null===e.data||void 0===e.data,{data:n,encoding:i}=tt(e.data,e.encoding,s);return e.data=n,e.encoding=i,null!=t&&t.cipher?async function(e,t){const{data:s,encoding:n}=await Ze(e.data,e.encoding,t);return e.data=s,e.encoding=n,e}(e,t):e}function tt(e,t,s){if(s)return{data:e,encoding:t};if(I(e)||Array.isArray(e))return{data:JSON.stringify(e),encoding:t?t+"/json":"json"};throw new R("Data type is unsupported",40013,400)}async function st(e,t){const{data:s,encoding:n,error:i}=await nt(e.data,e.encoding,t);if(e.data=s,e.encoding=n,i)throw i}async function nt(e,t,s){const n=function(e){return e&&e.channelOptions?e:{channelOptions:e,plugins:{},baseEncodedPreviousPayload:void 0}}(s);let i,a=e,r=e,o=t;if(t){const e=t.split("/");let s,c=e.length,u="";try{for(;(s=c)>0;){const t=e[--c].match(/([-\w]+)(\+([\w-]+))?/);if(!t)break;switch(u=t[1],u){case"base64":r=m.BufferUtils.base64Decode(String(r)),s==e.length&&(a=r);continue;case"utf-8":r=m.BufferUtils.utf8Decode(r);continue;case"json":r=JSON.parse(r);continue;case"cipher":if(null!=n.channelOptions&&n.channelOptions.cipher&&n.channelOptions.channelCipher){const e=t[3],s=n.channelOptions.channelCipher;if(e!=s.algorithm)throw new Error("Unable to decrypt message with given cipher; incompatible cipher params");r=await s.decrypt(r);continue}throw new Error("Unable to decrypt message; not an encrypted channel");case"vcdiff":if(!n.plugins||!n.plugins.vcdiff)throw new R("Missing Vcdiff decoder (https://github.com/ably-forks/vcdiff-decoder)",40019,400);if("undefined"==typeof Uint8Array)throw new R("Delta decoding not supported on this browser (need ArrayBuffer & Uint8Array)",40020,400);try{let e=n.baseEncodedPreviousPayload;"string"==typeof e&&(e=m.BufferUtils.utf8Encode(e));const t=m.BufferUtils.toBuffer(e);r=m.BufferUtils.toBuffer(r),r=m.BufferUtils.arrayBufferViewToBuffer(n.plugins.vcdiff.decode(r,t)),a=r}catch(l){throw new R("Vcdiff delta decode failed with "+l,40018,400)}continue;default:throw new Error("Unknown encoding")}}}catch(l){const e=l;i=new R(`Error processing the ${u} encoding, decoder returned ‘${e.message}’`,e.code||40013,400)}finally{o=s<=0?null:e.slice(0,s).join("/")}}return i?{error:i,data:r,encoding:o}:(n.baseEncodedPreviousPayload=a,{data:r,encoding:o})}function it(...e){const t=e.length>0?"json":"msgpack",{data:s,encoding:n}=at(this.data,this.encoding,t);return Object.assign({},this,{encoding:n,data:s})}function at(e,t,s){return e&&m.BufferUtils.isBuffer(e)?"msgpack"===s?{data:m.BufferUtils.toBuffer(e),encoding:t}:{data:m.BufferUtils.base64Encode(e),encoding:t?t+"/base64":"base64"}:{data:e,encoding:t}}var rt={encryptData:Ze,encodeData:tt,encodeDataForWire:at,decodeData:nt};function ot(e){const{id:t,connectionId:s,timestamp:n}=e;let i;switch(e.action){case ze.MESSAGE:i=e.messages;break;case ze.PRESENCE:case ze.SYNC:i=e.presence;break;case ze.ANNOTATION:i=e.annotations;break;case ze.OBJECT:case ze.OBJECT_SYNC:i=e.state;break;default:throw new R("Unexpected action "+e.action,4e4,400)}for(let a=0;a<i.length;a++){const e=i[a];e.connectionId||(e.connectionId=s),e.timestamp||(e.timestamp=n),t&&!e.id&&(e.id=t+":"+a)}}function lt(e,t){let s="["+t;for(const n in e)"data"===n?"string"==typeof e.data?s+="; data="+e.data:m.BufferUtils.isBuffer(e.data)?s+="; data (buffer)="+m.BufferUtils.base64Encode(e.data):void 0!==e.data&&(s+="; data (json)="+JSON.stringify(e.data)):!n||"extras"!==n&&"operation"!==n?void 0!==e[n]&&(s+="; "+n+"="+e[n]):s+="; "+n+"="+JSON.stringify(e[n]);return s+="]",s}var ct=class{},ut=class{constructor(e){var t,s,n,i,a,r,o,l,c,u;this.Platform=m,this.ErrorInfo=R,this.Logger=_,this.Defaults=Ae,this.Utils=C,this.EventEmitter=We,this.MessageEncoding=rt,this._additionalHTTPRequestImplementations=null!=(t=e.plugins)?t:null,this.logger=new _,this.logger.setLog(e.logLevel,e.logHandler),_.logAction(this.logger,_.LOG_MICRO,"BaseClient()","initialized with clientOptions "+m.Config.inspect(e)),this._MsgPack=null!=(n=null==(s=e.plugins)?void 0:s.MsgPack)?n:null;const d=this.options=Ae.normaliseOptions(e,this._MsgPack,this.logger);if(d.key){const e=d.key.match(/^([^:\s]+):([^:.\s]+)$/);if(!e){const e="invalid key parameter";throw _.logAction(this.logger,_.LOG_ERROR,"BaseClient()",e),new R(e,40400,404)}d.keyName=e[1],d.keySecret=e[2]}if("clientId"in d){if("string"!=typeof d.clientId&&null!==d.clientId)throw new R("clientId must be either a string or null",40012,400);if("*"===d.clientId)throw new R('Can’t use "*" as a clientId as that string is reserved. (To change the default token request behaviour to use a wildcard clientId, use {defaultTokenParams: {clientId: "*"}})',40012,400)}_.logAction(this.logger,_.LOG_MINOR,"BaseClient()","started; version = "+Ae.version),this._currentFallback=null,this.serverTimeOffset=null,this.http=new $e(this),this.auth=new He(this,d),this._rest=(null==(i=e.plugins)?void 0:i.Rest)?new e.plugins.Rest(this):null,this._Crypto=null!=(r=null==(a=e.plugins)?void 0:a.Crypto)?r:null,this.__FilteredSubscriptions=null!=(l=null==(o=e.plugins)?void 0:o.MessageInteractions)?l:null,this._Annotations=null!=(u=null==(c=e.plugins)?void 0:c.Annotations)?u:null}get rest(){return this._rest||me("Rest"),this._rest}get _FilteredSubscriptions(){return this.__FilteredSubscriptions||me("MessageInteractions"),this.__FilteredSubscriptions}get channels(){return this.rest.channels}get push(){return this.rest.push}device(){var e;return(null==(e=this.options.plugins)?void 0:e.Push)&&this.push.LocalDevice||me("Push"),this._device||(this._device=this.push.LocalDevice.load(this)),this._device}baseUri(e){return Ae.getHttpScheme(this.options)+e+":"+Ae.getPort(this.options,!1)}async stats(e){return this.rest.stats(e)}async time(e){return this.rest.time(e)}async request(e,t,s,n,i,a){return this.rest.request(e,t,s,n,i,a)}batchPublish(e){return this.rest.batchPublish(e)}batchPresence(e){return this.rest.batchPresence(e)}setLog(e){this.logger.setLog(e.level,e.handler)}async getTimestamp(e){return!this.isTimeOffsetSet()&&e?this.time():this.getTimestampUsingOffset()}getTimestampUsingOffset(){return Date.now()+(this.serverTimeOffset||0)}isTimeOffsetSet(){return null!==this.serverTimeOffset}};ut.Platform=m;var dt=ut,ht=class e{toJSON(){var e,t,s;return{id:this.id,deviceSecret:this.deviceSecret,platform:this.platform,formFactor:this.formFactor,clientId:this.clientId,metadata:this.metadata,deviceIdentityToken:this.deviceIdentityToken,push:{recipient:null==(e=this.push)?void 0:e.recipient,state:null==(t=this.push)?void 0:t.state,error:null==(s=this.push)?void 0:s.error}}}toString(){var e,t,s,n;let i="[DeviceDetails";return this.id&&(i+="; id="+this.id),this.platform&&(i+="; platform="+this.platform),this.formFactor&&(i+="; formFactor="+this.formFactor),this.clientId&&(i+="; clientId="+this.clientId),this.metadata&&(i+="; metadata="+this.metadata),this.deviceIdentityToken&&(i+="; deviceIdentityToken="+JSON.stringify(this.deviceIdentityToken)),(null==(e=this.push)?void 0:e.recipient)&&(i+="; push.recipient="+JSON.stringify(this.push.recipient)),(null==(t=this.push)?void 0:t.state)&&(i+="; push.state="+this.push.state),(null==(s=this.push)?void 0:s.error)&&(i+="; push.error="+JSON.stringify(this.push.error)),(null==(n=this.push)?void 0:n.metadata)&&(i+="; push.metadata="+this.push.metadata),i+="]",i}static toRequestBody(e,t,s){return ae(e,t,s)}static fromResponseBody(t,s,n){return n&&(t=ie(t,s,n)),Array.isArray(t)?e.fromValuesArray(t):e.fromValues(t)}static fromValues(t){return t.error=t.error&&R.fromValues(t.error),Object.assign(new e,t)}static fromLocalDevice(t){return Object.assign(new e,t)}static fromValuesArray(t){const s=t.length,n=new Array(s);for(let i=0;i<s;i++)n[i]=e.fromValues(t[i]);return n}};async function pt(e,t,s,n){return e.http.supportsAuthHeaders?n(S(await e.auth.getAuthHeaders(),t),s):n(t,S(await e.auth.getAuthParams(),s))}var ft=class e{static async get(t,s,n,i,a,r){return e.do(Le.Get,t,s,null,n,i,a,null!=r&&r)}static async delete(t,s,n,i,a,r){return e.do(Le.Delete,t,s,null,n,i,a,r)}static async post(t,s,n,i,a,r,o){return e.do(Le.Post,t,s,n,i,a,r,o)}static async patch(t,s,n,i,a,r,o){return e.do(Le.Patch,t,s,n,i,a,r,o)}static async put(t,s,n,i,a,r,o){return e.do(Le.Put,t,s,n,i,a,r,o)}static async do(e,t,s,n,i,a,r,o){r&&((a=a||{}).envelope=r);const l=t.logger;let c=await pt(t,i,a,async function i(a,r){var o;if(l.shouldLog(_.LOG_MICRO)){let i=n;if((null==(o=a["content-type"])?void 0:o.indexOf("msgpack"))>0)try{t._MsgPack||me("MsgPack"),i=t._MsgPack.decode(n)}catch(u){_.logAction(l,_.LOG_MICRO,"Resource."+e+"()","Sending MsgPack Decoding Error: "+Q(u))}_.logAction(l,_.LOG_MICRO,"Resource."+e+"()","Sending; "+je(s,r)+"; Body: "+i)}const c=await t.http.do(e,s,a,n,r);return c.error&&He.isTokenErr(c.error)?(await t.auth.authorize(null,null),pt(t,a,r,i)):{err:c.error,body:c.body,headers:c.headers,unpacked:c.unpacked,statusCode:c.statusCode}});if(r&&(c=function(e,t,s){if(e.err&&!e.body)return{err:e.err};if(e.statusCode===Ne.NoContent)return f(p({},e),{body:[],unpacked:!0});let n=e.body;if(!e.unpacked)try{n=ie(n,t,s)}catch(o){return Y(o)?{err:o}:{err:new T(Q(o),null)}}if(!n)return{err:new T("unenvelope(): Response body is missing",null)};const{statusCode:i,response:a,headers:r}=n;if(void 0===i)return f(p({},e),{body:n,unpacked:!0});if(i<200||i>=300){let t=a&&a.error||e.err;return t||(t=new Error("Error in unenveloping "+n),t.statusCode=i),{err:t,body:a,headers:r,unpacked:!0,statusCode:i}}return{err:e.err,body:a,headers:r,unpacked:!0,statusCode:i}}(c,t._MsgPack,r)),l.shouldLog(_.LOG_MICRO)&&function(e,t,s,n,i){e.err?_.logAction(i,_.LOG_MICRO,"Resource."+t+"()","Received Error; "+je(s,n)+"; Error: "+Q(e.err)):_.logAction(i,_.LOG_MICRO,"Resource."+t+"()","Received; "+je(s,n)+"; Headers: "+Ge(e.headers)+"; StatusCode: "+e.statusCode+"; Body: "+(m.BufferUtils.isBuffer(e.body)?" (Base64): "+m.BufferUtils.base64Encode(e.body):": "+m.Config.inspect(e.body)))}(c,e,s,a,l),o){if(c.err)throw c.err;{const e=p({},c);return delete e.err,e}}return c}};function gt(e){const t=e.match(/^\.\/(\w+)\?(.*)$/);return t&&t[2]&&K(t[2])}var vt=class{constructor(e,t,s){this.resource=e,this.items=t,this._relParams=s}async first(){if(this.hasFirst())return this.get(this._relParams.first);throw new R("No link to the first page of results",40400,404)}async current(){if(this.hasCurrent())return this.get(this._relParams.current);throw new R("No link to the current page of results",40400,404)}async next(){return this.hasNext()?this.get(this._relParams.next):null}hasFirst(){return null!=this._relParams&&"first"in this._relParams}hasCurrent(){return null!=this._relParams&&"current"in this._relParams}hasNext(){return null!=this._relParams&&"next"in this._relParams}isLast(){return!this.hasNext()}async get(e){const t=this.resource,s=await ft.get(t.client,t.path,t.headers,e,t.envelope,!1);return t.handlePage(s)}},mt=class extends vt{constructor(e,t,s,n,i,a){super(e,t,i),this.statusCode=n,this.success=n<300&&n>=200,this.headers=s,this.errorCode=a&&a.code,this.errorMessage=a&&a.message}toJSON(){return{items:this.items,statusCode:this.statusCode,success:this.success,headers:this.headers,errorCode:this.errorCode,errorMessage:this.errorMessage}}},yt=class{constructor(e,t,s,n,i,a){this.client=e,this.path=t,this.headers=s,this.envelope=null!=n?n:null,this.bodyHandler=i,this.useHttpPaginatedResponse=a||!1}get logger(){return this.client.logger}async get(e){const t=await ft.get(this.client,this.path,this.headers,e,this.envelope,!1);return this.handlePage(t)}async delete(e){const t=await ft.delete(this.client,this.path,this.headers,e,this.envelope,!1);return this.handlePage(t)}async post(e,t){const s=await ft.post(this.client,this.path,t,this.headers,e,this.envelope,!1);return this.handlePage(s)}async put(e,t){const s=await ft.put(this.client,this.path,t,this.headers,e,this.envelope,!1);return this.handlePage(s)}async patch(e,t){const s=await ft.patch(this.client,this.path,t,this.headers,e,this.envelope,!1);return this.handlePage(s)}async handlePage(e){if(e.err&&(t=e.err,s=e.body,!this.useHttpPaginatedResponse||!s&&"number"!=typeof t.code))throw _.logAction(this.logger,_.LOG_ERROR,"PaginatedResource.handlePage()","Unexpected error getting resource: err = "+Q(e.err)),e.err;var t,s;let n,i,a;try{n=e.statusCode==Ne.NoContent?[]:await this.bodyHandler(e.body,e.headers||{},e.unpacked)}catch(r){throw e.err||r}return e.headers&&(i=e.headers.Link||e.headers.link)&&(a=function(e){"string"==typeof e&&(e=e.split(","));const t={};for(let s=0;s<e.length;s++){const n=e[s].match(/^\s*<(.+)>;\s*rel="(\w+)"$/);if(n){const e=gt(n[1]);e&&(t[n[2]]=e)}}return t}(i)),this.useHttpPaginatedResponse?new mt(this,n,e.headers||{},e.statusCode,a,e.err):new vt(this,n,a)}},bt=class e{toJSON(){return{channel:this.channel,deviceId:this.deviceId,clientId:this.clientId}}toString(){let e="[PushChannelSubscription";return this.channel&&(e+="; channel="+this.channel),this.deviceId&&(e+="; deviceId="+this.deviceId),this.clientId&&(e+="; clientId="+this.clientId),e+="]",e}static fromResponseBody(t,s,n){return n&&(t=ie(t,s,n)),Array.isArray(t)?e.fromValuesArray(t):e.fromValues(t)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){const s=t.length,n=new Array(s);for(let i=0;i<s;i++)n[i]=e.fromValues(t[i]);return n}};bt.toRequestBody=ae;var wt=bt,kt=class{constructor(e){this.client=e,this.deviceRegistrations=new _t(e),this.channelSubscriptions=new Ct(e)}async publish(e,t){const s=this.client,n=s.options.useBinaryProtocol?"msgpack":"json",i=Ae.defaultPostHeaders(s.options,{format:n}),a={},r=S({recipient:e},t);S(i,s.options.headers),s.options.pushFullWait&&S(a,{fullWait:"true"});const o=ae(r,s._MsgPack,n);await ft.post(s,"/push/publish",o,i,a,null,!0)}},_t=class{constructor(e){this.client=e}async save(e){const t=this.client,s=ht.fromValues(e),n=t.options.useBinaryProtocol?"msgpack":"json",i=Ae.defaultPostHeaders(t.options,{format:n}),a={};S(i,t.options.headers),t.options.pushFullWait&&S(a,{fullWait:"true"});const r=ae(s,t._MsgPack,n),o=await ft.put(t,"/push/deviceRegistrations/"+encodeURIComponent(e.id),r,i,a,null,!0);return ht.fromResponseBody(o.body,t._MsgPack,o.unpacked?void 0:n)}async get(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Ae.defaultGetHeaders(t.options,{format:s}),i=e.id||e;if("string"!=typeof i||!i.length)throw new R("First argument to DeviceRegistrations#get must be a deviceId string or DeviceDetails",4e4,400);S(n,t.options.headers);const a=await ft.get(t,"/push/deviceRegistrations/"+encodeURIComponent(i),n,{},null,!0);return ht.fromResponseBody(a.body,t._MsgPack,a.unpacked?void 0:s)}async list(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s,i=Ae.defaultGetHeaders(t.options,{format:s});return S(i,t.options.headers),new yt(t,"/push/deviceRegistrations",i,n,async function(e,n,i){return ht.fromResponseBody(e,t._MsgPack,i?void 0:s)}).get(e)}async remove(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Ae.defaultGetHeaders(t.options,{format:s}),i={},a=e.id||e;if("string"!=typeof a||!a.length)throw new R("First argument to DeviceRegistrations#remove must be a deviceId string or DeviceDetails",4e4,400);S(n,t.options.headers),t.options.pushFullWait&&S(i,{fullWait:"true"}),await ft.delete(t,"/push/deviceRegistrations/"+encodeURIComponent(a),n,i,null,!0)}async removeWhere(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Ae.defaultGetHeaders(t.options,{format:s});S(n,t.options.headers),t.options.pushFullWait&&S(e,{fullWait:"true"}),await ft.delete(t,"/push/deviceRegistrations",n,e,null,!0)}},Ct=class e{constructor(t){this.remove=e.prototype.removeWhere,this.client=t}async save(e){const t=this.client,s=wt.fromValues(e),n=t.options.useBinaryProtocol?"msgpack":"json",i=Ae.defaultPostHeaders(t.options,{format:n}),a={};S(i,t.options.headers),t.options.pushFullWait&&S(a,{fullWait:"true"});const r=ae(s,t._MsgPack,n),o=await ft.post(t,"/push/channelSubscriptions",r,i,a,null,!0);return wt.fromResponseBody(o.body,t._MsgPack,o.unpacked?void 0:n)}async list(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s,i=Ae.defaultGetHeaders(t.options,{format:s});return S(i,t.options.headers),new yt(t,"/push/channelSubscriptions",i,n,async function(e,n,i){return wt.fromResponseBody(e,t._MsgPack,i?void 0:s)}).get(e)}async removeWhere(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Ae.defaultGetHeaders(t.options,{format:s});S(n,t.options.headers),t.options.pushFullWait&&S(e,{fullWait:"true"}),await ft.delete(t,"/push/channelSubscriptions",n,e,null,!0)}async listChannels(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s,i=Ae.defaultGetHeaders(t.options,{format:s});return S(i,t.options.headers),t.options.pushFullWait&&S(e,{fullWait:"true"}),new yt(t,"/push/channels",i,n,async function(e,n,i){const a=!i&&s?ie(e,t._MsgPack,s):e;for(let t=0;t<a.length;t++)a[t]=String(a[t]);return a}).get(e)}},Et=class{constructor(e){var t;this.client=e,this.admin=new kt(e),m.Config.push&&(null==(t=e.options.plugins)?void 0:t.Push)&&(this.stateMachine=new e.options.plugins.Push.ActivationStateMachine(e),this.LocalDevice=e.options.plugins.Push.localDeviceFactory(ht))}async activate(e,t){await new Promise((s,n)=>{var i;(null==(i=this.client.options.plugins)?void 0:i.Push)?this.stateMachine?this.stateMachine.activatedCallback?n(new R("Activation already in progress",4e4,400)):(this.stateMachine.activatedCallback=e=>{e?n(e):s()},this.stateMachine.updateFailedCallback=t,this.stateMachine.handleEvent(new this.client.options.plugins.Push.CalledActivate(this.stateMachine,e))):n(new R("This platform is not supported as a target of push notifications",4e4,400)):n(ve("Push"))})}async deactivate(e){await new Promise((t,s)=>{var n;(null==(n=this.client.options.plugins)?void 0:n.Push)?this.stateMachine?this.stateMachine.deactivatedCallback?s(new R("Deactivation already in progress",4e4,400)):(this.stateMachine.deactivatedCallback=e=>{e?s(e):t()},this.stateMachine.handleEvent(new this.client.options.plugins.Push.CalledDeactivate(this.stateMachine,e))):s(new R("This platform is not supported as a target of push notifications",4e4,400)):s(ve("Push"))})}},Rt=["absent","present","enter","leave","update"];async function Tt(e,t,s,n){const i=Xe(t,e,null!=n?n:null);return At.fromValues(s).decode(i,e)}async function St(e,t){return Promise.all(e.map(function(e){return async function(e,t){return At.fromValues(e).decode(t.channelOptions,t.logger)}(e,t)}))}var Ot=class e extends ct{isSynthesized(){return!this.id||!this.connectionId||this.id.substring(this.connectionId.length,0)!==this.connectionId}parseId(){if(!this.id)throw new Error("parseId(): Presence message does not contain an id");const e=this.id.split(":");return{connectionId:e[0],msgSerial:parseInt(e[1],10),index:parseInt(e[2],10)}}async encode(e){return et(Object.assign(new At,this,{action:Rt.indexOf(this.action||"present")}),e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}static fromData(t){return t instanceof e?t:e.fromValues({data:t})}toString(){return lt(this,"PresenceMessage")}},At=class e extends ct{toJSON(...e){return it.call(this,...e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}async decode(e,t){const s=Object.assign(new Ot,f(p({},this),{action:Rt[this.action]}));try{await st(s,e)}catch(n){_.logAction(t,_.LOG_ERROR,"WirePresenceMessage.decode()",Q(n))}return s}toString(){return lt(this,"WirePresenceMessage")}},It=Ot,Mt=class{constructor(e){this.channel=e}get logger(){return this.channel.logger}async get(e){_.logAction(this.logger,_.LOG_MICRO,"RestPresence.get()","channel = "+this.channel.name);const t=this.channel.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.channel.client.http.supportsLinkHeaders?void 0:s,i=Ae.defaultGetHeaders(t.options,{format:s});return S(i,t.options.headers),new yt(t,this.channel.client.rest.presenceMixin.basePath(this),i,n,async(e,n,i)=>St(i?e:ie(e,t._MsgPack,s),this.channel)).get(e)}async history(e){return _.logAction(this.logger,_.LOG_MICRO,"RestPresence.history()","channel = "+this.channel.name),this.channel.client.rest.presenceMixin.history(this,e)}},Lt=["message.create","message.update","message.delete","meta","message.summary"];function Pt(e){let t=0;return e.name&&(t+=e.name.length),e.clientId&&(t+=e.clientId.length),e.extras&&(t+=JSON.stringify(e.extras).length),e.data&&(t+=Z(e.data)),t}async function Nt(e,t,s,n){const i=Xe(t,e,null!=n?n:null);return Vt.fromValues(s).decode(i,e)}async function Ut(e,t){return Promise.all(e.map(function(e){return async function(e,t){return Vt.fromValues(e).decode(t.channelOptions,t.logger)}(e,t)}))}async function Dt(e,t){return Promise.all(e.map(e=>e.encode(t)))}var xt=ae;function Bt(e){let t,s=0;for(let n=0;n<e.length;n++)t=e[n],s+=t.size||(t.size=Pt(t));return s}var qt=class e extends ct{expandFields(){"message.create"===this.action&&(this.version&&!this.serial&&(this.serial=this.version),this.timestamp&&!this.createdAt&&(this.createdAt=this.timestamp))}async encode(e){return et(Object.assign(new Vt,this,{action:Lt.indexOf(this.action||"message.create")}),e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}toString(){return lt(this,"Message")}},Vt=class e extends ct{toJSON(...e){return it.call(this,...e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}async decodeWithErr(e,t){const s=Object.assign(new qt,f(p({},this),{action:(n=this.action,Lt[n||0]||"unknown")}));var n;let i;try{await st(s,e)}catch(a){_.logAction(t,_.LOG_ERROR,"WireMessage.decode()",Q(a)),i=a}return s.expandFields(),{decoded:s,err:i}}async decode(e,t){const{decoded:s}=await this.decodeWithErr(e,t);return s}toString(){return lt(this,"WireMessage")}},Ht=qt,Gt=class{constructor(e,t,s){var n,i;this._annotations=null,_.logAction(e.logger,_.LOG_MINOR,"RestChannel()","started; name = "+t),this.name=t,this.client=e,this.presence=new Mt(this),this.channelOptions=Te(null!=(n=e._Crypto)?n:null,this.logger,s),(null==(i=e.options.plugins)?void 0:i.Push)&&(this._push=new e.options.plugins.Push.PushChannel(this)),e._Annotations&&(this._annotations=new e._Annotations.RestAnnotations(this))}get annotations(){return this._annotations||me("Annotations"),this._annotations}get push(){return this._push||me("Push"),this._push}get logger(){return this.client.logger}setOptions(e){var t;this.channelOptions=Te(null!=(t=this.client._Crypto)?t:null,this.logger,e)}async history(e){return _.logAction(this.logger,_.LOG_MICRO,"RestChannel.history()","channel = "+this.name),this.client.rest.channelMixin.history(this,e)}async publish(...e){const t=e[0],s=e[1];let n,i;if("string"==typeof t||null===t)n=[Ht.fromValues({name:t,data:s})],i=e[2];else if(I(t))n=[Ht.fromValues(t)],i=e[1];else{if(!Array.isArray(t))throw new R("The single-argument form of publish() expects a message object or an array of message objects",40013,400);n=Ht.fromValuesArray(t),i=e[1]}i||(i={});const a=this.client,r=a.options,o=r.useBinaryProtocol?"msgpack":"json",l=a.options.idempotentRestPublishing,c=Ae.defaultPostHeaders(a.options,{format:o});if(S(c,r.headers),l&&function(e){return e.every(function(e){return!e.id})}(n)){const e=await te(9);n.forEach(function(t,s){t.id=e+":"+s.toString()})}const u=await Dt(n,this.channelOptions),d=Bt(u),h=r.maxMessageSize;if(d>h)throw new R(`Maximum size of messages that can be published at once exceeded (was ${d} bytes; limit is ${h} bytes)`,40009,400);await this._publish(xt(u,a._MsgPack,o),c,i)}async _publish(e,t,s){await ft.post(this.client,this.client.rest.channelMixin.basePath(this)+"/messages",e,t,s,null,!0)}async status(){return this.client.rest.channelMixin.status(this)}},jt=class e{constructor(e){this.entries=e&&e.entries||void 0,this.schema=e&&e.schema||void 0,this.appId=e&&e.appId||void 0,this.inProgress=e&&e.inProgress||void 0,this.unit=e&&e.unit||void 0,this.intervalId=e&&e.intervalId||void 0}static fromValues(t){return new e(t)}},$t=class{static basePath(e){return"/channels/"+encodeURIComponent(e.name)}static history(e,t){const s=e.client,n=s.options.useBinaryProtocol?"msgpack":"json",i=e.client.http.supportsLinkHeaders?void 0:n,a=Ae.defaultGetHeaders(s.options,{format:n});return S(a,s.options.headers),new yt(s,this.basePath(e)+"/messages",a,i,async function(t,i,a){return Ut(a?t:ie(t,s._MsgPack,n),e)}).get(t)}static async status(e){const t=e.client.options.useBinaryProtocol?"msgpack":"json",s=Ae.defaultPostHeaders(e.client.options,{format:t});return(await ft.get(e.client,this.basePath(e),s,{},t,!0)).body}},Ft=class{static basePath(e){return $t.basePath(e.channel)+"/presence"}static async history(e,t){const s=e.channel.client,n=s.options.useBinaryProtocol?"msgpack":"json",i=e.channel.client.http.supportsLinkHeaders?void 0:n,a=Ae.defaultGetHeaders(s.options,{format:n});return S(a,s.options.headers),new yt(s,this.basePath(e)+"/history",a,i,async(t,i,a)=>St(a?t:ie(t,s._MsgPack,n),e.channel)).get(t)}},Wt=class{constructor(e){this.channelMixin=$t,this.presenceMixin=Ft,this.Resource=ft,this.PaginatedResource=yt,this.DeviceDetails=ht,this.PushChannelSubscription=wt,this.client=e,this.channels=new zt(this.client),this.push=new Et(this.client)}async stats(e){const t=Ae.defaultGetHeaders(this.client.options),s=this.client.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s;return S(t,this.client.options.headers),new yt(this.client,"/stats",t,n,function(e,t,s){const n=s?e:JSON.parse(e);for(let i=0;i<n.length;i++)n[i]=jt.fromValues(n[i]);return n}).get(e)}async time(e){const t=Ae.defaultGetHeaders(this.client.options);this.client.options.headers&&S(t,this.client.options.headers);let{error:s,body:n,unpacked:i}=await this.client.http.do(Le.Get,e=>this.client.baseUri(e)+"/time",t,null,e);if(s)throw s;i||(n=JSON.parse(n));const a=n[0];if(!a)throw new R("Internal error (unexpected result type from GET /time)",5e4,500);return this.client.serverTimeOffset=a-Date.now(),a}async request(e,t,s,n,i,a){var r;const[o,l,c]=(()=>this.client.options.useBinaryProtocol?(this.client._MsgPack||me("MsgPack"),[this.client._MsgPack.encode,this.client._MsgPack.decode,"msgpack"]):[JSON.stringify,JSON.parse,"json"])(),u=this.client.http.supportsLinkHeaders?void 0:c;n=n||{};const d=e.toLowerCase(),h="get"==d?Ae.defaultGetHeaders(this.client.options,{format:c,protocolVersion:s}):Ae.defaultPostHeaders(this.client.options,{format:c,protocolVersion:s});"string"!=typeof i&&(i=null!=(r=o(i))?r:null),S(h,this.client.options.headers),a&&S(h,a);const p=new yt(this.client,t,h,u,async function(e,t,s){return A(s?e:l(e))},!0);if(!m.Http.methods.includes(d))throw new R("Unsupported method "+d,40500,405);return m.Http.methodsWithBody.includes(d)?p[d](n,i):p[d](n)}async batchPublish(e){let t,s;Array.isArray(e)?(t=e,s=!1):(t=[e],s=!0);const n=this.client.options.useBinaryProtocol?"msgpack":"json",i=Ae.defaultPostHeaders(this.client.options,{format:n});this.client.options.headers&&S(i,this.client.options.headers);const a=ae(t,this.client._MsgPack,n),r=await ft.post(this.client,"/messages",a,i,{},null,!0),o=r.unpacked?r.body:ie(r.body,this.client._MsgPack,n);return s?o[0]:o}async batchPresence(e){const t=this.client.options.useBinaryProtocol?"msgpack":"json",s=Ae.defaultPostHeaders(this.client.options,{format:t});this.client.options.headers&&S(s,this.client.options.headers);const n=e.join(","),i=await ft.get(this.client,"/presence",s,{channels:n},null,!0);return i.unpacked?i.body:ie(i.body,this.client._MsgPack,t)}async revokeTokens(e,t){if(qe(this.client.options))throw new R("Cannot revoke tokens when using token auth",40162,401);const s=this.client.options.keyName;let n=null!=t?t:{};const i=p({targets:e.map(e=>`${e.type}:${e.value}`)},n),a=this.client.options.useBinaryProtocol?"msgpack":"json",r=Ae.defaultPostHeaders(this.client.options,{format:a});this.client.options.headers&&S(r,this.client.options.headers);const o=ae(i,this.client._MsgPack,a),l=await ft.post(this.client,`/keys/${s}/revokeTokens`,o,r,{},null,!0);return l.unpacked?l.body:ie(l.body,this.client._MsgPack,a)}},zt=class{constructor(e){this.client=e,this.all=Object.create(null)}get(e,t){e=String(e);let s=this.all[e];return s?t&&s.setOptions(t):this.all[e]=s=new Gt(this.client,e,t),s}release(e){delete this.all[String(e)]}},Jt=class extends dt{constructor(e){super(Ae.objectifyOptions(e,!1,"BaseRest",_.defaultLogger,{Rest:Wt}))}},Kt={Rest:Wt},Yt=class extends Ht{static async fromEncoded(e,t){return Nt(_.defaultLogger,m.Crypto,e,t)}static async fromEncodedArray(e,t){return async function(e,t,s,n){return Promise.all(s.map(function(s){return Nt(e,t,s,n)}))}(_.defaultLogger,m.Crypto,e,t)}static fromValues(e){return Ht.fromValues(e)}},Qt=class extends It{static async fromEncoded(e,t){return Tt(_.defaultLogger,m.Crypto,e,t)}static async fromEncodedArray(e,t){return async function(e,t,s,n){return Promise.all(s.map(function(s){return Tt(e,t,s,n)}))}(_.defaultLogger,m.Crypto,e,t)}static fromValues(e){return It.fromValues(e)}},Xt=["annotation.create","annotation.delete"];async function Zt(e,t,s){return ss.fromValues(t).decode(s||{},e)}async function es(e,t){return Promise.all(e.map(function(e){return async function(e,t){return ss.fromValues(e).decode(t.channelOptions,t.logger)}(e,t)}))}var ts=class e extends ct{async encode(){return et(Object.assign(new ss,this,{action:Xt.indexOf(this.action||"annotation.create")}),{})}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}toString(){return lt(this,"Annotation")}},ss=class e extends ct{toJSON(...e){return it.call(this,...e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}async decode(e,t){const s=Object.assign(new ts,f(p({},this),{action:Xt[this.action]}));try{await st(s,e)}catch(n){_.logAction(t,_.LOG_ERROR,"WireAnnotation.decode()",Q(n))}return s}toString(){return lt(this,"WireAnnotation")}},ns=ts,is=class extends ns{static async fromEncoded(e,t){return Zt(_.defaultLogger,e,t)}static async fromEncodedArray(e,t){return async function(e,t,s){return Promise.all(t.map(function(t){return Zt(e,t,s)}))}(_.defaultLogger,e,t)}static fromValues(e){return ns.fromValues(e)}};function as(e){let t;switch(typeof e){case"string":t=e;break;case"object":t=e.serial}if(!t||"string"!=typeof t)throw new R("First argument of annotations.publish() must be either a Message (or at least an object with a string `serial` property) or a message serial (string)",40003,400);return t}function rs(e,t){const s=as(e);if(!t||"object"!=typeof t)throw new R("Second argument of annotations.publish() must be an object (the intended annotation to publish)",40003,400);const n=ns.fromValues(t);return n.messageSerial=s,n.action||(n.action="annotation.create"),n}function os(e,t){return e.client.rest.channelMixin.basePath(e)+"/messages/"+encodeURIComponent(t)+"/annotations"}var ls=class{constructor(e){this.channel=e}async publish(e,t){const s=rs(e,t),n=await s.encode(),i=this.channel.client,a=i.options.useBinaryProtocol?"msgpack":"json",r=Ae.defaultPostHeaders(i.options,{format:a});S(r,i.options.headers);const o=ae([n],i._MsgPack,a);await ft.post(i,os(this.channel,s.messageSerial),o,r,{},null,!0)}async delete(e,t){return t.action="annotation.delete",this.publish(e,t)}async get(e,t){const s=this.channel.client,n=as(e),i=s.options.useBinaryProtocol?"msgpack":"json",a=s.http.supportsLinkHeaders?void 0:i,r=Ae.defaultGetHeaders(s.options,{format:i});return S(r,s.options.headers),new yt(s,os(this.channel,n),r,a,async(e,t,n)=>es(n?e:ie(e,s._MsgPack,i),this.channel)).get(t)}},cs=ae;function us(e){const t=[];if(e)for(let s=0;s<e.length;s++)t.push(e[s].toString());return"[ "+t.join(", ")+" ]"}function ds(e,t,s,n){let i,a,r,o,l;return e.error&&(i=R.fromValues(e.error)),e.messages&&(a=Vt.fromValuesArray(e.messages)),t&&e.presence&&(r=t.WirePresenceMessage.fromValuesArray(e.presence)),s&&e.annotations&&(o=s.WireAnnotation.fromValuesArray(e.annotations)),n&&e.state&&(l=n.ObjectMessage.fromValuesArray(e.state,C,rt)),Object.assign(new gs,f(p({},e),{presence:r,messages:a,annotations:o,state:l,error:i}))}function hs(e){return t=>{var s;return ds(t,{WirePresenceMessage:At},{WireAnnotation:ss},null!=(s=null==e?void 0:e.ObjectsPlugin)?s:null)}}function ps(e){return Object.assign(new gs,e)}function fs(e,t,s,n){let i="[ProtocolMessage";void 0!==e.action&&(i+="; action="+Je[e.action]||e.action);const a=["id","channel","channelSerial","connectionId","count","msgSerial","timestamp"];let r;for(let o=0;o<a.length;o++)r=a[o],void 0!==e[r]&&(i+="; "+r+"="+e[r]);if(e.messages&&(i+="; messages="+us(Vt.fromValuesArray(e.messages))),e.presence&&t&&(i+="; presence="+us(t.WirePresenceMessage.fromValuesArray(e.presence))),e.annotations&&s&&(i+="; annotations="+us(s.WireAnnotation.fromValuesArray(e.annotations))),e.state&&n&&(i+="; state="+us(n.ObjectMessage.fromValuesArray(e.state,C,rt))),e.error&&(i+="; error="+R.fromValues(e.error).toString()),e.auth&&e.auth.accessToken&&(i+="; token="+e.auth.accessToken),e.flags&&(i+="; flags="+Ye.filter(e.hasFlag).join(",")),e.params){let t="";$(e.params,function(s){t.length>0&&(t+="; "),t+=s+"="+e.params[s]}),t.length>0&&(i+="; params=["+t+"]")}return i+="]",i}var gs=class{constructor(){this.hasFlag=e=>(this.flags&Ke[e])>0}setFlag(e){return this.flags=this.flags|Ke[e]}getMode(){return(this.flags||0)&Ke.MODE_ALL}encodeModesToFlags(e){e.forEach(e=>this.setFlag(e))}decodeModesFromFlags(){const e=[];return Qe.forEach(t=>{this.hasFlag(t)&&e.push(t)}),e.length>0?e:void 0}},vs=gs,ms=class{constructor(e,t,s,n,i){this.previous=e,this.current=t,"attached"===t&&(this.resumed=s,this.hasBacklog=n),i&&(this.reason=i)}},ys=function(){};function bs(e){const t=e||{},{agent:s}=t;return((e,t)=>{var s={};for(var n in e)u.call(e,n)&&t.indexOf(n)<0&&(s[n]=e[n]);if(null!=e&&c)for(var n of c(e))t.indexOf(n)<0&&d.call(e,n)&&(s[n]=e[n]);return s})(t,["agent"])}var ws=class e extends We{constructor(e,t,s){var n,i,a;super(e.logger),this._annotations=null,this._mode=0,this.retryCount=0,this.history=async function(e){_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.history()","channel = "+this.name);const t=this.client.rest.channelMixin;if(e&&e.untilAttach){if("attached"!==this.state)throw new R("option untilAttach requires the channel to be attached",4e4,400);if(!this.properties.attachSerial)throw new R("untilAttach was specified and channel is attached, but attachSerial is not defined",4e4,400);delete e.untilAttach,e.from_serial=this.properties.attachSerial}return t.history(this,e)},this.whenState=e=>We.prototype.whenState.call(this,e,this.state),_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel()","started; name = "+t),this.name=t,this.channelOptions=Te(null!=(n=e._Crypto)?n:null,this.logger,s),this.client=e,this._presence=e._RealtimePresence?new e._RealtimePresence.RealtimePresence(this):null,e._Annotations&&(this._annotations=new e._Annotations.RealtimeAnnotations(this)),this.connectionManager=e.connection.connectionManager,this.state="initialized",this.subscriptions=new We(this.logger),this.syncChannelSerial=void 0,this.properties={attachSerial:void 0,channelSerial:void 0},this.setOptions(s),this.errorReason=null,this._attachResume=!1,this._decodingContext={channelOptions:this.channelOptions,plugins:e.options.plugins||{},baseEncodedPreviousPayload:void 0},this._lastPayload={messageId:null,protocolMessageChannelSerial:null,decodeFailureRecoveryInProgress:null},this._allChannelChanges=new We(this.logger),(null==(i=e.options.plugins)?void 0:i.Push)&&(this._push=new e.options.plugins.Push.PushChannel(this)),(null==(a=e.options.plugins)?void 0:a.Objects)&&(this._objects=new e.options.plugins.Objects.Objects(this))}get presence(){return this._presence||me("RealtimePresence"),this._presence}get annotations(){return this._annotations||me("Annotations"),this._annotations}get push(){return this._push||me("Push"),this._push}get objects(){return this._objects||me("Objects"),this._objects}invalidStateError(){return new R("Channel operation failed as channel state is "+this.state,90001,400,this.errorReason||void 0)}static processListenerArgs(e){return"function"==typeof(e=Array.prototype.slice.call(e))[0]&&e.unshift(null),e}async setOptions(e){var t;const s=this.channelOptions,n=function(e){if(e&&"params"in e&&!I(e.params))return new R("options.params must be an object",4e4,400);if(e&&"modes"in e){if(!Array.isArray(e.modes))return new R("options.modes must be an array",4e4,400);for(let t=0;t<e.modes.length;t++){const s=e.modes[t];if(!s||"string"!=typeof s||!Qe.includes(String.prototype.toUpperCase.call(s)))return new R("Invalid channel mode: "+s,4e4,400)}}}(e);if(n)throw n;if(this.channelOptions=Te(null!=(t=this.client._Crypto)?t:null,this.logger,e),this._decodingContext&&(this._decodingContext.channelOptions=this.channelOptions),this._shouldReattachToSetOptions(e,s))return this.attachImpl(),new Promise((e,t)=>{this._allChannelChanges.once(["attached","update","detached","failed"],function(s){switch(this.event){case"update":case"attached":e();break;default:t(s.reason)}})})}_shouldReattachToSetOptions(e,t){if("attached"!==this.state&&"attaching"!==this.state)return!1;if(null==e?void 0:e.params){const s=bs(e.params),n=bs(t.params);if(Object.keys(s).length!==Object.keys(n).length)return!0;if(!he(n,s))return!0}return!(!(null==e?void 0:e.modes)||t.modes&&ge(e.modes,t.modes))}async publish(...e){let t;if(1==e.length)if(I(e[0]))t=[Ht.fromValues(e[0])];else{if(!Array.isArray(e[0]))throw new R("The single-argument form of publish() expects a message object or an array of message objects",40013,400);t=Ht.fromValuesArray(e[0])}else t=[Ht.fromValues({name:e[0],data:e[1]})];const s=this.client.options.maxMessageSize,n=await Dt(t,this.channelOptions),i=Bt(n);if(i>s)throw new R(`Maximum size of messages that can be published at once exceeded (was ${i} bytes; limit is ${s} bytes)`,40009,400);this.throwIfUnpublishableState(),_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.publish()","sending message; channel state is "+this.state+", message count = "+n.length);const a=ps({action:ze.MESSAGE,channel:this.name,messages:n});return this.sendMessage(a)}throwIfUnpublishableState(){if(!this.connectionManager.activeState())throw this.connectionManager.getError();if("failed"===this.state||"suspended"===this.state)throw this.invalidStateError()}onEvent(e){_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.onEvent()","received message");const t=this.subscriptions;for(let s=0;s<e.length;s++){const n=e[s];t.emit(n.name,n)}}async attach(){return"attached"===this.state?null:new Promise((e,t)=>{this._attach(!1,null,(s,n)=>s?t(s):e(n))})}_attach(e,t,s){s||(s=e=>{e&&_.logAction(this.logger,_.LOG_ERROR,"RealtimeChannel._attach()","Channel attach failed: "+e.toString())});const n=this.connectionManager;n.activeState()?(("attaching"!==this.state||e)&&this.requestState("attaching",t),this.once(function(e){switch(this.event){case"attached":null==s||s(null,e);break;case"detached":case"suspended":case"failed":null==s||s(e.reason||n.getError()||new R("Unable to attach; reason unknown; state = "+this.event,9e4,500));break;case"detaching":null==s||s(new R("Attach request superseded by a subsequent detach request",9e4,409))}})):s(n.getError())}attachImpl(){_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.attachImpl()","sending ATTACH message");const e=ps({action:ze.ATTACH,channel:this.name,params:this.channelOptions.params,channelSerial:this.properties.channelSerial});this.channelOptions.modes&&e.encodeModesToFlags(oe(this.channelOptions.modes)),this._attachResume&&e.setFlag("ATTACH_RESUME"),this._lastPayload.decodeFailureRecoveryInProgress&&(e.channelSerial=this._lastPayload.protocolMessageChannelSerial),this.sendMessage(e).catch(ys)}async detach(){const e=this.connectionManager;if(!e.activeState())throw e.getError();switch(this.state){case"suspended":return void this.notifyState("detached");case"detached":return;case"failed":throw new R("Unable to detach; channel state = failed",90001,400);default:this.requestState("detaching");case"detaching":return new Promise((t,s)=>{this.once(function(n){switch(this.event){case"detached":t();break;case"attached":case"suspended":case"failed":s(n.reason||e.getError()||new R("Unable to detach; reason unknown; state = "+this.event,9e4,500));break;case"attaching":s(new R("Detach request superseded by a subsequent attach request",9e4,409))}})})}}detachImpl(){_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.detach()","sending DETACH message");const e=ps({action:ze.DETACH,channel:this.name});this.sendMessage(e).catch(ys)}async subscribe(...t){const[s,n]=e.processListenerArgs(t);if("failed"===this.state)throw R.fromValues(this.invalidStateError());return s&&"object"==typeof s&&!Array.isArray(s)?this.client._FilteredSubscriptions.subscribeFilter(this,s,n):this.subscriptions.on(s,n),!1!==this.channelOptions.attachOnSubscribe?this.attach():null}unsubscribe(...t){var s;const[n,i]=e.processListenerArgs(t);"object"==typeof n&&!i||(null==(s=this.filteredSubscriptions)?void 0:s.has(i))?this.client._FilteredSubscriptions.getAndDeleteFilteredSubscriptions(this,n,i).forEach(e=>this.subscriptions.off(e)):this.subscriptions.off(n,i)}sync(){switch(this.state){case"initialized":case"detaching":case"detached":throw new T("Unable to sync to channel; not attached",4e4)}const e=this.connectionManager;if(!e.activeState())throw e.getError();const t=ps({action:ze.SYNC,channel:this.name});this.syncChannelSerial&&(t.channelSerial=this.syncChannelSerial),e.send(t)}async sendMessage(e){return new Promise((t,s)=>{this.connectionManager.send(e,this.client.options.queueMessages,e=>{e?s(e):t()})})}async sendPresence(e){const t=ps({action:ze.PRESENCE,channel:this.name,presence:e});return this.sendMessage(t)}sendState(e){const t=ps({action:ze.OBJECT,channel:this.name,state:e});return this.sendMessage(t)}async processMessage(e){e.action!==ze.ATTACHED&&e.action!==ze.MESSAGE&&e.action!==ze.PRESENCE&&e.action!==ze.OBJECT&&e.action!==ze.ANNOTATION||this.setChannelSerial(e.channelSerial);let t,s=!1;switch(e.action){case ze.ATTACHED:{this.properties.attachSerial=e.channelSerial,this._mode=e.getMode(),this.params=e.params||{};const t=e.decodeModesFromFlags();this.modes=t&&re(t)||void 0;const s=e.hasFlag("RESUMED"),n=e.hasFlag("HAS_PRESENCE"),i=e.hasFlag("HAS_BACKLOG"),a=e.hasFlag("HAS_OBJECTS");if("attached"===this.state){s||(this._presence&&this._presence.onAttached(n),this._objects&&this._objects.onAttached(a));const t=new ms(this.state,this.state,s,i,e.error);this._allChannelChanges.emit("update",t),s&&!this.channelOptions.updateOnAttached||this.emit("update",t)}else"detaching"===this.state?this.checkPendingState():this.notifyState("attached",e.error,s,n,i,a);break}case ze.DETACHED:{const t=e.error?R.fromValues(e.error):new R("Channel detached",90001,404);"detaching"===this.state?this.notifyState("detached",t):"attaching"===this.state?this.notifyState("suspended",t):"attached"!==this.state&&"suspended"!==this.state||this.requestState("attaching",t);break}case ze.SYNC:if(s=!0,t=this.syncChannelSerial=e.channelSerial,!e.presence)break;case ze.PRESENCE:{if(!e.presence)break;ot(e);const n=this.channelOptions;if(this._presence){const i=await Promise.all(e.presence.map(e=>e.decode(n,this.logger)));this._presence.setPresence(i,s,t)}break}case ze.OBJECT:case ze.OBJECT_SYNC:{if(!this._objects||!e.state)return;ot(e);const t=e.state,s=this.client.connection.connectionManager.getActiveTransportFormat();await Promise.all(t.map(e=>this.client._objectsPlugin.ObjectMessage.decode(e,this.client,this.logger,_,C,s))),e.action===ze.OBJECT?this._objects.handleObjectMessages(t):this._objects.handleObjectSyncMessages(t,e.channelSerial);break}case ze.MESSAGE:{if("attached"!==this.state)return void _.logAction(this.logger,_.LOG_MAJOR,"RealtimeChannel.processMessage()",'Message "'+e.id+'" skipped as this channel "'+this.name+'" state is not "attached" (state is "'+this.state+'").');ot(e);const t=e.messages,s=t[0],n=t[t.length-1];if(s.extras&&s.extras.delta&&s.extras.delta.from!==this._lastPayload.messageId){const t='Delta message decode failure - previous message not available for message "'+e.id+'" on this channel "'+this.name+'".';_.logAction(this.logger,_.LOG_ERROR,"RealtimeChannel.processMessage()",t),this._startDecodeFailureRecovery(new R(t,40018,400));break}let i=[];for(let e=0;e<t.length;e++){const{decoded:s,err:n}=await t[e].decodeWithErr(this._decodingContext,this.logger);if(i[e]=s,n)switch(n.code){case 40018:return void this._startDecodeFailureRecovery(n);case 40019:case 40021:return void this.notifyState("failed",n)}}this._lastPayload.messageId=n.id,this._lastPayload.protocolMessageChannelSerial=e.channelSerial,this.onEvent(i);break}case ze.ANNOTATION:{ot(e);const t=this.channelOptions;if(this._annotations){const s=await Promise.all((e.annotations||[]).map(e=>e.decode(t,this.logger)));this._annotations._processIncoming(s)}break}case ze.ERROR:{const t=e.error;t&&80016==t.code?this.checkPendingState():this.notifyState("failed",R.fromValues(t));break}default:_.logAction(this.logger,_.LOG_MAJOR,"RealtimeChannel.processMessage()","Protocol error: unrecognised message action ("+e.action+")")}}_startDecodeFailureRecovery(e){this._lastPayload.decodeFailureRecoveryInProgress||(_.logAction(this.logger,_.LOG_MAJOR,"RealtimeChannel.processMessage()","Starting decode failure recovery process."),this._lastPayload.decodeFailureRecoveryInProgress=!0,this._attach(!0,e,()=>{this._lastPayload.decodeFailureRecoveryInProgress=!1}))}onAttached(){_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel.onAttached","activating channel; name = "+this.name)}notifyState(e,t,s,n,i,a){if(_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.notifyState","name = "+this.name+", current state = "+this.state+", notifying state "+e),this.clearStateTimer(),["detached","suspended","failed"].includes(e)&&(this.properties.channelSerial=null),e===this.state)return;this._presence&&this._presence.actOnChannelState(e,n,t),this._objects&&this._objects.actOnChannelState(e,a),"suspended"===e&&this.connectionManager.state.sendEvents?this.startRetryTimer():this.cancelRetryTimer(),t&&(this.errorReason=t);const r=new ms(this.state,e,s,i,t),o='Channel state for channel "'+this.name+'"',l=e+(t?"; reason: "+t:"");"failed"===e?_.logAction(this.logger,_.LOG_ERROR,o,l):_.logAction(this.logger,_.LOG_MAJOR,o,l),"attaching"!==e&&"suspended"!==e&&(this.retryCount=0),"attached"===e&&this.onAttached(),"attached"===e?this._attachResume=!0:"detaching"!==e&&"failed"!==e||(this._attachResume=!1),this.state=e,this._allChannelChanges.emit(e,r),this.emit(e,r)}requestState(e,t){_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel.requestState","name = "+this.name+", state = "+e),this.notifyState(e,t),this.checkPendingState()}checkPendingState(){if(this.connectionManager.state.sendEvents)switch(_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel.checkPendingState","name = "+this.name+", state = "+this.state),this.state){case"attaching":this.startStateTimerIfNotRunning(),this.attachImpl();break;case"detaching":this.startStateTimerIfNotRunning(),this.detachImpl();break;case"attached":this.sync()}else _.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel.checkPendingState","sendEvents is false; state is "+this.connectionManager.state.state)}timeoutPendingState(){switch(this.state){case"attaching":{const e=new R("Channel attach timed out",90007,408);this.notifyState("suspended",e);break}case"detaching":{const e=new R("Channel detach timed out",90007,408);this.notifyState("attached",e);break}default:this.checkPendingState()}}startStateTimerIfNotRunning(){this.stateTimer||(this.stateTimer=setTimeout(()=>{_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel.startStateTimerIfNotRunning","timer expired"),this.stateTimer=null,this.timeoutPendingState()},this.client.options.timeouts.realtimeRequestTimeout))}clearStateTimer(){const e=this.stateTimer;e&&(clearTimeout(e),this.stateTimer=null)}startRetryTimer(){if(this.retryTimer)return;this.retryCount++;const e=ue(this.client.options.timeouts.channelRetryTimeout,this.retryCount);this.retryTimer=setTimeout(()=>{"suspended"===this.state&&this.connectionManager.state.sendEvents&&(this.retryTimer=null,_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel retry timer expired","attempting a new attach"),this.requestState("attaching"))},e)}cancelRetryTimer(){this.retryTimer&&(clearTimeout(this.retryTimer),this.retryTimer=null)}getReleaseErr(){const e=this.state;return"initialized"===e||"detached"===e||"failed"===e?null:new R("Can only release a channel in a state where there is no possibility of further updates from the server being received (initialized, detached, or failed); was "+e,90001,400)}setChannelSerial(e){_.logAction(this.logger,_.LOG_MICRO,"RealtimeChannel.setChannelSerial()","Updating channel serial; serial = "+e+"; previous = "+this.properties.channelSerial),e&&(this.properties.channelSerial=e)}async status(){return this.client.rest.channelMixin.status(this)}},ks=class{constructor(e){this.channel=e,this.logger=e.logger,this.subscriptions=new We(this.logger)}async publish(e,t){const s=this.channel.name,n=rs(e,t),i=await n.encode();this.channel.throwIfUnpublishableState(),_.logAction(this.logger,_.LOG_MICRO,"RealtimeAnnotations.publish()","channelName = "+s+", sending annotation with messageSerial = "+n.messageSerial+", type = "+n.type);const a=ps({action:ze.ANNOTATION,channel:s,annotations:[i]});return this.channel.sendMessage(a)}async delete(e,t){return t.action="annotation.delete",this.publish(e,t)}async subscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1],i=this.channel;if("failed"===i.state)throw R.fromValues(i.invalidStateError());if(this.subscriptions.on(s,n),!1!==this.channel.channelOptions.attachOnSubscribe&&await i.attach(),0===("attached"===this.channel.state&&this.channel._mode&Ke.ANNOTATION_SUBSCRIBE))throw new R("You are trying to add an annotation listener, but you haven't requested the annotation_subscribe channel mode in ChannelOptions, so this won't do anything (we only deliver annotations to clients who have explicitly requested them)",93001,400)}unsubscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1];this.subscriptions.off(s,n)}_processIncoming(e){for(const t of e)this.subscriptions.emit(t.type||"",t)}async get(e,t){return ls.prototype.get.call(this,e,t)}},_s=class e extends Jt{constructor(t){var s,n;if(!e._MsgPack)throw new Error("Expected DefaultRest._MsgPack to have been set");super(Ae.objectifyOptions(t,!0,"Rest",_.defaultLogger,f(p({},Kt),{Crypto:null!=(s=e.Crypto)?s:void 0,MsgPack:null!=(n=e._MsgPack)?n:void 0,Annotations:{Annotation:ns,WireAnnotation:ss,RealtimeAnnotations:ks,RestAnnotations:ls}})))}static get Crypto(){if(null===this._Crypto)throw new Error("Encryption not enabled; use ably.encryption.js instead");return this._Crypto}static set Crypto(e){this._Crypto=e}};_s._Crypto=null,_s.Message=Yt,_s.PresenceMessage=Qt,_s.Annotation=is,_s._MsgPack=null,_s._Http=$e;var Cs,Es,Rs=_s,Ts=class extends We{constructor(e){super(e),this.messages=[]}count(){return this.messages.length}push(e){this.messages.push(e)}shift(){return this.messages.shift()}last(){return this.messages[this.messages.length-1]}copyAll(){return this.messages.slice()}append(e){this.messages.push.apply(this.messages,e)}prepend(e){this.messages.unshift.apply(this.messages,e)}completeMessages(e,t,s){_.logAction(this.logger,_.LOG_MICRO,"MessageQueue.completeMessages()","serial = "+e+"; count = "+t),s=s||null;const n=this.messages;if(0===n.length)throw new Error("MessageQueue.completeMessages(): completeMessages called on any empty MessageQueue");const i=n[0];if(i){const a=i.message.msgSerial,r=e+t;if(r>a){const e=n.splice(0,r-a);for(const t of e)t.callback(s)}0==n.length&&this.emit("idle")}}completeAllMessages(e){this.completeMessages(0,Number.MAX_SAFE_INTEGER||Number.MAX_VALUE,e)}resetSendAttempted(){for(let e of this.messages)e.sendAttempted=!1}clear(){_.logAction(this.logger,_.LOG_MICRO,"MessageQueue.clear()","clearing "+this.messages.length+" messages"),this.messages=[],this.emit("idle")}},Ss=class{constructor(e,t){this.message=e,this.callback=t,this.merged=!1;const s=e.action;this.sendAttempted=!1,this.ackRequired="number"==typeof s&&[ze.MESSAGE,ze.PRESENCE,ze.ANNOTATION,ze.OBJECT].includes(s)}},Os=class extends We{constructor(e){super(e.logger),this.transport=e,this.messageQueue=new Ts(this.logger),e.on("ack",(e,t)=>{this.onAck(e,t)}),e.on("nack",(e,t,s)=>{this.onNack(e,t,s)})}onAck(e,t){_.logAction(this.logger,_.LOG_MICRO,"Protocol.onAck()","serial = "+e+"; count = "+t),this.messageQueue.completeMessages(e,t)}onNack(e,t,s){_.logAction(this.logger,_.LOG_ERROR,"Protocol.onNack()","serial = "+e+"; count = "+t+"; err = "+Q(s)),s||(s=new R("Unable to send message; channel not responding",50001,500)),this.messageQueue.completeMessages(e,t,s)}onceIdle(e){const t=this.messageQueue;0!==t.count()?t.once("idle",e):e()}send(e){e.ackRequired&&this.messageQueue.push(e),this.logger.shouldLog(_.LOG_MICRO)&&_.logActionNoStrip(this.logger,_.LOG_MICRO,"Protocol.send()","sending msg; "+fs(e.message,this.transport.connectionManager.realtime._RealtimePresence,this.transport.connectionManager.realtime._Annotations,this.transport.connectionManager.realtime._objectsPlugin)),e.sendAttempted=!0,this.transport.send(e.message)}getTransport(){return this.transport}getPendingMessages(){return this.messageQueue.copyAll()}clearPendingMessages(){return this.messageQueue.clear()}finish(){const e=this.transport;this.onceIdle(function(){e.disconnect()})}},As=class{constructor(e,t,s,n){this.previous=e,this.current=t,s&&(this.retryIn=s),n&&(this.reason=n)}},Is={DISCONNECTED:80003,SUSPENDED:80002,FAILED:8e4,CLOSING:80017,CLOSED:80017,UNKNOWN_CONNECTION_ERR:50002,UNKNOWN_CHANNEL_ERR:50001},Ms={disconnected:()=>R.fromValues({statusCode:400,code:Is.DISCONNECTED,message:"Connection to server temporarily unavailable"}),suspended:()=>R.fromValues({statusCode:400,code:Is.SUSPENDED,message:"Connection to server unavailable"}),failed:()=>R.fromValues({statusCode:400,code:Is.FAILED,message:"Connection failed or disconnected by server"}),closing:()=>R.fromValues({statusCode:400,code:Is.CLOSING,message:"Connection closing"}),closed:()=>R.fromValues({statusCode:400,code:Is.CLOSED,message:"Connection closed"}),unknownConnectionErr:()=>R.fromValues({statusCode:500,code:Is.UNKNOWN_CONNECTION_ERR,message:"Internal connection error"}),unknownChannelErr:()=>R.fromValues({statusCode:500,code:Is.UNKNOWN_CONNECTION_ERR,message:"Internal channel error"})},Ls=ps({action:ze.CLOSE}),Ps=ps({action:ze.DISCONNECT}),Ns=class extends We{constructor(e,t,s,n){super(e.logger),n&&(s.format=void 0,s.heartbeats=!0),this.connectionManager=e,this.auth=t,this.params=s,this.timeouts=s.options.timeouts,this.format=s.format,this.isConnected=!1,this.isFinished=!1,this.isDisposed=!1,this.maxIdleInterval=null,this.idleTimer=null,this.lastActivity=null}connect(){}close(){this.isConnected&&this.requestClose(),this.finish("closed",Ms.closed())}disconnect(e){this.isConnected&&this.requestDisconnect(),this.finish("disconnected",e||Ms.disconnected())}fail(e){this.isConnected&&this.requestDisconnect(),this.finish("failed",e||Ms.failed())}finish(e,t){var s;this.isFinished||(this.isFinished=!0,this.isConnected=!1,this.maxIdleInterval=null,clearTimeout(null!=(s=this.idleTimer)?s:void 0),this.idleTimer=null,this.emit(e,t),this.dispose())}onProtocolMessage(e){switch(this.logger.shouldLog(_.LOG_MICRO)&&_.logActionNoStrip(this.logger,_.LOG_MICRO,"Transport.onProtocolMessage()","received on "+this.shortName+": "+fs(e,this.connectionManager.realtime._RealtimePresence,this.connectionManager.realtime._Annotations,this.connectionManager.realtime._objectsPlugin)+"; connectionId = "+this.connectionManager.connectionId),this.onActivity(),e.action){case ze.HEARTBEAT:_.logActionNoStrip(this.logger,_.LOG_MICRO,"Transport.onProtocolMessage()",this.shortName+" heartbeat; connectionId = "+this.connectionManager.connectionId),this.emit("heartbeat",e.id);break;case ze.CONNECTED:this.onConnect(e),this.emit("connected",e.error,e.connectionId,e.connectionDetails,e);break;case ze.CLOSED:this.onClose(e);break;case ze.DISCONNECTED:this.onDisconnect(e);break;case ze.ACK:this.emit("ack",e.msgSerial,e.count);break;case ze.NACK:this.emit("nack",e.msgSerial,e.count,e.error);break;case ze.SYNC:this.connectionManager.onChannelMessage(e,this);break;case ze.ACTIVATE:break;case ze.AUTH:ne(this.auth.authorize(),e=>{e&&_.logAction(this.logger,_.LOG_ERROR,"Transport.onProtocolMessage()","Ably requested re-authentication, but unable to obtain a new token: "+Q(e))});break;case ze.ERROR:if(_.logAction(this.logger,_.LOG_MINOR,"Transport.onProtocolMessage()","received error action; connectionId = "+this.connectionManager.connectionId+"; err = "+m.Config.inspect(e.error)+(e.channel?", channel: "+e.channel:"")),void 0===e.channel){this.onFatalError(e);break}this.connectionManager.onChannelMessage(e,this);break;default:this.connectionManager.onChannelMessage(e,this)}}onConnect(e){if(this.isConnected=!0,!e.connectionDetails)throw new Error("Transport.onConnect(): Connect message recieved without connectionDetails");const t=e.connectionDetails.maxIdleInterval;t&&(this.maxIdleInterval=t+this.timeouts.realtimeRequestTimeout,this.onActivity())}onDisconnect(e){const t=e&&e.error;_.logAction(this.logger,_.LOG_MINOR,"Transport.onDisconnect()","err = "+Q(t)),this.finish("disconnected",t)}onFatalError(e){const t=e&&e.error;_.logAction(this.logger,_.LOG_MINOR,"Transport.onFatalError()","err = "+Q(t)),this.finish("failed",t)}onClose(e){const t=e&&e.error;_.logAction(this.logger,_.LOG_MINOR,"Transport.onClose()","err = "+Q(t)),this.finish("closed",t)}requestClose(){_.logAction(this.logger,_.LOG_MINOR,"Transport.requestClose()",""),this.send(Ls)}requestDisconnect(){_.logAction(this.logger,_.LOG_MINOR,"Transport.requestDisconnect()",""),this.send(Ps)}ping(e){const t={action:ze.HEARTBEAT};e&&(t.id=e),this.send(ps(t))}dispose(){_.logAction(this.logger,_.LOG_MINOR,"Transport.dispose()",""),this.isDisposed=!0,this.off()}onActivity(){this.maxIdleInterval&&(this.lastActivity=this.connectionManager.lastActivity=Date.now(),this.setIdleTimer(this.maxIdleInterval+100))}setIdleTimer(e){this.idleTimer||(this.idleTimer=setTimeout(()=>{this.onIdleTimerExpire()},e))}onIdleTimerExpire(){if(!this.lastActivity||!this.maxIdleInterval)throw new Error("Transport.onIdleTimerExpire(): lastActivity/maxIdleInterval not set");this.idleTimer=null;const e=Date.now()-this.lastActivity,t=this.maxIdleInterval-e;if(t<=0){const t="No activity seen from realtime in "+e+"ms; assuming connection has dropped";_.logAction(this.logger,_.LOG_ERROR,"Transport.onIdleTimerExpire()",t),this.disconnect(new R(t,80003,408))}else this.setIdleTimer(t+100)}static tryConnect(e,t,s,n,i){const a=new e(t,s,n);let r;const o=function(e){clearTimeout(r),i({event:this.event,error:e})},l=t.options.timeouts.realtimeRequestTimeout;return r=setTimeout(()=>{a.off(["preconnect","disconnected","failed"]),a.dispose(),o.call({event:"disconnected"},new R("Timeout waiting for transport to indicate itself viable",5e4,500))},l),a.on(["failed","disconnected"],o),a.on("preconnect",function(){_.logAction(t.logger,_.LOG_MINOR,"Transport.tryConnect()","viable transport "+a),clearTimeout(r),a.off(["failed","disconnected"],o),i(null,a)}),a.connect(),a}static isAvailable(){throw new R("isAvailable not implemented for transport",5e4,500)}};(Es=Cs||(Cs={})).WebSocket="web_socket",Es.Comet="comet",Es.XhrPolling="xhr_polling";var Us=void 0!==s?s:"undefined"!=typeof window?window:self,Ds=()=>{var e;return void 0!==m.WebStorage&&(null==(e=m.WebStorage)?void 0:e.localSupported)},xs=()=>{var e;return void 0!==m.WebStorage&&(null==(e=m.WebStorage)?void 0:e.sessionSupported)},Bs=function(){},qs="ably-transport-preference";function Vs(e){try{return JSON.parse(e)}catch(t){return null}}var Hs=class{constructor(e,t,s,n){this.options=e,this.host=t,this.mode=s,this.connectionKey=n,this.format=e.useBinaryProtocol?"msgpack":"json"}getConnectParams(e){const t=e?O(e):{},s=this.options;switch(this.mode){case"resume":t.resume=this.connectionKey;break;case"recover":{const e=Vs(s.recover);e&&(t.recover=e.connectionKey);break}}return void 0!==s.clientId&&(t.clientId=s.clientId),!1===s.echoMessages&&(t.echo="false"),void 0!==this.format&&(t.format=this.format),void 0!==this.stream&&(t.stream=this.stream),void 0!==this.heartbeats&&(t.heartbeats=this.heartbeats),t.v=Ae.protocolVersion,t.agent=Re(this.options),void 0!==s.transportParams&&S(t,s.transportParams),t}toString(){let e="[mode="+this.mode;return this.host&&(e+=",host="+this.host),this.connectionKey&&(e+=",connectionKey="+this.connectionKey),this.format&&(e+=",format="+this.format),e+="]",e}},Gs=class e extends We{constructor(e,t){super(e.logger),this.supportedTransports={},this.disconnectedRetryCount=0,this.pendingChannelMessagesState={isProcessing:!1,queue:[]},this.realtime=e,this.initTransports(),this.options=t;const s=t.timeouts,n=s.webSocketConnectTimeout+s.realtimeRequestTimeout;if(this.states={initialized:{state:"initialized",terminal:!1,queueEvents:!0,sendEvents:!1,failState:"disconnected"},connecting:{state:"connecting",terminal:!1,queueEvents:!0,sendEvents:!1,retryDelay:n,failState:"disconnected"},connected:{state:"connected",terminal:!1,queueEvents:!1,sendEvents:!0,failState:"disconnected"},disconnected:{state:"disconnected",terminal:!1,queueEvents:!0,sendEvents:!1,retryDelay:s.disconnectedRetryTimeout,failState:"disconnected"},suspended:{state:"suspended",terminal:!1,queueEvents:!1,sendEvents:!1,retryDelay:s.suspendedRetryTimeout,failState:"suspended"},closing:{state:"closing",terminal:!1,queueEvents:!1,sendEvents:!1,retryDelay:s.realtimeRequestTimeout,failState:"closed"},closed:{state:"closed",terminal:!0,queueEvents:!1,sendEvents:!1,failState:"closed"},failed:{state:"failed",terminal:!0,queueEvents:!1,sendEvents:!1,failState:"failed"}},this.state=this.states.initialized,this.errorReason=null,this.queuedMessages=new Ts(this.logger),this.msgSerial=0,this.connectionDetails=void 0,this.connectionId=void 0,this.connectionKey=void 0,this.connectionStateTtl=s.connectionStateTtl,this.maxIdleInterval=null,this.transports=x(t.transports||Ae.defaultTransports,this.supportedTransports),this.transportPreference=null,this.transports.includes(Cs.WebSocket)&&(this.webSocketTransportAvailable=!0),this.transports.includes(Cs.XhrPolling)?this.baseTransport=Cs.XhrPolling:this.transports.includes(Cs.Comet)&&(this.baseTransport=Cs.Comet),this.httpHosts=Ae.getHosts(t),this.wsHosts=Ae.getHosts(t,!0),this.activeProtocol=null,this.host=null,this.lastAutoReconnectAttempt=null,this.lastActivity=null,this.forceFallbackHost=!1,this.connectCounter=0,this.wsCheckResult=null,this.webSocketSlowTimer=null,this.webSocketGiveUpTimer=null,this.abandonedWebSocket=!1,_.logAction(this.logger,_.LOG_MINOR,"Realtime.ConnectionManager()","started"),_.logAction(this.logger,_.LOG_MICRO,"Realtime.ConnectionManager()","requested transports = ["+(t.transports||Ae.defaultTransports)+"]"),_.logAction(this.logger,_.LOG_MICRO,"Realtime.ConnectionManager()","available transports = ["+this.transports+"]"),_.logAction(this.logger,_.LOG_MICRO,"Realtime.ConnectionManager()","http hosts = ["+this.httpHosts+"]"),!this.transports.length){const e="no requested transports available";throw _.logAction(this.logger,_.LOG_ERROR,"realtime.ConnectionManager()",e),new Error(e)}const i=m.Config.addEventListener;i&&(xs()&&"function"==typeof t.recover&&i("beforeunload",this.persistConnection.bind(this)),!0===t.closeOnUnload&&i("beforeunload",()=>{_.logAction(this.logger,_.LOG_MAJOR,"Realtime.ConnectionManager()","beforeunload event has triggered the connection to close as closeOnUnload is true"),this.requestState({state:"closing"})}),i("online",()=>{var e;this.state==this.states.disconnected||this.state==this.states.suspended?(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager caught browser ‘online’ event","reattempting connection"),this.requestState({state:"connecting"})):this.state==this.states.connecting&&(null==(e=this.pendingTransport)||e.off(),this.disconnectAllTransports(),this.startConnect())}),i("offline",()=>{this.state==this.states.connected&&(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager caught browser ‘offline’ event","disconnecting active transport"),this.disconnectAllTransports())}))}static supportedTransports(e){const t={supportedTransports:{}};return this.initTransports(e,t),t.supportedTransports}static initTransports(e,t){const s=p(p({},m.Transports.bundledImplementations),e);[Cs.WebSocket,...m.Transports.order].forEach(e=>{const n=s[e];n&&n.isAvailable()&&(t.supportedTransports[e]=n)})}initTransports(){e.initTransports(this.realtime._additionalTransportImplementations,this)}createTransportParams(e,t){return new Hs(this.options,e,t,this.connectionKey)}getTransportParams(e){(e=>{if(this.connectionKey)return void e("resume");if("string"==typeof this.options.recover)return void e("recover");const t=this.options.recover,s=this.getSessionRecoverData(),n=this.sessionRecoveryName();if(s&&"function"==typeof t)return _.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.getTransportParams()","Calling clientOptions-provided recover function with last session data (recovery scope: "+n+")"),void t(s,t=>{t?(this.options.recover=s.recoveryKey,e("recover")):e("clean")});e("clean")})(t=>{const s=this.createTransportParams(null,t);if("recover"===t){_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.getTransportParams()","Transport recovery mode = recover; recoveryKey = "+this.options.recover);const e=Vs(this.options.recover);e&&(this.msgSerial=e.msgSerial)}else _.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.getTransportParams()","Transport params = "+s.toString());e(s)})}tryATransport(e,t,s){_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.tryATransport()","trying "+t),this.proposedTransport=Ns.tryConnect(this.supportedTransports[t],this,this.realtime.auth,e,(n,i)=>{const a=this.state;return a==this.states.closing||a==this.states.closed||a==this.states.failed?(i&&(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.tryATransport()","connection "+a.state+" while we were attempting the transport; closing "+i),i.close()),void s(!0)):n?(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.tryATransport()","transport "+t+" "+n.event+", err: "+n.error.toString()),void(!He.isTokenErr(n.error)||this.errorReason&&He.isTokenErr(this.errorReason)?"failed"===n.event?(this.notifyState({state:"failed",error:n.error}),s(!0)):"disconnected"===n.event&&(!(r=n.error).statusCode||!r.code||r.statusCode>=500||Object.values(Is).includes(r.code)?s(!1):(this.notifyState({state:this.states.connecting.failState,error:n.error}),s(!0))):(this.errorReason=n.error,ne(this.realtime.auth._forceNewToken(null,null),n=>{n?this.actOnErrorFromAuthorize(n):this.tryATransport(e,t,s)})))):(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.tryATransport()","viable transport "+t+"; setting pending"),this.setTransportPending(i,e),void s(null,i));var r})}setTransportPending(e,t){const s=t.mode;_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.setTransportPending()","transport = "+e+"; mode = "+s),this.pendingTransport=e,this.cancelWebSocketSlowTimer(),this.cancelWebSocketGiveUpTimer(),e.once("connected",(t,n,i)=>{this.activateTransport(t,e,n,i),"recover"===s&&this.options.recover&&(delete this.options.recover,this.unpersistConnection())});const n=this;e.on(["disconnected","closed","failed"],function(t){n.deactivateTransport(e,this.event,t)}),this.emit("transport.pending",e)}activateTransport(e,t,s,n){_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.activateTransport()","transport = "+t),e&&_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.activateTransport()","error = "+e),s&&_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.activateTransport()","connectionId =  "+s),n&&_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.activateTransport()","connectionDetails =  "+JSON.stringify(n)),this.persistTransportPreference(t);const i=this.state,a=this.states.connected.state;if(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.activateTransport()","current state = "+i.state),i.state==this.states.closing.state||i.state==this.states.closed.state||i.state==this.states.failed.state)return _.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.activateTransport()","Disconnecting transport and abandoning"),t.disconnect(),!1;if(delete this.pendingTransport,!t.isConnected)return _.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.activateTransport()","Declining to activate transport "+t+" since it appears to no longer be connected"),!1;const r=this.activeProtocol;this.activeProtocol=new Os(t),this.host=t.params.host;const o=n.connectionKey;if(o&&this.connectionKey!=o&&this.setConnection(s,n,!!e),this.onConnectionDetailsUpdate(n,t),m.Config.nextTick(()=>{t.on("connected",(e,s,n)=>{this.onConnectionDetailsUpdate(n,t),this.emit("update",new As(a,a,null,e))})}),i.state===this.states.connected.state?e&&(this.errorReason=this.realtime.connection.errorReason=e,this.emit("update",new As(a,a,null,e))):(this.notifyState({state:"connected",error:e}),this.errorReason=this.realtime.connection.errorReason=e||null),this.emit("transport.active",t),r)if(r.messageQueue.count()>0&&_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.activateTransport()","Previous active protocol (for transport "+r.transport.shortName+", new one is "+t.shortName+") finishing with "+r.messageQueue.count()+" messages still pending"),r.transport===t){const e="Assumption violated: activating a transport that was also the transport for the previous active protocol; transport = "+t.shortName+"; stack = "+(new Error).stack;_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.activateTransport()",e)}else r.finish();return!0}deactivateTransport(e,t,s){const n=this.activeProtocol,i=n&&n.getTransport()===e,a=e===this.pendingTransport,r=this.noTransportsScheduledForActivation();if(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.deactivateTransport()","transport = "+e),_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.deactivateTransport()","state = "+t+(i?"; was active":a?"; was pending":"")+(r?"":"; another transport is scheduled for activation")),s&&s.message&&_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.deactivateTransport()","reason =  "+s.message),i&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.deactivateTransport()","Getting, clearing, and requeuing "+this.activeProtocol.messageQueue.count()+" pending messages"),this.queuePendingMessages(n.getPendingMessages()),n.clearPendingMessages(),this.activeProtocol=this.host=null),this.emit("transport.inactive",e),i&&r||i&&"failed"===t||"closed"===t||null===n&&a){if("disconnected"===t&&s&&s.statusCode>500&&this.httpHosts.length>1)return this.unpersistTransportPreference(),this.forceFallbackHost=!0,void this.notifyState({state:t,error:s,retryImmediately:!0});const e="failed"===t&&He.isTokenErr(s)?"disconnected":t;return void this.notifyState({state:e,error:s})}}noTransportsScheduledForActivation(){return!this.pendingTransport||!this.pendingTransport.isConnected}setConnection(e,t,s){const n=this.connectionId;(n&&n!==e||!n&&s)&&(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.setConnection()","Resetting msgSerial"),this.msgSerial=0,this.queuedMessages.resetSendAttempted()),this.connectionId!==e&&_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.setConnection()","New connectionId; reattaching any attached channels"),this.realtime.connection.id=this.connectionId=e,this.realtime.connection.key=this.connectionKey=t.connectionKey}clearConnection(){this.realtime.connection.id=this.connectionId=void 0,this.realtime.connection.key=this.connectionKey=void 0,this.msgSerial=0,this.unpersistConnection()}createRecoveryKey(){return this.connectionKey?JSON.stringify({connectionKey:this.connectionKey,msgSerial:this.msgSerial,channelSerials:this.realtime.channels.channelSerials()}):null}checkConnectionStateFreshness(){if(!this.lastActivity||!this.connectionId)return;const e=Date.now()-this.lastActivity;e>this.connectionStateTtl+this.maxIdleInterval&&(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.checkConnectionStateFreshness()","Last known activity from realtime was "+e+"ms ago; discarding connection state"),this.clearConnection(),this.states.connecting.failState="suspended")}persistConnection(){if(xs()){const e=this.createRecoveryKey();e&&this.setSessionRecoverData({recoveryKey:e,disconnectedAt:Date.now(),location:Us.location,clientId:this.realtime.auth.clientId})}}unpersistConnection(){this.clearSessionRecoverData()}getActiveTransportFormat(){var e;return null==(e=this.activeProtocol)?void 0:e.getTransport().format}getError(){if(this.errorReason){const e=T.fromValues(this.errorReason);return e.cause=this.errorReason,e}return this.getStateError()}getStateError(){var e,t;return null==(t=(e=Ms)[this.state.state])?void 0:t.call(e)}activeState(){return this.state.queueEvents||this.state.sendEvents}enactStateChange(e){const t="Connection state",s=e.current+(e.reason?"; reason: "+e.reason:"");"failed"===e.current?_.logAction(this.logger,_.LOG_ERROR,t,s):_.logAction(this.logger,_.LOG_MAJOR,t,s),_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.enactStateChange","setting new state: "+e.current+"; reason = "+(e.reason&&e.reason.message));const n=this.state=this.states[e.current];e.reason&&(this.errorReason=e.reason,this.realtime.connection.errorReason=e.reason),(n.terminal||"suspended"===n.state)&&this.clearConnection(),this.emit("connectionstate",e)}startTransitionTimer(e){_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.startTransitionTimer()","transitionState: "+e.state),this.transitionTimer&&(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.startTransitionTimer()","clearing already-running timer"),clearTimeout(this.transitionTimer)),this.transitionTimer=setTimeout(()=>{this.transitionTimer&&(this.transitionTimer=null,_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager "+e.state+" timer expired","requesting new state: "+e.failState),this.notifyState({state:e.failState}))},e.retryDelay)}cancelTransitionTimer(){_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.cancelTransitionTimer()",""),this.transitionTimer&&(clearTimeout(this.transitionTimer),this.transitionTimer=null)}startSuspendTimer(){this.suspendTimer||(this.suspendTimer=setTimeout(()=>{this.suspendTimer&&(this.suspendTimer=null,_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager suspend timer expired","requesting new state: suspended"),this.states.connecting.failState="suspended",this.notifyState({state:"suspended"}))},this.connectionStateTtl))}checkSuspendTimer(e){"disconnected"!==e&&"suspended"!==e&&"connecting"!==e&&this.cancelSuspendTimer()}cancelSuspendTimer(){this.states.connecting.failState="disconnected",this.suspendTimer&&(clearTimeout(this.suspendTimer),this.suspendTimer=null)}startRetryTimer(e){this.retryTimer=setTimeout(()=>{_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager retry timer expired","retrying"),this.retryTimer=null,this.requestState({state:"connecting"})},e)}cancelRetryTimer(){this.retryTimer&&(clearTimeout(this.retryTimer),this.retryTimer=null)}startWebSocketSlowTimer(){this.webSocketSlowTimer=setTimeout(()=>{_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager WebSocket slow timer","checking connectivity"),this.checkWsConnectivity().then(()=>{_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager WebSocket slow timer","ws connectivity check succeeded"),this.wsCheckResult=!0}).catch(()=>{_.logAction(this.logger,_.LOG_MAJOR,"ConnectionManager WebSocket slow timer","ws connectivity check failed"),this.wsCheckResult=!1}),this.realtime.http.checkConnectivity&&ne(this.realtime.http.checkConnectivity(),(e,t)=>{e||!t?(_.logAction(this.logger,_.LOG_MAJOR,"ConnectionManager WebSocket slow timer","http connectivity check failed"),this.cancelWebSocketGiveUpTimer(),this.notifyState({state:"disconnected",error:new R("Unable to connect (network unreachable)",80003,404)})):_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager WebSocket slow timer","http connectivity check succeeded")})},this.options.timeouts.webSocketSlowTimeout)}cancelWebSocketSlowTimer(){this.webSocketSlowTimer&&(clearTimeout(this.webSocketSlowTimer),this.webSocketSlowTimer=null)}startWebSocketGiveUpTimer(e){this.webSocketGiveUpTimer=setTimeout(()=>{var t,s;this.wsCheckResult||(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager WebSocket give up timer","websocket connection took more than 10s; "+(this.baseTransport?"trying base transport":"")),this.baseTransport?(this.abandonedWebSocket=!0,null==(t=this.proposedTransport)||t.dispose(),null==(s=this.pendingTransport)||s.dispose(),this.connectBase(e,++this.connectCounter)):_.logAction(this.logger,_.LOG_MAJOR,"ConnectionManager WebSocket give up timer","websocket connectivity appears to be unavailable but no other transports to try"))},this.options.timeouts.webSocketConnectTimeout)}cancelWebSocketGiveUpTimer(){this.webSocketGiveUpTimer&&(clearTimeout(this.webSocketGiveUpTimer),this.webSocketGiveUpTimer=null)}notifyState(e){var t,s;const n=e.state,i="disconnected"===n&&(this.state===this.states.connected||e.retryImmediately||this.state===this.states.connecting&&e.error&&He.isTokenErr(e.error)&&!(this.errorReason&&He.isTokenErr(this.errorReason)));if(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.notifyState()","new state: "+n+(i?"; will retry connection immediately":"")),n==this.state.state)return;if(this.cancelTransitionTimer(),this.cancelRetryTimer(),this.cancelWebSocketSlowTimer(),this.cancelWebSocketGiveUpTimer(),this.checkSuspendTimer(e.state),"suspended"!==n&&"connected"!==n||(this.disconnectedRetryCount=0),this.state.terminal)return;const a=this.states[e.state];let r=a.retryDelay;"disconnected"===a.state&&(this.disconnectedRetryCount++,r=ue(a.retryDelay,this.disconnectedRetryCount));const o=new As(this.state.state,a.state,r,e.error||(null==(s=(t=Ms)[a.state])?void 0:s.call(t)));if(i){const e=()=>{this.state===this.states.disconnected&&(this.lastAutoReconnectAttempt=Date.now(),this.requestState({state:"connecting"}))},t=this.lastAutoReconnectAttempt&&Date.now()-this.lastAutoReconnectAttempt+1;t&&t<1e3?(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.notifyState()","Last reconnect attempt was only "+t+"ms ago, waiting another "+(1e3-t)+"ms before trying again"),setTimeout(e,1e3-t)):m.Config.nextTick(e)}else"disconnected"!==n&&"suspended"!==n||this.startRetryTimer(r);("disconnected"===n&&!i||"suspended"===n||a.terminal)&&m.Config.nextTick(()=>{this.disconnectAllTransports()}),"connected"!=n||this.activeProtocol||_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.notifyState()","Broken invariant: attempted to go into connected state, but there is no active protocol"),this.enactStateChange(o),this.state.sendEvents?this.sendQueuedMessages():this.state.queueEvents||(this.realtime.channels.propogateConnectionInterruption(n,o.reason),this.failQueuedMessages(o.reason))}requestState(e){var t,s;const n=e.state;if(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.requestState()","requested state: "+n+"; current state: "+this.state.state),n==this.state.state)return;if(this.cancelWebSocketSlowTimer(),this.cancelWebSocketGiveUpTimer(),this.cancelTransitionTimer(),this.cancelRetryTimer(),this.checkSuspendTimer(n),"connecting"==n&&"connected"==this.state.state)return;if("closing"==n&&"closed"==this.state.state)return;const i=this.states[n],a=new As(this.state.state,i.state,null,e.error||(null==(s=(t=Ms)[i.state])?void 0:s.call(t)));this.enactStateChange(a),"connecting"==n&&m.Config.nextTick(()=>{this.startConnect()}),"closing"==n&&this.closeImpl()}startConnect(){if(this.state!==this.states.connecting)return void _.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.startConnect()","Must be in connecting state to connect, but was "+this.state.state);const e=this.realtime.auth,t=++this.connectCounter,s=()=>{this.checkConnectionStateFreshness(),this.getTransportParams(e=>{if("recover"===e.mode&&e.options.recover){const t=Vs(e.options.recover);t&&this.realtime.channels.recoverChannels(t.channelSerials)}t===this.connectCounter&&this.connectImpl(e,t)})};if(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.startConnect()","starting connection"),this.startSuspendTimer(),this.startTransitionTimer(this.states.connecting),"basic"===e.method)s();else{const n=e=>{t===this.connectCounter&&(e?this.actOnErrorFromAuthorize(e):s())};this.errorReason&&He.isTokenErr(this.errorReason)?ne(e._forceNewToken(null,null),n):ne(e._ensureValidAuthCredentials(!1),n)}}connectImpl(e,t){const s=this.state.state;if(s!==this.states.connecting.state)return void _.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.connectImpl()","Must be in connecting state to connect, but was "+s);const n=this.getTransportPreference();n&&n===this.baseTransport&&this.webSocketTransportAvailable&&this.checkWsConnectivity().then(()=>{this.unpersistTransportPreference(),this.state===this.states.connecting&&(_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.connectImpl():","web socket connectivity available, cancelling connection attempt with "+this.baseTransport),this.disconnectAllTransports(),this.connectWs(e,++this.connectCounter))}).catch(Bs),n&&n===this.baseTransport||this.baseTransport&&!this.webSocketTransportAvailable?this.connectBase(e,t):this.connectWs(e,t)}connectWs(e,t){_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.connectWs()"),this.wsCheckResult=null,this.abandonedWebSocket=!1,this.startWebSocketSlowTimer(),this.startWebSocketGiveUpTimer(e),this.tryTransportWithFallbacks("web_socket",e,!0,t,()=>!1!==this.wsCheckResult&&!this.abandonedWebSocket)}connectBase(e,t){_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.connectBase()"),this.baseTransport?this.tryTransportWithFallbacks(this.baseTransport,e,!1,t,()=>!0):this.notifyState({state:"disconnected",error:new R("No transports left to try",8e4,404)})}tryTransportWithFallbacks(e,t,s,n,i){_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.tryTransportWithFallbacks()",e);const a=e=>{this.notifyState({state:this.states.connecting.failState,error:e})},r=s?this.wsHosts.slice():this.httpHosts.slice(),o=(e,t)=>{n===this.connectCounter&&(i()?t||e||c():t&&t.dispose())},l=r.shift();if(!l)return void a(new R("Unable to connect (no available host)",80003,404));t.host=l;const c=()=>{r.length?this.realtime.http.checkConnectivity?ne(this.realtime.http.checkConnectivity(),(s,l)=>{n===this.connectCounter&&i()&&(s?a(s):l?(t.host=z(r),this.tryATransport(t,e,o)):a(new R("Unable to connect (network unreachable)",80003,404)))}):a(new T("Internal error: Http.checkConnectivity not set",null,500)):a(new R("Unable to connect (and no more fallback hosts to try)",80003,404))};if(this.forceFallbackHost&&r.length)return this.forceFallbackHost=!1,void c();this.tryATransport(t,e,o)}closeImpl(){_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.closeImpl()","closing connection"),this.cancelSuspendTimer(),this.startTransitionTimer(this.states.closing),this.pendingTransport&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.closeImpl()","Closing pending transport: "+this.pendingTransport),this.pendingTransport.close()),this.activeProtocol&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.closeImpl()","Closing active transport: "+this.activeProtocol.getTransport()),this.activeProtocol.getTransport().close()),this.notifyState({state:"closed"})}onAuthUpdated(e,t){var s;switch(this.state.state){case"connected":{_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.onAuthUpdated()","Sending AUTH message on active transport");const n=null==(s=this.activeProtocol)?void 0:s.getTransport();n&&n.onAuthUpdated&&n.onAuthUpdated(e);const i=ps({action:ze.AUTH,auth:{accessToken:e.token}});this.send(i);const a=()=>{this.off(r),t(null,e)},r=e=>{"failed"===e.current&&(this.off(a),this.off(r),t(e.reason||this.getStateError()))};this.once("connectiondetails",a),this.on("connectionstate",r);break}case"connecting":_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.onAuthUpdated()","Aborting current connection attempts in order to start again with the new auth details"),this.disconnectAllTransports();default:{_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.onAuthUpdated()","Connection state is "+this.state.state+"; waiting until either connected or failed");const s=n=>{switch(n.current){case"connected":this.off(s),t(null,e);break;case"failed":case"closed":case"suspended":this.off(s),t(n.reason||this.getStateError())}};this.on("connectionstate",s),"connecting"===this.state.state?this.startConnect():this.requestState({state:"connecting"})}}}disconnectAllTransports(){_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.disconnectAllTransports()","Disconnecting all transports"),this.connectCounter++,this.pendingTransport&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.disconnectAllTransports()","Disconnecting pending transport: "+this.pendingTransport),this.pendingTransport.disconnect()),delete this.pendingTransport,this.proposedTransport&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.disconnectAllTransports()","Disconnecting proposed transport: "+this.pendingTransport),this.proposedTransport.disconnect()),delete this.pendingTransport,this.activeProtocol&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.disconnectAllTransports()","Disconnecting active transport: "+this.activeProtocol.getTransport()),this.activeProtocol.getTransport().disconnect())}send(e,t,s){s=s||Bs;const n=this.state;if(n.sendEvents)return _.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.send()","sending event"),void this.sendImpl(new Ss(e,s));if(!t||!n.queueEvents){const e="rejecting event, queueEvent was "+t+", state was "+n.state;return _.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.send()",e),void s(this.errorReason||new R(e,9e4,400))}this.logger.shouldLog(_.LOG_MICRO)&&_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.send()","queueing msg; "+fs(e,this.realtime._RealtimePresence,this.realtime._Annotations,this.realtime._objectsPlugin)),this.queue(e,s)}sendImpl(e){const t=e.message;e.ackRequired&&!e.sendAttempted&&(t.msgSerial=this.msgSerial++);try{this.activeProtocol.send(e)}catch(s){_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.sendImpl()","Unexpected exception in transport.send(): "+s.stack)}}queue(e,t){_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.queue()","queueing event");const s=this.queuedMessages.last(),n=this.options.maxMessageSize;s&&!s.sendAttempted&&function(e,t,s){let n;if(e.channel!==t.channel)return!1;if((n=e.action)!==ze.PRESENCE&&n!==ze.MESSAGE)return!1;if(n!==t.action)return!1;const i=n===ze.PRESENCE?"presence":"messages",a=e[i].concat(t[i]);return!(Bt(a)>s||!F(a,"clientId")||!a.every(function(e){return!e.id})||(e[i]=a,0))}(s.message,e,n)?(s.merged||(s.callback=Ie.create(this.logger,[s.callback]),s.merged=!0),s.callback.push(t)):this.queuedMessages.push(new Ss(e,t))}sendQueuedMessages(){let e;for(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.sendQueuedMessages()","sending "+this.queuedMessages.count()+" queued messages");e=this.queuedMessages.shift();)this.sendImpl(e)}queuePendingMessages(e){e&&e.length&&(_.logAction(this.logger,_.LOG_MICRO,"ConnectionManager.queuePendingMessages()","queueing "+e.length+" pending messages"),this.queuedMessages.prepend(e))}failQueuedMessages(e){const t=this.queuedMessages.count();t>0&&(_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.failQueuedMessages()","failing "+t+" queued messages, err = "+Q(e)),this.queuedMessages.completeAllMessages(e))}onChannelMessage(e,t){this.pendingChannelMessagesState.queue.push({message:e,transport:t}),this.pendingChannelMessagesState.isProcessing||this.processNextPendingChannelMessage()}processNextPendingChannelMessage(){if(this.pendingChannelMessagesState.queue.length>0){this.pendingChannelMessagesState.isProcessing=!0;const e=this.pendingChannelMessagesState.queue.shift();this.processChannelMessage(e.message).catch(e=>{_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.processNextPendingChannelMessage() received error ",e)}).finally(()=>{this.pendingChannelMessagesState.isProcessing=!1,this.processNextPendingChannelMessage()})}}async processChannelMessage(e){await this.realtime.channels.processChannelMessage(e)}async ping(){var e;if("connected"!==this.state.state)throw new R("Unable to ping service; not connected",4e4,400);const t=null==(e=this.activeProtocol)?void 0:e.getTransport();if(!t)throw this.getStateError();_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.ping()","transport = "+t);const s=Date.now(),n=ee();return ye(new Promise(e=>{const i=a=>{a===n&&(t.off("heartbeat",i),e(Date.now()-s))};t.on("heartbeat",i),t.ping(n)}),this.options.timeouts.realtimeRequestTimeout,"Timeout waiting for heartbeat response")}abort(e){this.activeProtocol.getTransport().fail(e)}getTransportPreference(){var e,t;return this.transportPreference||Ds()&&(null==(t=null==(e=m.WebStorage)?void 0:e.get)?void 0:t.call(e,qs))}persistTransportPreference(e){var t,s;this.transportPreference=e.shortName,Ds()&&(null==(s=null==(t=m.WebStorage)?void 0:t.set)||s.call(t,qs,e.shortName))}unpersistTransportPreference(){var e,t;this.transportPreference=null,Ds()&&(null==(t=null==(e=m.WebStorage)?void 0:e.remove)||t.call(e,qs))}actOnErrorFromAuthorize(e){if(40171===e.code)this.notifyState({state:"failed",error:e});else if(40102===e.code)this.notifyState({state:"failed",error:e});else if(e.statusCode===Ne.Forbidden){const t="Client configured authentication provider returned 403; failing the connection";_.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.actOnErrorFromAuthorize()",t),this.notifyState({state:"failed",error:new R(t,80019,403,e)})}else{const t="Client configured authentication provider request failed";_.logAction(this.logger,_.LOG_MINOR,"ConnectionManager.actOnErrorFromAuthorize",t),this.notifyState({state:this.state.failState,error:new R(t,80019,401,e)})}}onConnectionDetailsUpdate(e,t){if(!e)return;this.connectionDetails=e,e.maxMessageSize&&(this.options.maxMessageSize=e.maxMessageSize);const s=e.clientId;if(s){const e=this.realtime.auth._uncheckedSetClientId(s);if(e)return _.logAction(this.logger,_.LOG_ERROR,"ConnectionManager.onConnectionDetailsUpdate()",e.message),void t.fail(e)}const n=e.connectionStateTtl;n&&(this.connectionStateTtl=n),this.maxIdleInterval=e.maxIdleInterval,this.emit("connectiondetails",e)}checkWsConnectivity(){const e=this.options.wsConnectivityCheckUrl||Ae.wsConnectivityCheckUrl,t=new m.Config.WebSocket(e);return new Promise((e,s)=>{let n=!1;t.onopen=()=>{n||(n=!0,e(),t.close())},t.onclose=t.onerror=()=>{n||(n=!0,s())}})}sessionRecoveryName(){return this.options.recoveryKeyStorageName||"ably-connection-recovery"}getSessionRecoverData(){var e,t;return xs()&&(null==(t=null==(e=m.WebStorage)?void 0:e.getSession)?void 0:t.call(e,this.sessionRecoveryName()))}setSessionRecoverData(e){var t,s;return xs()&&(null==(s=null==(t=m.WebStorage)?void 0:t.setSession)?void 0:s.call(t,this.sessionRecoveryName(),e))}clearSessionRecoverData(){var e,t;return xs()&&(null==(t=null==(e=m.WebStorage)?void 0:e.removeSession)?void 0:t.call(e,this.sessionRecoveryName()))}},js=class extends We{constructor(e,t){super(e.logger),this.whenState=e=>We.prototype.whenState.call(this,e,this.state),this.ably=e,this.connectionManager=new Gs(e,t),this.state=this.connectionManager.state.state,this.key=void 0,this.id=void 0,this.errorReason=null,this.connectionManager.on("connectionstate",e=>{const t=this.state=e.current;m.Config.nextTick(()=>{this.emit(t,e)})}),this.connectionManager.on("update",e=>{m.Config.nextTick(()=>{this.emit("update",e)})})}connect(){_.logAction(this.logger,_.LOG_MINOR,"Connection.connect()",""),this.connectionManager.requestState({state:"connecting"})}async ping(){return _.logAction(this.logger,_.LOG_MINOR,"Connection.ping()",""),this.connectionManager.ping()}close(){_.logAction(this.logger,_.LOG_MINOR,"Connection.close()","connectionKey = "+this.key),this.connectionManager.requestState({state:"closing"})}get recoveryKey(){return this.logger.deprecationWarning("The `Connection.recoveryKey` attribute has been replaced by the `Connection.createRecoveryKey()` method. Replace your usage of `recoveryKey` with the return value of `createRecoveryKey()`. `recoveryKey` will be removed in a future version."),this.createRecoveryKey()}createRecoveryKey(){return this.connectionManager.createRecoveryKey()}},$s=class e extends dt{constructor(t){var s,n,i,a;if(super(Ae.objectifyOptions(t,!1,"BaseRealtime",_.defaultLogger)),_.logAction(this.logger,_.LOG_MINOR,"Realtime()",""),"string"==typeof EdgeRuntime)throw new R('Ably.Realtime instance cannot be used in Vercel Edge runtime. If you are running Vercel Edge functions, please replace your "new Ably.Realtime()" with "new Ably.Rest()" and use Ably Rest API instead of the Realtime API. If you are server-rendering your application in the Vercel Edge runtime, please use the condition "if (typeof EdgeRuntime === \'string\')" to prevent instantiating Ably.Realtime instance during SSR in the Vercel Edge runtime.',4e4,400);this._additionalTransportImplementations=e.transportImplementationsFromPlugins(this.options.plugins),this._RealtimePresence=null!=(n=null==(s=this.options.plugins)?void 0:s.RealtimePresence)?n:null,this._objectsPlugin=null!=(a=null==(i=this.options.plugins)?void 0:i.Objects)?a:null,this.connection=new js(this,this.options),this._channels=new Ws(this),!1!==this.options.autoConnect&&this.connect()}static transportImplementationsFromPlugins(e){const t={};return(null==e?void 0:e.WebSocketTransport)&&(t[Cs.WebSocket]=e.WebSocketTransport),(null==e?void 0:e.XHRPolling)&&(t[Cs.XhrPolling]=e.XHRPolling),t}get channels(){return this._channels}connect(){_.logAction(this.logger,_.LOG_MINOR,"Realtime.connect()",""),this.connection.connect()}close(){_.logAction(this.logger,_.LOG_MINOR,"Realtime.close()",""),this.connection.close()}};$s.EventEmitter=We;var Fs=$s,Ws=class extends We{constructor(e){super(e.logger),this.realtime=e,this.all=Object.create(null),e.connection.connectionManager.on("transport.active",()=>{this.onTransportActive()})}channelSerials(){let e={};for(const t of G(this.all,!0)){const s=this.all[t];s.properties.channelSerial&&(e[t]=s.properties.channelSerial)}return e}recoverChannels(e){for(const t of G(e,!0))this.get(t).properties.channelSerial=e[t]}async processChannelMessage(e){const t=e.channel;if(void 0===t)return void _.logAction(this.logger,_.LOG_ERROR,"Channels.processChannelMessage()","received event unspecified channel, action = "+e.action);const s=this.all[t];s?await s.processMessage(e):_.logAction(this.logger,_.LOG_ERROR,"Channels.processChannelMessage()","received event for non-existent channel: "+t)}onTransportActive(){for(const e in this.all){const t=this.all[e];"attaching"===t.state||"detaching"===t.state?t.checkPendingState():"suspended"===t.state?t._attach(!1,null):"attached"===t.state&&t.requestState("attaching")}}propogateConnectionInterruption(e,t){const s=["attaching","attached","detaching","suspended"],n={closing:"detached",closed:"detached",failed:"failed",suspended:"suspended"}[e];for(const i in this.all){const e=this.all[i];s.includes(e.state)&&e.notifyState(n,t)}}get(e,t){e=String(e);let s=this.all[e];if(s){if(t){if(s._shouldReattachToSetOptions(t,s.channelOptions))throw new R("Channels.get() cannot be used to set channel options that would cause the channel to reattach. Please, use RealtimeChannel.setOptions() instead.",4e4,400);s.setOptions(t)}}else s=this.all[e]=new ws(this.realtime,e,t);return s}getDerived(e,t,s){if(t.filter){const s=fe(t.filter),n=pe(e);e=`[filter=${s}${n.qualifierParam}]${n.channelName}`}return this.get(e,s)}release(e){e=String(e);const t=this.all[e];if(!t)return;const s=t.getReleaseErr();if(s)throw s;delete this.all[e]}},zs=Fs;function Js(e,t){if(e.isSynthesized()||t.isSynthesized())return e.timestamp>=t.timestamp;const s=e.parseId(),n=t.parseId();return s.msgSerial===n.msgSerial?s.index>n.index:s.msgSerial>n.msgSerial}var Ks=class extends We{constructor(e,t,s=Js){super(e.logger),this.presence=e,this.map=Object.create(null),this.syncInProgress=!1,this.residualMembers=null,this.memberKey=t,this.newerThan=s}get(e){return this.map[e]}getClient(e){const t=this.map,s=[];for(const n in t){const i=t[n];i.clientId==e&&"absent"!=i.action&&s.push(i)}return s}list(e){const t=this.map,s=e&&e.clientId,n=e&&e.connectionId,i=[];for(const a in t){const e=t[a];"absent"!==e.action&&(s&&s!=e.clientId||n&&n!=e.connectionId||i.push(e))}return i}put(e){"enter"!==e.action&&"update"!==e.action||((e=It.fromValues(e)).action="present");const t=this.map,s=this.memberKey(e);this.residualMembers&&delete this.residualMembers[s];const n=t[s];return!(n&&!this.newerThan(e,n)||(t[s]=e,0))}values(){const e=this.map,t=[];for(const s in e){const n=e[s];"absent"!=n.action&&t.push(n)}return t}remove(e){const t=this.map,s=this.memberKey(e),n=t[s];return!(n&&!this.newerThan(e,n)||(this.syncInProgress?((e=It.fromValues(e)).action="absent",t[s]=e):delete t[s],!n))}startSync(){const e=this.map,t=this.syncInProgress;_.logAction(this.logger,_.LOG_MINOR,"PresenceMap.startSync()","channel = "+this.presence.channel.name+"; syncInProgress = "+t),this.syncInProgress||(this.residualMembers=O(e),this.setInProgress(!0))}endSync(){const e=this.map,t=this.syncInProgress;if(_.logAction(this.logger,_.LOG_MINOR,"PresenceMap.endSync()","channel = "+this.presence.channel.name+"; syncInProgress = "+t),t){for(const t in e)"absent"===e[t].action&&delete e[t];this.presence._synthesizeLeaves(j(this.residualMembers));for(const t in this.residualMembers)delete e[t];this.residualMembers=null,this.setInProgress(!1)}this.emit("sync")}waitSync(e){const t=this.syncInProgress;_.logAction(this.logger,_.LOG_MINOR,"PresenceMap.waitSync()","channel = "+this.presence.channel.name+"; syncInProgress = "+t),t?this.once("sync",e):e()}clear(){this.map={},this.setInProgress(!1),this.residualMembers=null}setInProgress(e){_.logAction(this.logger,_.LOG_MICRO,"PresenceMap.setInProgress()","inProgress = "+e),this.syncInProgress=e,this.presence.syncComplete=!e}};function Ys(e){const t=e.channel.client,s=t.auth.clientId;return(!s||"*"===s)&&"connected"===t.connection.state}var Qs=class extends We{constructor(e){super(e.logger),this.channel=e,this.syncComplete=!1,this.members=new Ks(this,e=>e.clientId+":"+e.connectionId),this._myMembers=new Ks(this,e=>e.clientId),this.subscriptions=new We(this.logger),this.pendingPresence=[]}async enter(e){if(Ys(this))throw new R("clientId must be specified to enter a presence channel",40012,400);return this._enterOrUpdateClient(void 0,void 0,e,"enter")}async update(e){if(Ys(this))throw new R("clientId must be specified to update presence data",40012,400);return this._enterOrUpdateClient(void 0,void 0,e,"update")}async enterClient(e,t){return this._enterOrUpdateClient(void 0,e,t,"enter")}async updateClient(e,t){return this._enterOrUpdateClient(void 0,e,t,"update")}async _enterOrUpdateClient(e,t,s,n){const i=this.channel;if(!i.connectionManager.activeState())throw i.connectionManager.getError();_.logAction(this.logger,_.LOG_MICRO,"RealtimePresence."+n+"Client()","channel = "+i.name+", id = "+e+", client = "+(t||"(implicit) "+this.channel.client.auth.clientId));const a=It.fromData(s);a.action=n,e&&(a.id=e),t&&(a.clientId=t);const r=await a.encode(i.channelOptions);switch(i.state){case"attached":return i.sendPresence([r]);case"initialized":case"detached":i.attach();case"attaching":return new Promise((e,t)=>{this.pendingPresence.push({presence:r,callback:s=>s?t(s):e()})});default:{const e=new T("Unable to "+n+" presence channel while in "+i.state+" state",90001);throw e.code=90001,e}}}async leave(e){if(Ys(this))throw new R("clientId must have been specified to enter or leave a presence channel",40012,400);return this.leaveClient(void 0,e)}async leaveClient(e,t){const s=this.channel;if(!s.connectionManager.activeState())throw s.connectionManager.getError();_.logAction(this.logger,_.LOG_MICRO,"RealtimePresence.leaveClient()","leaving; channel = "+this.channel.name+", client = "+e);const n=It.fromData(t);n.action="leave",e&&(n.clientId=e);const i=await n.encode(s.channelOptions);switch(s.state){case"attached":return s.sendPresence([i]);case"attaching":return new Promise((e,t)=>{this.pendingPresence.push({presence:i,callback:s=>s?t(s):e()})});case"initialized":case"failed":throw new T("Unable to leave presence channel (incompatible state)",90001);default:throw s.invalidStateError()}}async get(e){const t=!e||!("waitForSync"in e)||e.waitForSync;return new Promise((s,n)=>{function i(t){s(e?t.list(e):t.values())}"suspended"!==this.channel.state?function(e,t,s){switch(e.state){case"attached":case"suspended":s();break;case"initialized":case"detached":case"detaching":case"attaching":ne(e.attach(),function(e){e?t(e):s()});break;default:t(R.fromValues(e.invalidStateError()))}}(this.channel,e=>n(e),()=>{const e=this.members;t?e.waitSync(function(){i(e)}):i(e)}):t?n(R.fromValues({statusCode:400,code:91005,message:"Presence state is out of sync due to channel being in the SUSPENDED state"})):i(this.members)})}async history(e){_.logAction(this.logger,_.LOG_MICRO,"RealtimePresence.history()","channel = "+this.name);const t=this.channel.client.rest.presenceMixin;if(e&&e.untilAttach){if("attached"!==this.channel.state)throw new R("option untilAttach requires the channel to be attached, was: "+this.channel.state,4e4,400);delete e.untilAttach,e.from_serial=this.channel.properties.attachSerial}return t.history(this,e)}setPresence(e,t,s){let n,i;_.logAction(this.logger,_.LOG_MICRO,"RealtimePresence.setPresence()","received presence for "+e.length+" participants; syncChannelSerial = "+s);const a=this.members,r=this._myMembers,o=[],l=this.channel.connectionManager.connectionId;t&&(this.members.startSync(),s&&(i=s.match(/^[\w-]+:(.*)$/))&&(n=i[1]));for(let c of e)switch(c.action){case"leave":a.remove(c)&&o.push(c),c.connectionId!==l||c.isSynthesized()||r.remove(c);break;case"enter":case"present":case"update":a.put(c)&&o.push(c),c.connectionId===l&&r.put(c)}t&&!n&&(a.endSync(),this.channel.syncChannelSerial=null);for(let c=0;c<o.length;c++){const e=o[c];this.subscriptions.emit(e.action,e)}}onAttached(e){_.logAction(this.logger,_.LOG_MINOR,"RealtimePresence.onAttached()","channel = "+this.channel.name+", hasPresence = "+e),e?this.members.startSync():(this._synthesizeLeaves(this.members.values()),this.members.clear()),this._ensureMyMembersPresent();const t=this.pendingPresence,s=t.length;if(s){this.pendingPresence=[];const e=[],n=Ie.create(this.logger);_.logAction(this.logger,_.LOG_MICRO,"RealtimePresence.onAttached","sending "+s+" queued presence messages");for(let i=0;i<s;i++){const s=t[i];e.push(s.presence),n.push(s.callback)}this.channel.sendPresence(e).then(()=>n()).catch(e=>n(e))}}actOnChannelState(e,t,s){switch(e){case"attached":this.onAttached(t);break;case"detached":case"failed":this._clearMyMembers(),this.members.clear();case"suspended":this.failPendingPresence(s)}}failPendingPresence(e){if(this.pendingPresence.length){_.logAction(this.logger,_.LOG_MINOR,"RealtimeChannel.failPendingPresence","channel; name = "+this.channel.name+", err = "+Q(e));for(let s=0;s<this.pendingPresence.length;s++)try{this.pendingPresence[s].callback(e)}catch(t){}this.pendingPresence=[]}}_clearMyMembers(){this._myMembers.clear()}_ensureMyMembersPresent(){const e=this._myMembers,t=this.channel.connectionManager.connectionId;for(const s in e.map){const n=e.map[s];_.logAction(this.logger,_.LOG_MICRO,"RealtimePresence._ensureMyMembersPresent()",'Auto-reentering clientId "'+n.clientId+'" into the presence set');const i=n.connectionId===t?n.id:void 0;this._enterOrUpdateClient(i,n.clientId,n.data,"enter").catch(e=>{const t=new R("Presence auto re-enter failed",91004,400,e);_.logAction(this.logger,_.LOG_ERROR,"RealtimePresence._ensureMyMembersPresent()","Presence auto re-enter failed; reason = "+Q(e));const s=new ms(this.channel.state,this.channel.state,!0,!1,t);this.channel.emit("update",s)})}}_synthesizeLeaves(e){const t=this.subscriptions;e.forEach(function(e){const s=It.fromValues({action:"leave",connectionId:e.connectionId,clientId:e.clientId,data:e.data,encoding:e.encoding,timestamp:Date.now()});t.emit("leave",s)})}async subscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1],i=this.channel;if("failed"===i.state)throw R.fromValues(i.invalidStateError());this.subscriptions.on(s,n),!1!==i.channelOptions.attachOnSubscribe&&await i.attach()}unsubscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1];this.subscriptions.off(s,n)}},Xs=Cs.WebSocket,Zs=class extends Ns{constructor(e,t,s){super(e,t,s),this.shortName=Xs,s.heartbeats=m.Config.useProtocolHeartbeats,this.wsHost=s.host}static isAvailable(){return!!m.Config.WebSocket}createWebSocket(e,t){return this.uri=e+J(t),new m.Config.WebSocket(this.uri)}toString(){return"WebSocketTransport; uri="+this.uri}connect(){_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.connect()","starting"),Ns.prototype.connect.call(this);const e=this,t=this.params,s=t.options,n=(s.tls?"wss://":"ws://")+this.wsHost+":"+Ae.getPort(s)+"/";_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.connect()","uri: "+n),ne(this.auth.getAuthParams(),function(s,i){if(e.isDisposed)return;let a="";for(const e in i)a+=" "+e+": "+i[e]+";";if(_.logAction(e.logger,_.LOG_MINOR,"WebSocketTransport.connect()","authParams:"+a+" err: "+s),s)return void e.disconnect(s);const r=t.getConnectParams(i);try{const t=e.wsConnection=e.createWebSocket(n,r);t.binaryType=m.Config.binaryType,t.onopen=function(){e.onWsOpen()},t.onclose=function(t){e.onWsClose(t)},t.onmessage=function(t){e.onWsData(t.data)},t.onerror=function(t){e.onWsError(t)},t.on&&t.on("ping",function(){e.onActivity()})}catch(o){_.logAction(e.logger,_.LOG_ERROR,"WebSocketTransport.connect()","Unexpected exception creating websocket: err = "+(o.stack||o.message)),e.disconnect(o)}})}send(e){const t=this.wsConnection;if(t)try{t.send(cs(e,this.connectionManager.realtime._MsgPack,this.params.format))}catch(s){const e="Exception from ws connection when trying to send: "+Q(s);_.logAction(this.logger,_.LOG_ERROR,"WebSocketTransport.send()",e),this.finish("disconnected",new R(e,5e4,500))}else _.logAction(this.logger,_.LOG_ERROR,"WebSocketTransport.send()","No socket connection")}onWsData(e){_.logAction(this.logger,_.LOG_MICRO,"WebSocketTransport.onWsData()","data received; length = "+e.length+"; type = "+typeof e);try{this.onProtocolMessage((t=e,s=this.connectionManager.realtime._MsgPack,n=this.connectionManager.realtime._RealtimePresence,i=this.connectionManager.realtime._Annotations,a=this.connectionManager.realtime._objectsPlugin,r=this.format,ds(ie(t,s,r),n,i,a)))}catch(o){_.logAction(this.logger,_.LOG_ERROR,"WebSocketTransport.onWsData()","Unexpected exception handing channel message: "+o.stack)}var t,s,n,i,a,r}onWsOpen(){_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.onWsOpen()","opened WebSocket"),this.emit("preconnect")}onWsClose(e){let t,s;if("object"==typeof e?(s=e.code,t=e.wasClean||1e3===s):(s=e,t=1e3==s),delete this.wsConnection,t){_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.onWsClose()","Cleanly closed WebSocket");const e=new R("Websocket closed",80003,400);this.finish("disconnected",e)}else{const e="Unclean disconnection of WebSocket ; code = "+s,t=new R(e,80003,400);_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.onWsClose()",e),this.finish("disconnected",t)}this.emit("disposed")}onWsError(e){_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.onError()","Error from WebSocket: "+e.message),m.Config.nextTick(()=>{this.disconnect(Error(e.message))})}dispose(){_.logAction(this.logger,_.LOG_MINOR,"WebSocketTransport.dispose()",""),this.isDisposed=!0;const e=this.wsConnection;e&&(e.onmessage=function(){},delete this.wsConnection,m.Config.nextTick(()=>{if(_.logAction(this.logger,_.LOG_MICRO,"WebSocketTransport.dispose()","closing websocket"),!e)throw new Error("WebSocketTransport.dispose(): wsConnection is not defined");e.close()}))}},en=class{static subscribeFilter(e,t,s){const n=e=>{var n,i,a,r,o,l;const c={name:e.name,refTimeserial:null==(i=null==(n=e.extras)?void 0:n.ref)?void 0:i.timeserial,refType:null==(r=null==(a=e.extras)?void 0:a.ref)?void 0:r.type,isRef:!!(null==(l=null==(o=e.extras)?void 0:o.ref)?void 0:l.timeserial),clientId:e.clientId};Object.entries(t).find(([e,t])=>void 0!==t&&c[e]!==t)||s(e)};this.addFilteredSubscription(e,t,s,n),e.subscriptions.on(n)}static addFilteredSubscription(e,t,s,n){var i;if(e.filteredSubscriptions||(e.filteredSubscriptions=new Map),e.filteredSubscriptions.has(s)){const a=e.filteredSubscriptions.get(s);a.set(t,(null==(i=null==a?void 0:a.get(t))?void 0:i.concat(n))||[n])}else e.filteredSubscriptions.set(s,new Map([[t,[n]]]))}static getAndDeleteFilteredSubscriptions(e,t,s){if(!e.filteredSubscriptions)return[];if(!s&&t)return Array.from(e.filteredSubscriptions.entries()).map(([s,n])=>{var i;let a=n.get(t);return n.delete(t),0===n.size&&(null==(i=e.filteredSubscriptions)||i.delete(s)),a}).reduce((e,t)=>t?e.concat(...t):e,[]);if(!s||!e.filteredSubscriptions.has(s))return[];const n=e.filteredSubscriptions.get(s);if(!t){const t=Array.from(n.values()).reduce((e,t)=>e.concat(...t),[]);return e.filteredSubscriptions.delete(s),t}let i=n.get(t);return n.delete(t),i||[]}},tn=class e extends zs{constructor(t){var s;const n=e._MsgPack;if(!n)throw new Error("Expected DefaultRealtime._MsgPack to have been set");super(Ae.objectifyOptions(t,!0,"Realtime",_.defaultLogger,f(p({},Kt),{Crypto:null!=(s=e.Crypto)?s:void 0,MsgPack:n,RealtimePresence:{RealtimePresence:Qs,PresenceMessage:It,WirePresenceMessage:At},Annotations:{Annotation:ns,WireAnnotation:ss,RealtimeAnnotations:ks,RestAnnotations:ls},WebSocketTransport:Zs,MessageInteractions:en})))}static get Crypto(){if(null===this._Crypto)throw new Error("Encryption not enabled; use ably.encryption.js instead");return this._Crypto}static set Crypto(e){this._Crypto=e}};tn.Utils=C,tn.ConnectionManager=Gs,tn.ProtocolMessage=vs,tn._Crypto=null,tn.Message=Yt,tn.PresenceMessage=Qt,tn.Annotation=is,tn._MsgPack=null,tn._Http=$e,tn._PresenceMap=Ks,tn._MessageEncoding=rt;var sn=tn,nn=Uint8Array,an=Uint32Array,rn=Math.pow,on=new an(8),ln=[],cn=new an(64);function un(e){return(e-(0|e))*rn(2,32)|0}for(var dn,hn,pn=2,fn=0;fn<64;){for(dn=!0,hn=2;hn<=pn/2;hn++)pn%hn===0&&(dn=!1);dn&&(fn<8&&(on[fn]=un(rn(pn,.5))),ln[fn]=un(rn(pn,1/3)),fn++),pn++}var gn=!!new nn(new an([1]).buffer)[0];function vn(e){return gn?e>>>24|(e>>>16&255)<<8|(65280&e)<<8|e<<24:e}function mn(e,t){return e>>>t|e<<32-t}function yn(e){var t,s=on.slice(),n=e.length,i=8*n,a=512-(i+64)%512-1+i+65,r=new nn(a/8),o=new an(r.buffer);r.set(e,0),r[n]=128,o[o.length-1]=vn(i);for(var l=0;l<a/32;l+=16){var c=s.slice();for(t=0;t<64;t++){var u;if(t<16)u=vn(o[l+t]);else{var d=cn[t-15],h=cn[t-2];u=cn[t-7]+cn[t-16]+(mn(d,7)^mn(d,18)^d>>>3)+(mn(h,17)^mn(h,19)^h>>>10)}cn[t]=u|=0;for(var p=(mn(c[4],6)^mn(c[4],11)^mn(c[4],25))+(c[4]&c[5]^~c[4]&c[6])+c[7]+u+ln[t],f=(mn(c[0],2)^mn(c[0],13)^mn(c[0],22))+(c[0]&c[1]^c[2]&(c[0]^c[1])),g=7;g>0;g--)c[g]=c[g-1];c[0]=p+f|0,c[4]=c[4]+p|0}for(t=0;t<8;t++)s[t]=s[t]+c[t]|0}return new nn(new an(s.map(function(e){return vn(e)})).buffer)}var bn,wn=new class{constructor(){this.base64CharSet="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",this.hexCharSet="0123456789abcdef"}uint8ViewToBase64(e){let t="";const s=this.base64CharSet,n=e.byteLength,i=n%3,a=n-i;let r,o,l,c,u;for(let d=0;d<a;d+=3)u=e[d]<<16|e[d+1]<<8|e[d+2],r=(16515072&u)>>18,o=(258048&u)>>12,l=(4032&u)>>6,c=63&u,t+=s[r]+s[o]+s[l]+s[c];return 1==i?(u=e[a],r=(252&u)>>2,o=(3&u)<<4,t+=s[r]+s[o]+"=="):2==i&&(u=e[a]<<8|e[a+1],r=(64512&u)>>10,o=(1008&u)>>4,l=(15&u)<<2,t+=s[r]+s[o]+s[l]+"="),t}base64ToArrayBuffer(e){const t=null==atob?void 0:atob(e),s=t.length,n=new Uint8Array(s);for(let i=0;i<s;i++){const e=t.charCodeAt(i);n[i]=e}return this.toArrayBuffer(n)}isBuffer(e){return e instanceof ArrayBuffer||ArrayBuffer.isView(e)}toBuffer(e){if(!ArrayBuffer)throw new Error("Can't convert to Buffer: browser does not support the necessary types");if(e instanceof ArrayBuffer)return new Uint8Array(e);if(ArrayBuffer.isView(e))return new Uint8Array(this.toArrayBuffer(e));throw new Error("BufferUtils.toBuffer expected an ArrayBuffer or a view onto one")}toArrayBuffer(e){if(!ArrayBuffer)throw new Error("Can't convert to ArrayBuffer: browser does not support the necessary types");if(e instanceof ArrayBuffer)return e;if(ArrayBuffer.isView(e))return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength);throw new Error("BufferUtils.toArrayBuffer expected an ArrayBuffer or a view onto one")}base64Encode(e){return this.uint8ViewToBase64(this.toBuffer(e))}base64UrlEncode(e){return this.base64Encode(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}base64Decode(e){if(ArrayBuffer&&m.Config.atob)return this.base64ToArrayBuffer(e);throw new Error("Expected ArrayBuffer to exist and Platform.Config.atob to be configured")}hexEncode(e){return this.toBuffer(e).reduce((e,t)=>e+t.toString(16).padStart(2,"0"),"")}hexDecode(e){if(e.length%2!=0)throw new Error("Can't create a byte array from a hex string of odd length");const t=new Uint8Array(e.length/2);for(let s=0;s<t.length;s++)t[s]=parseInt(e.slice(2*s,2*(s+1)),16);return this.toArrayBuffer(t)}utf8Encode(e){if(m.Config.TextEncoder){const t=(new m.Config.TextEncoder).encode(e);return this.toArrayBuffer(t)}throw new Error("Expected TextEncoder to be configured")}utf8Decode(e){if(!this.isBuffer(e))throw new Error("Expected input of utf8decode to be an arraybuffer or typed array");if(TextDecoder)return(new TextDecoder).decode(e);throw new Error("Expected TextDecoder to be configured")}areBuffersEqual(e,t){if(!e||!t)return!1;const s=this.toArrayBuffer(e),n=this.toArrayBuffer(t);if(s.byteLength!=n.byteLength)return!1;const i=new Uint8Array(s),a=new Uint8Array(n);for(var r=0;r<i.length;r++)if(i[r]!=a[r])return!1;return!0}byteLength(e){return e instanceof ArrayBuffer||ArrayBuffer.isView(e)?e.byteLength:-1}arrayBufferViewToBuffer(e){return this.toArrayBuffer(e)}concat(e){const t=e.reduce((e,t)=>e+t.byteLength,0),s=new Uint8Array(t);let n=0;for(const i of e){const e=this.toBuffer(i);s.set(e,n),n+=e.byteLength}return s.buffer}sha256(e){const t=yn(this.toBuffer(e));return this.toArrayBuffer(t)}hmacSha256(e,t){const s=function(e,t){if(e.length>64&&(e=yn(e)),e.length<64){const t=new Uint8Array(64);t.set(e,0),e=t}for(var s=new Uint8Array(64),n=new Uint8Array(64),i=0;i<64;i++)s[i]=54^e[i],n[i]=92^e[i];var a=new Uint8Array(t.length+64);a.set(s,0),a.set(t,64);var r=new Uint8Array(96);return r.set(n,0),r.set(yn(a),64),yn(r)}(this.toBuffer(t),this.toBuffer(e));return this.toArrayBuffer(s)}},kn=(e=>(e[e.REQ_SEND=0]="REQ_SEND",e[e.REQ_RECV=1]="REQ_RECV",e[e.REQ_RECV_POLL=2]="REQ_RECV_POLL",e[e.REQ_RECV_STREAM=3]="REQ_RECV_STREAM",e))(kn||{}),_n=kn;function Cn(){return new R("No HTTP request plugin provided. Provide at least one of the FetchRequest or XHRRequest plugins.",400,4e4)}var En=((bn=class{constructor(e){var t;this.checksInProgress=null,this.checkConnectivity=void 0,this.supportsAuthHeaders=!1,this.supportsLinkHeaders=!1,this.client=null!=e?e:null;const s=(null==e?void 0:e.options.connectivityCheckUrl)||Ae.connectivityCheckUrl,n=null!=(t=null==e?void 0:e.options.connectivityCheckParams)?t:null,i=!(null==e?void 0:e.options.connectivityCheckUrl),a=p(p({},En.bundledRequestImplementations),null==e?void 0:e._additionalHTTPRequestImplementations),r=a.XHRRequest,o=a.FetchRequest,l=!(!r&&!o);if(!l)throw Cn();m.Config.xhrSupported&&r?(this.supportsAuthHeaders=!0,this.Request=async function(t,s,n,i,a){return new Promise(o=>{var l;const c=r.createRequest(s,n,i,a,_n.REQ_SEND,null!=(l=e&&e.options.timeouts)?l:null,this.logger,t);c.once("complete",(e,t,s,n,i)=>o({error:e,body:t,headers:s,unpacked:n,statusCode:i})),c.exec()})},(null==e?void 0:e.options.disableConnectivityCheck)?this.checkConnectivity=async function(){return!0}:this.checkConnectivity=async function(){var e;_.logAction(this.logger,_.LOG_MICRO,"(XHRRequest)Http.checkConnectivity()","Sending; "+s);const t=await this.doUri(Le.Get,s,null,null,n);let a=!1;var r;return a=i?!t.error&&"yes"==(null==(e=t.body)?void 0:e.replace(/\n/,"")):!t.error&&(r=t.statusCode)>=200&&r<400,_.logAction(this.logger,_.LOG_MICRO,"(XHRRequest)Http.checkConnectivity()","Result: "+a),a}):m.Config.fetchSupported&&o?(this.supportsAuthHeaders=!0,this.Request=async(t,s,n,i,a)=>o(t,null!=e?e:null,s,n,i,a),(null==e?void 0:e.options.disableConnectivityCheck)?this.checkConnectivity=async function(){return!0}:this.checkConnectivity=async function(){var e;_.logAction(this.logger,_.LOG_MICRO,"(Fetch)Http.checkConnectivity()","Sending; "+s);const t=await this.doUri(Le.Get,s,null,null,null),n=!t.error&&"yes"==(null==(e=t.body)?void 0:e.replace(/\n/,""));return _.logAction(this.logger,_.LOG_MICRO,"(Fetch)Http.checkConnectivity()","Result: "+n),n}):this.Request=async()=>({error:l?new T("no supported HTTP transports available",null,400):Cn()})}get logger(){var e,t;return null!=(t=null==(e=this.client)?void 0:e.logger)?t:_.defaultLogger}async doUri(e,t,s,n,i){return this.Request?this.Request(e,t,s,i,n):{error:new T("Request invoked before assigned to",null,500)}}shouldFallback(e){const t=e.statusCode;return 408===t&&!e.code||400===t&&!e.code||t>=500&&t<=504}}).methods=[Le.Get,Le.Delete,Le.Post,Le.Put,Le.Patch],bn.methodsWithoutBody=[Le.Get,Le.Delete],bn.methodsWithBody=[Le.Post,Le.Put,Le.Patch],bn),Rn=En,Tn="ablyjs-storage-test",Sn=void 0!==s?s:"undefined"!=typeof window?window:self,On=new class{constructor(){try{Sn.sessionStorage.setItem(Tn,Tn),Sn.sessionStorage.removeItem(Tn),this.sessionSupported=!0}catch(e){this.sessionSupported=!1}try{Sn.localStorage.setItem(Tn,Tn),Sn.localStorage.removeItem(Tn),this.localSupported=!0}catch(e){this.localSupported=!1}}get(e){return this._get(e,!1)}getSession(e){return this._get(e,!0)}remove(e){return this._remove(e,!1)}removeSession(e){return this._remove(e,!0)}set(e,t,s){return this._set(e,t,s,!1)}setSession(e,t,s){return this._set(e,t,s,!0)}_set(e,t,s,n){const i={value:t};return s&&(i.expires=Date.now()+s),this.storageInterface(n).setItem(e,JSON.stringify(i))}_get(e,t){if(t&&!this.sessionSupported)throw new Error("Session Storage not supported");if(!t&&!this.localSupported)throw new Error("Local Storage not supported");const s=this.storageInterface(t).getItem(e);if(!s)return null;const n=JSON.parse(s);return n.expires&&n.expires<Date.now()?(this.storageInterface(t).removeItem(e),null):n.value}_remove(e,t){return this.storageInterface(t).removeItem(e)}storageInterface(e){return e?Sn.sessionStorage:Sn.localStorage}},An=de(),In={agent:"browser",logTimestamps:!0,userAgent:An.navigator&&An.navigator.userAgent.toString(),currentUrl:An.location&&An.location.href,binaryType:"arraybuffer",WebSocket:An.WebSocket,fetchSupported:!!An.fetch,xhrSupported:An.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest,allowComet:function(){const e=An.location;return!An.WebSocket||!e||!e.origin||e.origin.indexOf("http")>-1}(),useProtocolHeartbeats:!0,supportsBinary:!!An.TextDecoder,preferBinary:!1,ArrayBuffer:An.ArrayBuffer,atob:An.atob,nextTick:void 0!==An.setImmediate?An.setImmediate.bind(An):function(e){setTimeout(e,0)},addEventListener:An.addEventListener,inspect:JSON.stringify,stringByteSize:function(e){return An.TextDecoder&&(new An.TextEncoder).encode(e).length||e.length},TextEncoder:An.TextEncoder,TextDecoder:An.TextDecoder,getRandomArrayBuffer:async function(e){const t=new Uint8Array(e);return An.crypto.getRandomValues(t),t.buffer},isWebworker:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,push:{platform:"browser",formFactor:"desktop",storage:On}};function Mn(e){return function(e){const t=[80015,80017,80030];return!!e.code&&!He.isTokenErr(e)&&(!!t.includes(e.code)||e.code>=4e4&&e.code<5e4)}(e)?[ps({action:ze.ERROR,error:e})]:[ps({action:ze.DISCONNECTED,error:e})]}var Ln=class extends Ns{constructor(e,t,s){super(e,t,s,!0),this.onAuthUpdated=e=>{this.authParams={access_token:e.token}},this.stream=!("stream"in s)||s.stream,this.sendRequest=null,this.recvRequest=null,this.pendingCallback=null,this.pendingItems=null}connect(){_.logAction(this.logger,_.LOG_MINOR,"CometTransport.connect()","starting"),Ns.prototype.connect.call(this);const e=this.params,t=e.options,s=Ae.getHost(t,e.host),n=Ae.getPort(t),i=t.tls?"https://":"http://";this.baseUri=i+s+":"+n+"/comet/";const a=this.baseUri+"connect";_.logAction(this.logger,_.LOG_MINOR,"CometTransport.connect()","uri: "+a),ne(this.auth.getAuthParams(),(e,t)=>{if(e)return void this.disconnect(e);if(this.isDisposed)return;this.authParams=t;const s=this.params.getConnectParams(t);"stream"in s&&(this.stream=s.stream),_.logAction(this.logger,_.LOG_MINOR,"CometTransport.connect()","connectParams:"+J(s));let n=!1;const i=this.recvRequest=this.createRequest(a,null,s,null,this.stream?_n.REQ_RECV_STREAM:_n.REQ_RECV);i.on("data",e=>{this.recvRequest&&(n||(n=!0,this.emit("preconnect")),this.onData(e))}),i.on("complete",e=>{this.recvRequest||(e=e||new R("Request cancelled",80003,400)),this.recvRequest=null,n||e||(n=!0,this.emit("preconnect")),this.onActivity(),e?e.code?this.onData(Mn(e)):this.disconnect(e):m.Config.nextTick(()=>{this.recv()})}),i.exec()})}requestClose(){_.logAction(this.logger,_.LOG_MINOR,"CometTransport.requestClose()"),this._requestCloseOrDisconnect(!0)}requestDisconnect(){_.logAction(this.logger,_.LOG_MINOR,"CometTransport.requestDisconnect()"),this._requestCloseOrDisconnect(!1)}_requestCloseOrDisconnect(e){const t=e?this.closeUri:this.disconnectUri;if(t){const s=this.createRequest(t,null,this.authParams,null,_n.REQ_SEND);s.on("complete",t=>{t&&(_.logAction(this.logger,_.LOG_ERROR,"CometTransport.request"+(e?"Close()":"Disconnect()"),"request returned err = "+Q(t)),this.finish("disconnected",t))}),s.exec()}}dispose(){_.logAction(this.logger,_.LOG_MINOR,"CometTransport.dispose()",""),this.isDisposed||(this.isDisposed=!0,this.recvRequest&&(_.logAction(this.logger,_.LOG_MINOR,"CometTransport.dispose()","aborting recv request"),this.recvRequest.abort(),this.recvRequest=null),this.finish("disconnected",Ms.disconnected()),m.Config.nextTick(()=>{this.emit("disposed")}))}onConnect(e){var t;if(this.isDisposed)return;const s=null==(t=e.connectionDetails)?void 0:t.connectionKey;Ns.prototype.onConnect.call(this,e);const n=this.baseUri+s;_.logAction(this.logger,_.LOG_MICRO,"CometTransport.onConnect()","baseUri = "+n),this.sendUri=n+"/send",this.recvUri=n+"/recv",this.closeUri=n+"/close",this.disconnectUri=n+"/disconnect"}send(e){if(this.sendRequest)return this.pendingItems=this.pendingItems||[],void this.pendingItems.push(e);const t=this.pendingItems||[];t.push(e),this.pendingItems=null,this.sendItems(t)}sendAnyPending(){const e=this.pendingItems;e&&(this.pendingItems=null,this.sendItems(e))}sendItems(e){const t=this.sendRequest=this.createRequest(this.sendUri,null,this.authParams,this.encodeRequest(e),_n.REQ_SEND);t.on("complete",(e,t)=>{e&&_.logAction(this.logger,_.LOG_ERROR,"CometTransport.sendItems()","on complete: err = "+Q(e)),this.sendRequest=null,e?e.code?this.onData(Mn(e)):this.disconnect(e):(t&&this.onData(t),this.pendingItems&&m.Config.nextTick(()=>{this.sendRequest||this.sendAnyPending()}))}),t.exec()}recv(){if(this.recvRequest)return;if(!this.isConnected)return;const e=this.recvRequest=this.createRequest(this.recvUri,null,this.authParams,null,this.stream?_n.REQ_RECV_STREAM:_n.REQ_RECV_POLL);e.on("data",e=>{this.onData(e)}),e.on("complete",e=>{this.recvRequest=null,this.onActivity(),e?e.code?this.onData(Mn(e)):this.disconnect(e):m.Config.nextTick(()=>{this.recv()})}),e.exec()}onData(e){try{const t=this.decodeResponse(e);if(t&&t.length)for(let e=0;e<t.length;e++)this.onProtocolMessage(ds(t[e],this.connectionManager.realtime._RealtimePresence,this.connectionManager.realtime._Annotations,this.connectionManager.realtime._objectsPlugin))}catch(t){_.logAction(this.logger,_.LOG_ERROR,"CometTransport.onData()","Unexpected exception handing channel event: "+t.stack)}}encodeRequest(e){return JSON.stringify(e)}decodeResponse(e){return"string"==typeof e?JSON.parse(e):e}};function Pn(e,t){if(function(e,t){return re(G(t)).includes("x-ably-errorcode")}(0,t))return e.error&&R.fromValues(e.error)}var Nn=function(){},Un=0,Dn={},xn=class e extends We{constructor(e,t,s,n,i,a,r,o){super(r),(s=s||{}).rnd=ee(),this.uri=e+J(s),this.headers=t||{},this.body=n,this.method=o?o.toUpperCase():L(n)?"GET":"POST",this.requestMode=i,this.timeouts=a,this.timedOut=!1,this.requestComplete=!1,this.id=String(++Un),Dn[this.id]=this}static createRequest(t,s,n,i,a,r,o,l){const c=r||Ae.TIMEOUTS;return new e(t,s,O(n),i,a,c,o,l)}complete(e,t,s,n,i){this.requestComplete||(this.requestComplete=!0,!e&&t&&this.emit("data",t),this.emit("complete",e,t,s,n,i),this.dispose())}abort(){this.dispose()}exec(){let e=this.headers;const t=this.requestMode==_n.REQ_SEND?this.timeouts.httpRequestTimeout:this.timeouts.recvTimeout,s=this.timer=setTimeout(()=>{this.timedOut=!0,i.abort()},t),n=this.method,i=this.xhr=new XMLHttpRequest,a=e.accept;let r=this.body,o="text";a?0===a.indexOf("application/x-msgpack")&&(o="arraybuffer"):e.accept="application/json",r&&(e["content-type"]||(e["content-type"]="application/json")).indexOf("application/json")>-1&&"string"!=typeof r&&(r=JSON.stringify(r)),i.open(n,this.uri,!0),i.responseType=o,"authorization"in e&&(i.withCredentials=!0);for(const m in e)i.setRequestHeader(m,e[m]);const l=(e,t,s,n)=>{var i;let a=t+" (event type: "+e.type+")";(null==(i=null==this?void 0:this.xhr)?void 0:i.statusText)&&(a+=", current statusText is "+this.xhr.statusText),_.logAction(this.logger,_.LOG_ERROR,"Request.on"+e.type+"()",a),this.complete(new T(a,s,n))};let c,u,d;i.onerror=function(e){l(e,"XHR error occurred",null,400)},i.onabort=e=>{this.timedOut?l(e,"Request aborted due to request timeout expiring",null,408):l(e,"Request cancelled",null,400)},i.ontimeout=function(e){l(e,"Request timed out",null,408)};let h=0,p=!1;const f=()=>{clearTimeout(s),d=u<400,204!=u?c=this.requestMode==_n.REQ_RECV_STREAM&&d&&function(e){return e.getResponseHeader&&(e.getResponseHeader("transfer-encoding")||!e.getResponseHeader("content-length"))}(i):this.complete(null,null,null,null,u)},g=()=>{let t;try{const s=function(e,t){return e.getResponseHeader&&e.getResponseHeader(t)}(i,"content-type");if(s?s.indexOf("application/json")>=0:"text"==i.responseType){const e="arraybuffer"===i.responseType?m.BufferUtils.utf8Decode(i.response):String(i.responseText);t=e.length?JSON.parse(e):e,p=!0}else t=i.response;void 0!==t.response?(u=t.statusCode,d=u<400,e=t.headers,t=t.response):e=function(e){const t=e.getAllResponseHeaders().trim().split("\r\n"),s={};for(let n=0;n<t.length;n++){const e=t[n].split(":").map(e=>e.trim());s[e[0].toLowerCase()]=e[1]}return s}(i)}catch(n){return void this.complete(new T("Malformed response body from server: "+n.message,null,400))}if(d||Array.isArray(t))return void this.complete(null,t,e,p,u);let s=Pn(t,e);s||(s=new T("Error response received from server: "+u+" body was: "+m.Config.inspect(t),null,u)),this.complete(s,t,e,p,u)};function v(){const e=i.responseText,t=e.length-1;let s,n;for(;h<t&&(s=e.indexOf("\n",h))>-1;)n=e.slice(h,s),h=s+1,y(n)}const y=e=>{try{e=JSON.parse(e)}catch(t){return void this.complete(new T("Malformed response body from server: "+t.message,null,400))}this.emit("data",e)},b=()=>{v(),this.streamComplete=!0,m.Config.nextTick(()=>{this.complete()})};i.onreadystatechange=function(){const e=i.readyState;e<3||0!==i.status&&(void 0===u&&(u=i.status,f()),3==e&&c?v():4==e&&(c?b():g()))},i.send(r)}dispose(){const e=this.xhr;if(e){e.onreadystatechange=e.onerror=e.onabort=e.ontimeout=Nn,this.xhr=null;const t=this.timer;t&&(clearTimeout(t),this.timer=null),this.requestComplete||e.abort()}delete Dn[this.id]}},Bn=Cs.XhrPolling,qn={order:["xhr_polling"],bundledImplementations:{web_socket:Zs,xhr_polling:class extends Ln{constructor(e,t,s){super(e,t,s),this.shortName=Bn,s.stream=!1,this.shortName=Bn}static isAvailable(){return!(!m.Config.xhrSupported||!m.Config.allowComet)}toString(){return"XHRPollingTransport; uri="+this.baseUri+"; isConnected="+this.isConnected}createRequest(e,t,s,n,i){return xn.createRequest(e,t,s,n,i,this.timeouts,this.logger)}}}},Vn={connectivityCheckUrl:"https://internet-up.ably-realtime.com/is-the-internet-up.txt",wsConnectivityCheckUrl:"wss://ws-up.ably-realtime.com",defaultTransports:[Cs.XhrPolling,Cs.WebSocket]};function Hn(e,t,s){for(let n=0,i=s.length;n<i;n++){const i=s.charCodeAt(n);if(i<128)e.setUint8(t++,i>>>0&127);else if(i<2048)e.setUint8(t++,i>>>6&31|192),e.setUint8(t++,i>>>0&63|128);else if(i<65536)e.setUint8(t++,i>>>12&15|224),e.setUint8(t++,i>>>6&63|128),e.setUint8(t++,i>>>0&63|128);else{if(!(i<1114112))throw new Error("bad codepoint "+i);e.setUint8(t++,i>>>18&7|240),e.setUint8(t++,i>>>12&63|128),e.setUint8(t++,i>>>6&63|128),e.setUint8(t++,i>>>0&63|128)}}}function Gn(e,t,s){let n="";for(let i=t,a=t+s;i<a;i++){const t=e.getUint8(i);if(128&t)if(192!=(224&t))if(224!=(240&t)){if(240!=(248&t))throw new Error("Invalid byte "+t.toString(16));n+=String.fromCharCode((7&t)<<18|(63&e.getUint8(++i))<<12|(63&e.getUint8(++i))<<6|63&e.getUint8(++i))}else n+=String.fromCharCode((15&t)<<12|(63&e.getUint8(++i))<<6|63&e.getUint8(++i));else n+=String.fromCharCode((15&t)<<6|63&e.getUint8(++i));else n+=String.fromCharCode(t)}return n}function jn(e){let t=0;for(let s=0,n=e.length;s<n;s++){const n=e.charCodeAt(s);if(n<128)t+=1;else if(n<2048)t+=2;else if(n<65536)t+=3;else{if(!(n<1114112))throw new Error("bad codepoint "+n);t+=4}}return t}var $n=4294967296,Fn=1/$n,Wn=class{constructor(e,t){this.map=e=>{const t={};for(let s=0;s<e;s++)t[this.parse()]=this.parse();return t},this.bin=e=>{const t=new ArrayBuffer(e);return new Uint8Array(t).set(new Uint8Array(this.view.buffer,this.offset,e),0),this.offset+=e,t},this.buf=this.bin,this.str=e=>{const t=Gn(this.view,this.offset,e);return this.offset+=e,t},this.array=e=>{const t=new Array(e);for(let s=0;s<e;s++)t[s]=this.parse();return t},this.ext=e=>(this.offset+=e,{type:this.view.getInt8(this.offset),data:this.buf(e)}),this.parse=()=>{const e=this.view.getUint8(this.offset);let t,s;if(!(128&e))return this.offset++,e;if(128==(240&e))return s=15&e,this.offset++,this.map(s);if(144==(240&e))return s=15&e,this.offset++,this.array(s);if(160==(224&e))return s=31&e,this.offset++,this.str(s);if(!(224&~e))return t=this.view.getInt8(this.offset),this.offset++,t;switch(e){case 192:return this.offset++,null;case 193:return void this.offset++;case 194:return this.offset++,!1;case 195:return this.offset++,!0;case 196:return s=this.view.getUint8(this.offset+1),this.offset+=2,this.bin(s);case 197:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.bin(s);case 198:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.bin(s);case 199:return s=this.view.getUint8(this.offset+1),this.offset+=2,this.ext(s);case 200:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.ext(s);case 201:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.ext(s);case 202:return t=this.view.getFloat32(this.offset+1),this.offset+=5,t;case 203:return t=this.view.getFloat64(this.offset+1),this.offset+=9,t;case 204:return t=this.view.getUint8(this.offset+1),this.offset+=2,t;case 205:return t=this.view.getUint16(this.offset+1),this.offset+=3,t;case 206:return t=this.view.getUint32(this.offset+1),this.offset+=5,t;case 207:return t=function(e,t){return t=t||0,e.getUint32(t)*$n+e.getUint32(t+4)}(this.view,this.offset+1),this.offset+=9,t;case 208:return t=this.view.getInt8(this.offset+1),this.offset+=2,t;case 209:return t=this.view.getInt16(this.offset+1),this.offset+=3,t;case 210:return t=this.view.getInt32(this.offset+1),this.offset+=5,t;case 211:return t=function(e,t){return t=t||0,e.getInt32(t)*$n+e.getUint32(t+4)}(this.view,this.offset+1),this.offset+=9,t;case 212:return s=1,this.offset++,this.ext(s);case 213:return s=2,this.offset++,this.ext(s);case 214:return s=4,this.offset++,this.ext(s);case 215:return s=8,this.offset++,this.ext(s);case 216:return s=16,this.offset++,this.ext(s);case 217:return s=this.view.getUint8(this.offset+1),this.offset+=2,this.str(s);case 218:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.str(s);case 219:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.str(s);case 220:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.array(s);case 221:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.array(s);case 222:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.map(s);case 223:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.map(s)}throw new Error("Unknown type 0x"+e.toString(16))},this.offset=t||0,this.view=e}};function zn(e,t){return Object.keys(e).filter(function(s){const n=e[s];return!(t&&null==n||"function"==typeof n&&!n.toJSON)})}function Jn(e,t,s,n){const i=typeof e;if("string"==typeof e){const n=jn(e);if(n<32)return t.setUint8(s,160|n),Hn(t,s+1,e),1+n;if(n<256)return t.setUint8(s,217),t.setUint8(s+1,n),Hn(t,s+2,e),2+n;if(n<65536)return t.setUint8(s,218),t.setUint16(s+1,n),Hn(t,s+3,e),3+n;if(n<4294967296)return t.setUint8(s,219),t.setUint32(s+1,n),Hn(t,s+5,e),5+n}if(ArrayBuffer.isView&&ArrayBuffer.isView(e)&&(e=e.buffer),e instanceof ArrayBuffer){const n=e.byteLength;if(n<256)return t.setUint8(s,196),t.setUint8(s+1,n),new Uint8Array(t.buffer).set(new Uint8Array(e),s+2),2+n;if(n<65536)return t.setUint8(s,197),t.setUint16(s+1,n),new Uint8Array(t.buffer).set(new Uint8Array(e),s+3),3+n;if(n<4294967296)return t.setUint8(s,198),t.setUint32(s+1,n),new Uint8Array(t.buffer).set(new Uint8Array(e),s+5),5+n}if("number"==typeof e){if(Math.floor(e)!==e)return t.setUint8(s,203),t.setFloat64(s+1,e),9;if(e>=0){if(e<128)return t.setUint8(s,e),1;if(e<256)return t.setUint8(s,204),t.setUint8(s+1,e),2;if(e<65536)return t.setUint8(s,205),t.setUint16(s+1,e),3;if(e<4294967296)return t.setUint8(s,206),t.setUint32(s+1,e),5;if(e<0x10000000000000000)return t.setUint8(s,207),function(e,t,s){s<0x10000000000000000?(e.setUint32(t,Math.floor(s*Fn)),e.setInt32(t+4,-1&s)):(e.setUint32(t,4294967295),e.setUint32(t+4,4294967295))}(t,s+1,e),9;throw new Error("Number too big 0x"+e.toString(16))}if(e>=-32)return t.setInt8(s,e),1;if(e>=-128)return t.setUint8(s,208),t.setInt8(s+1,e),2;if(e>=-32768)return t.setUint8(s,209),t.setInt16(s+1,e),3;if(e>=-2147483648)return t.setUint8(s,210),t.setInt32(s+1,e),5;if(e>=-0x8000000000000000)return t.setUint8(s,211),function(e,t,s){s<0x8000000000000000?(e.setInt32(t,Math.floor(s*Fn)),e.setInt32(t+4,-1&s)):(e.setUint32(t,2147483647),e.setUint32(t+4,2147483647))}(t,s+1,e),9;throw new Error("Number too small -0x"+(-e).toString(16).substr(1))}if("undefined"===i)return n?0:(t.setUint8(s,212),t.setUint8(s+1,0),t.setUint8(s+2,0),3);if(null===e)return n?0:(t.setUint8(s,192),1);if("boolean"===i)return t.setUint8(s,e?195:194),1;if("function"==typeof e.toJSON)return Jn(e.toJSON(),t,s,n);if("object"===i){let i,a,r=0;const o=Array.isArray(e);if(o?i=e.length:(a=zn(e,n),i=a.length),i<16?(t.setUint8(s,i|(o?144:128)),r=1):i<65536?(t.setUint8(s,o?220:222),t.setUint16(s+1,i),r=3):i<4294967296&&(t.setUint8(s,o?221:223),t.setUint32(s+1,i),r=5),o)for(let l=0;l<i;l++)r+=Jn(e[l],t,s+r,n);else if(a)for(let l=0;l<i;l++){const i=a[l];r+=Jn(i,t,s+r),r+=Jn(e[i],t,s+r,n)}return r}if("function"===i)return 0;throw new Error("Unknown type "+i)}function Kn(e,t){const s=typeof e;if("string"===s){const t=jn(e);if(t<32)return 1+t;if(t<256)return 2+t;if(t<65536)return 3+t;if(t<4294967296)return 5+t}if(ArrayBuffer.isView&&ArrayBuffer.isView(e)&&(e=e.buffer),e instanceof ArrayBuffer){const t=e.byteLength;if(t<256)return 2+t;if(t<65536)return 3+t;if(t<4294967296)return 5+t}if("number"==typeof e){if(Math.floor(e)!==e)return 9;if(e>=0){if(e<128)return 1;if(e<256)return 2;if(e<65536)return 3;if(e<4294967296)return 5;if(e<0x10000000000000000)return 9;throw new Error("Number too big 0x"+e.toString(16))}if(e>=-32)return 1;if(e>=-128)return 2;if(e>=-32768)return 3;if(e>=-2147483648)return 5;if(e>=-0x8000000000000000)return 9;throw new Error("Number too small -0x"+e.toString(16).substr(1))}if("boolean"===s)return 1;if(null===e)return t?0:1;if(void 0===e)return t?0:3;if("function"==typeof e.toJSON)return Kn(e.toJSON(),t);if("object"===s){let s,n=0;if(Array.isArray(e)){s=e.length;for(let i=0;i<s;i++)n+=Kn(e[i],t)}else{const i=zn(e,t);s=i.length;for(let a=0;a<s;a++){const s=i[a];n+=Kn(s)+Kn(e[s],t)}}if(s<16)return 1+n;if(s<65536)return 3+n;if(s<4294967296)return 5+n;throw new Error("Array or object too long 0x"+s.toString(16))}if("function"===s)return 0;throw new Error("Unknown type "+s)}var Yn,Qn={encode:function(e,t){const s=Kn(e,t);if(0===s)return;const n=new ArrayBuffer(s);return Jn(e,new DataView(n),0,t),n},decode:function(e){const t=new DataView(e),s=new Wn(t),n=s.parse();if(s.offset!==e.byteLength)throw new Error(e.byteLength-s.offset+" trailing bytes");return n},inspect:function(e){if(void 0===e)return"undefined";let t,s;if(e instanceof ArrayBuffer?(s="ArrayBuffer",t=new DataView(e)):e instanceof DataView&&(s="DataView",t=e),!t)return JSON.stringify(e);const n=[];for(let i=0;i<e.byteLength;i++){if(i>20){n.push("...");break}let e=t.getUint8(i).toString(16);1===e.length&&(e="0"+e),n.push(e)}return"<"+s+" "+n.join(" ")+">"},utf8Write:Hn,utf8Read:Gn,utf8ByteCount:jn},Xn={XHRRequest:xn,FetchRequest:async function(e,t,s,n,i,a){const r=new Headers(n||{}),o=e?e.toUpperCase():L(a)?"GET":"POST",l=new AbortController;let c;const u=new Promise(e=>{c=setTimeout(()=>{l.abort(),e({error:new T("Request timed out",null,408)})},t?t.options.timeouts.httpRequestTimeout:Ae.TIMEOUTS.httpRequestTimeout)}),d={method:o,headers:r,body:a,signal:l.signal};m.Config.isWebworker||(d.credentials=r.has("authorization")?"include":"same-origin");const h=(async()=>{try{const e=new URLSearchParams(i||{});e.set("rnd",ee());const t=s+"?"+e,n=await de().fetch(t,d);if(clearTimeout(c),204==n.status)return{error:null,statusCode:n.status};const a=n.headers.get("Content-Type");let r;r=a&&a.indexOf("application/x-msgpack")>-1?await n.arrayBuffer():a&&a.indexOf("application/json")>-1?await n.json():await n.text();const o=!!a&&-1===a.indexOf("application/x-msgpack"),l=function(e){const t={};return e.forEach((e,s)=>{t[s]=e}),t}(n.headers);if(n.ok)return{error:null,body:r,headers:l,unpacked:o,statusCode:n.status};{const e=function(e,t){if(function(e,t){return!!t.get("x-ably-errorcode")}(0,t))return e.error&&R.fromValues(e.error)}(r,n.headers)||new T("Error response received from server: "+n.status+" body was: "+m.Config.inspect(r),null,n.status);return{error:e,body:r,headers:l,unpacked:o,statusCode:n.status}}}catch(e){return clearTimeout(c),{error:e}}})();return Promise.race([u,h])}},Zn=function(e,t){class s{constructor(e,t,s,n){this.algorithm=e,this.keyLength=t,this.mode=s,this.key=n}}class n{static getDefaultParams(e){var n;if(!e.key)throw new Error("Crypto.getDefaultParams: a key is required");n="string"==typeof e.key?t.toArrayBuffer(t.base64Decode(e.key.replace("_","/").replace("-","+"))):e.key instanceof ArrayBuffer?e.key:t.toArrayBuffer(e.key);var i=e.algorithm||"aes",a=8*n.byteLength,r=e.mode||"cbc",o=new s(i,a,r,n);if(e.keyLength&&e.keyLength!==o.keyLength)throw new Error("Crypto.getDefaultParams: a keyLength of "+e.keyLength+" was specified, but the key actually has length "+o.keyLength);return function(e){if("aes"===e.algorithm&&"cbc"===e.mode){if(128===e.keyLength||256===e.keyLength)return;throw new Error("Unsupported key length "+e.keyLength+" for aes-cbc encryption. Encryption key must be 128 or 256 bits (16 or 32 ASCII characters)")}}(o),o}static async generateRandomKey(t){try{return e.getRandomArrayBuffer((t||256)/8)}catch(s){throw new R("Failed to generate random key: "+s.message,400,5e4,s)}}static getCipher(e,t){var n,a=function(e){return e instanceof s}(e)?e:this.getDefaultParams(e);return{cipherParams:a,cipher:new i(a,null!=(n=e.iv)?n:null,t)}}}n.CipherParams=s;class i{constructor(e,s,n){if(this.logger=n,!crypto.subtle)throw isSecureContext?new Error("Crypto operations are not possible since the browser’s SubtleCrypto class is unavailable (reason unknown)."):new Error("Crypto operations are is not possible since the current environment is a non-secure context and hence the browser’s SubtleCrypto class is not available.");this.algorithm=e.algorithm+"-"+String(e.keyLength)+"-"+e.mode,this.webCryptoAlgorithm=e.algorithm+"-"+e.mode,this.key=t.toArrayBuffer(e.key),this.iv=s?t.toArrayBuffer(s):null}concat(e,s){const n=new ArrayBuffer(e.byteLength+s.byteLength),i=new DataView(n),a=new DataView(t.toArrayBuffer(e));for(let t=0;t<a.byteLength;t++)i.setInt8(t,a.getInt8(t));const r=new DataView(t.toArrayBuffer(s));for(let t=0;t<r.byteLength;t++)i.setInt8(a.byteLength+t,r.getInt8(t));return n}async encrypt(e){_.logAction(this.logger,_.LOG_MICRO,"CBCCipher.encrypt()","");const t=await this.getIv(),s=await crypto.subtle.importKey("raw",this.key,this.webCryptoAlgorithm,!1,["encrypt"]),n=await crypto.subtle.encrypt({name:this.webCryptoAlgorithm,iv:t},s,e);return this.concat(t,n)}async decrypt(e){_.logAction(this.logger,_.LOG_MICRO,"CBCCipher.decrypt()","");const s=t.toArrayBuffer(e),n=s.slice(0,16),i=s.slice(16),a=await crypto.subtle.importKey("raw",this.key,this.webCryptoAlgorithm,!1,["decrypt"]);return crypto.subtle.decrypt({name:this.webCryptoAlgorithm,iv:n},a,i)}async getIv(){if(this.iv){var s=this.iv;return this.iv=null,s}const n=await e.getRandomArrayBuffer(16);return t.toArrayBuffer(n)}}return n}(In,wn);m.Crypto=Zn,m.BufferUtils=wn,m.Http=Rn,m.Config=In,m.Transports=qn,m.WebStorage=On;for(const s of[Rs,sn])s.Crypto=Zn,s._MsgPack=Qn;Rn.bundledRequestImplementations=Xn,_.initLogHandlers(),m.Defaults=(Yn=Vn,Object.assign(we,Yn)),m.Config.agent&&(m.Defaults.agent+=" "+m.Config.agent);var ei={ErrorInfo:R,Rest:Rs,Realtime:sn,msgpack:Qn,makeProtocolMessageFromDeserialized:hs};return"object"==typeof n.exports&&(n.exports=((e,t,s,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of Object.getOwnPropertyNames(t))Object.prototype.hasOwnProperty.call(e,i)||i===s||Object.defineProperty(e,i,{get:()=>t[i],enumerable:!(n=Object.getOwnPropertyDescriptor(t,i))||n.enumerable});return e})(n.exports,t)),n.exports},de.exports=he());let fe=null;const ge=n(!1),ve=n([]),me=()=>"undefined"!=typeof window?window.location.hostname:"default",ye=async e=>{J.info("[ABLY] Connexion au service de temps réel");try{J.debug("[ABLY] Initialisation sans clientId spécifique");const t={authCallback:async(e,t)=>{J.debug("[ABLY] authCallback appelé pour renouveler le token");try{t(null,await(async()=>{var e;try{const t=await F.routes.client.getRealtimeToken();if(!(null==(e=t.data)?void 0:e.token))throw new Error("Token Ably client non trouvé dans la réponse");return J.debug("[ABLY] Nouveau token client obtenu avec succès",{client_id:t.data.client_id}),t.data.token}catch(t){throw J.error("[ABLY] Erreur lors de la récupération du token client",{error:t}),t}})())}catch(s){J.error("[ABLY] Erreur dans authCallback",{error:s});t({code:40170,statusCode:401,message:s instanceof Error?s.message:"Erreur de récupération du token",name:"TokenError"},null)}},token:e,echoMessages:!1,closeOnUnload:!1,recover:""};fe=new pe.Realtime(t),fe.connection.on("connected",()=>{J.info("[ABLY] Connecté au service de temps réel"),ge.value=!0}),fe.connection.on("disconnected",()=>{J.info("[ABLY] Déconnecté du service de temps réel"),ge.value=!1}),fe.connection.on("failed",e=>{J.error("[ABLY] Échec de connexion",{reason:e.reason})}),fe.connection.on("suspended",()=>{}),setTimeout(()=>{ge.value||(J.debug("[ABLY] Tentative de connexion forcée après délai d'initialisation"),null==fe||fe.connect())},1e3)}catch(t){throw J.error("[ABLY] Erreur de connexion",{error:t}),t}},be=(e,t,s)=>{if(!fe)return J.error("[ABLY] Client non initialisé"),()=>{};try{fe.channels.get(e).subscribe(t,n=>{J.debug(`[ABLY] Message reçu sur ${e}:${t}`,{message:n}),s(n.data)});return-1===ve.value.findIndex(s=>s.channel===e&&s.event===t)?(ve.value.push({channel:e,event:t,callback:s}),J.debug(`[ABLY] Abonnement ajouté: ${e}:${t}`)):J.debug(`[ABLY] Abonnement déjà existant: ${e}:${t}`),()=>we(e,t)}catch(n){return J.error("[ABLY] Erreur lors de l'abonnement au canal",{channel:e,error:n}),()=>{}}},we=(e,t)=>{if(fe)try{const s=fe.channels.get(e);t?(s.unsubscribe(t),ve.value=ve.value.filter(s=>!(s.channel===e&&s.event===t))):(s.unsubscribe(),ve.value=ve.value.filter(t=>t.channel!==e)),ke()}catch(s){J.error("[ABLY] Erreur lors du désabonnement du canal",{channel:e,error:s})}else J.error("[ABLY] Client non initialisé")},ke=()=>{var e;const t=(()=>{if(!fe)return J.error("[ABLY] Client non initialisé"),[];const e=new Map;return ve.value.forEach(t=>{var s,n;e.has(t.channel)||e.set(t.channel,[]),(null==(s=e.get(t.channel))?void 0:s.includes(t.event))||null==(n=e.get(t.channel))||n.push(t.event)}),Array.from(e.entries()).map(([e,t])=>{let s="unknown";try{fe&&fe.channels&&(s=fe.channels.get(e).state||"attached")}catch(n){}return{name:e,state:s,events:t}}).sort((e,t)=>e.name.localeCompare(t.name))})();return J.debug(`[ABLY] Nombre total d'abonnements enregistrés: ${ve.value.length}`),0===t.length?ve.value.length>0&&J.debug("[ABLY] Abonnements enregistrés mais aucun canal trouvé:",ve.value.map(e=>`${e.channel}:${e.event}`)):t.forEach(e=>{e.name.includes("private-admin");e.events.length>0&&J.debug("Événements:",{events:e.events.join(", ")})}),"undefined"!=typeof window&&(null==(e=window.AblyDebug)?void 0:e.isAutoDisplayActive)&&window.AblyDebug.isAutoDisplayActive(),t},_e=(e,t,s)=>{const n=`${me()}:private-client-${e}`;J.info(`[ABLY] 🔔 ABONNEMENT CANAL PRIVÉ CLIENT: ${n}, événement: ${t}`);return be(n,t,e=>{J.info(`[ABLY] 🎯 MESSAGE REÇU sur ${n}:${t}`,{message:e,timestamp:(new Date).toISOString(),channelName:n,event:t}),s(e)})},Ce=(e,t,s)=>{const n=me();return be(`${n}:ticket-${e}`,t,s)},Ee=i("realtime",()=>{const e=n(!1),t=n(!1),s=n(null),i=n(0),r=n(5),o=n(null),l=n(null),c=n(null),u=n(new Map),d=n(null),h=n([]),p=n(0),f=a(()=>null!==s.value),g=a(()=>i.value<r.value),v=a(()=>e.value&&t.value&&!f.value),m=a(()=>p.value>0),y=a(()=>!!d.value),b=async()=>{var n;try{J.info("[REALTIME STORE CLIENT] Initialisation du service temps réel");if(!Zo().isAuthenticated)throw new Error("Utilisateur non authentifié");J.debug("[REALTIME STORE CLIENT] Récupération du token Ably");const a=null==(n=(await F.routes.client.getRealtimeToken()).data)?void 0:n.token;if(!a)throw new Error("Token Ably non trouvé dans la réponse");J.debug("[REALTIME STORE CLIENT] Initialisation d'Ably avec le token"),await ye(a),w(),e.value=!0,t.value=!0,i.value=0,s.value=null,J.info("[REALTIME STORE CLIENT] Service de temps réel initialisé avec succès")}catch(a){J.error("[REALTIME STORE CLIENT] Erreur lors de l'initialisation",{error:a}),s.value=a,e.value=!1,t.value=!1}},w=()=>{const e=fe;e&&(e.connection.on("connected",()=>{J.info("[REALTIME STORE CLIENT] Connexion établie"),t.value=!0,s.value=null,i.value=0}),e.connection.on("disconnected",()=>{J.warn("[REALTIME STORE CLIENT] Connexion perdue"),t.value=!1}),e.connection.on("failed",e=>{var n;J.error("[REALTIME STORE CLIENT] Échec de connexion",{reason:e.reason}),t.value=!1,s.value=new Error((null==(n=e.reason)?void 0:n.message)||"Connexion échouée")}),e.connection.on("suspended",()=>{J.warn("[REALTIME STORE CLIENT] Connexion suspendue"),t.value=!1}))},k=e=>{if(J.info("[REALTIME STORE CLIENT] Nouvelle réponse reçue",{data:e}),E(e.timestamp))return void J.debug("[REALTIME STORE CLIENT] Événement dupliqué ignoré",{timestamp:e.timestamp});const t={action:"reply",ticket:e.ticket,timestamp:e.timestamp,author:e.author,data:e.reply};o.value=t,l.value=e.timestamp},_=e=>{if(J.info("[REALTIME STORE CLIENT] Changement de statut reçu",{data:e}),E(e.timestamp))return void J.debug("[REALTIME STORE CLIENT] Événement dupliqué ignoré",{timestamp:e.timestamp});const t={action:"status_change",ticket:e.ticket,timestamp:e.timestamp,author:e.author,data:{oldStatus:e.oldStatus,newStatus:e.newStatus}};o.value=t,l.value=e.timestamp},C=e=>{if(J.info("[REALTIME STORE CLIENT] Mise à jour ticket reçue",{data:e}),E(e.timestamp))return void J.debug("[REALTIME STORE CLIENT] Événement dupliqué ignoré",{timestamp:e.timestamp});const t={action:"update",ticket:e.ticket,timestamp:e.timestamp,author:e.author,data:e.changes};o.value=t,l.value=e.timestamp},E=e=>l.value===e,R=e=>{J.info("[REALTIME STORE] Nouvelle notification",{notification:e}),h.value.unshift(e),e.read||p.value++,h.value.length>50&&(h.value=h.value.slice(0,50))};return{initialized:e,connected:t,error:s,reconnectAttempts:i,maxReconnectAttempts:r,lastRealtimeEvent:o,lastEventTimestamp:l,lastDashboardEvent:c,dashboardEventHandlers:u,dashboardChannel:d,notifications:h,unreadNotificationsCount:p,hasError:f,canReconnect:g,isReady:v,hasUnreadNotifications:m,isDashboardConnected:y,init:b,disconnect:()=>{J.info("[REALTIME STORE CLIENT] Déconnexion du service temps réel"),J.info("[ABLY] Déconnexion du service de temps réel"),fe&&(fe.close(),fe=null,ge.value=!1),e.value=!1,t.value=!1,s.value=null,o.value=null,l.value=null},retry:async()=>{if(g.value){i.value++,J.info("[REALTIME STORE CLIENT] Tentative de reconnexion",{attempt:i.value});try{await b()}catch(e){J.error("[REALTIME STORE CLIENT] Échec de la reconnexion",{error:e})}}else J.warn("[REALTIME STORE CLIENT] Nombre maximum de tentatives de reconnexion atteint")},subscribeToTicket:e=>{if(!v.value)return J.warn("[REALTIME STORE CLIENT] Service non prêt pour abonnement ticket",{ticketId:e}),()=>{};J.info("[REALTIME STORE CLIENT] Abonnement aux événements du ticket",{ticketId:e});const t=Ce(e,"ticket-reply",e=>{k(e)}),s=Ce(e,"ticket-status-change",e=>{_(e)}),n=Ce(e,"ticket-update",e=>{C(e)});return()=>{J.info("[REALTIME STORE CLIENT] Désabonnement des événements du ticket",{ticketId:e}),t(),s(),n()}},subscribeToDashboardEvents:async e=>{try{if(!v.value)throw new Error("Service temps réel non initialisé");if(d.value)return void J.warn("[REALTIME STORE] Abonnement dashboard déjà actif",{clientId:e});J.info("[REALTIME STORE] Abonnement aux événements dashboard sur canal privé client",{clientId:e});const t=_e(e,"dashboard-update",t=>{J.info("[REALTIME STORE] Événement dashboard-update reçu",{event:t,clientId:e});const s=t;c.value=s;const n=u.value.get(s.entity_type);n&&n(s),"notification"===s.action&&s.data.notification&&R(s.data.notification)});J.info("[REALTIME STORE] Abonnement à service-update sur canal private-client-"+e);const s=_e(e,"service-update",t=>{var s,n,i,a;J.info("[REALTIME STORE] 🎯 ÉVÉNEMENT SERVICE-UPDATE REÇU !",{data:t,clientId:e,timestamp:(new Date).toISOString()});const r={entity_type:"service",action:t.action||t.type,data:{service:(null==(s=t.data)?void 0:s.service)||t.service||t},timestamp:t.timestamp||(new Date).toISOString()};J.info("[REALTIME STORE] Événement service normalisé",{normalizedEvent:r});const o=u.value.get("service");o&&o(r);const l=u.value.get("service-page");l&&l(r);const c=(null==(i=null==(n=t.data)?void 0:n.service)?void 0:i.id)||(null==(a=t.service)?void 0:a.id);if(c){const e=`service-detail-${c}`,t=u.value.get(e);t?(J.info("[REALTIME STORE] 📤 ENVOI VERS SERVICE DETAIL HANDLER:",{handler_key:e,handler_exists:!0,data_sent:r,service_id:c}),t(r)):J.info("[REALTIME STORE] ℹ️ SERVICE DETAIL HANDLER NON TROUVÉ (normal si pas sur page détail):",{handler_key:e,service_id:c})}});J.info("[REALTIME STORE] Abonnement à invoice-update sur canal private-client-"+e);const n=_e(e,"invoice-update",t=>{var s,n,i,a,r,o,l;J.info("[REALTIME STORE] 🎯 ÉVÉNEMENT INVOICE-UPDATE REÇU !",{data:t,clientId:e,timestamp:(new Date).toISOString()}),J.info("[REALTIME STORE] 📊 ANALYSE STRUCTURE DONNÉES INVOICE:",{data_keys:Object.keys(t),"data.action":t.action,"data.entity_type":t.entity_type,"data.data_exists":!!t.data,"data.data_keys":t.data?Object.keys(t.data):null,"data.invoice_exists":!!t.invoice,"data.data.invoice_exists":t.data?!!t.data.invoice:null,structure_complete:JSON.stringify(t,null,2)});const c=u.value.get("invoice");c?(J.info("[REALTIME STORE] 📤 ENVOI VERS DASHBOARD HANDLER:",{handler_exists:!0,data_sent:t}),c(t)):J.warn("[REALTIME STORE] ❌ DASHBOARD HANDLER INTROUVABLE pour invoice");const d=u.value.get("invoice-page");d?(J.info("[REALTIME STORE] 📤 ENVOI VERS PAGE HANDLER:",{handler_exists:!0,data_sent:t}),d(t)):J.warn("[REALTIME STORE] ❌ PAGE HANDLER INTROUVABLE pour invoice-page");const h=(null==(n=null==(s=t.data)?void 0:s.invoice)?void 0:n.id)||(null==(i=t.invoice)?void 0:i.id);if(h){const e=`invoice-detail-${h}`,s=u.value.get(e);if(s){const n={entity_type:"invoice",action:t.action||t.type,data:{invoice:(null==(a=t.data)?void 0:a.invoice)||t.invoice||t},timestamp:t.timestamp||(new Date).toISOString()};J.info("[REALTIME STORE] 📤 ENVOI VERS DETAIL HANDLER:",{handler_key:e,handler_exists:!0,data_sent:n,invoice_id:h}),s(n)}else J.info("[REALTIME STORE] ℹ️ DETAIL HANDLER NON TROUVÉ (normal si pas sur page détail):",{handler_key:e,invoice_id:h})}const p=(null==(o=null==(r=t.data)?void 0:r.invoice)?void 0:o.id)||(null==(l=t.invoice)?void 0:l.id);if(p){const e=`invoice-detail-${p}`,s=u.value.get(e);s?(J.info("[REALTIME STORE] 📤 ENVOI VERS DETAIL HANDLER:",{handler_key:e,handler_exists:!0,data_sent:t,invoice_id:p}),s(t)):J.info("[REALTIME STORE] ℹ️ DETAIL HANDLER NON TROUVÉ (normal si pas sur page détail):",{handler_key:e,invoice_id:p})}}),i=_e(e,"ticket-update",t=>{J.info("[REALTIME STORE] Événement ticket-update reçu",{data:t,clientId:e});const s=u.value.get("ticket");s&&s(t);const n=u.value.get("ticket-page");n?(J.info("[REALTIME STORE] 📤 ENVOI VERS TICKET PAGE HANDLER:",{handler_exists:!0,data_sent:t}),n(t)):J.info("[REALTIME STORE] ℹ️ TICKET PAGE HANDLER NON TROUVÉ (normal si pas sur page support)")}),a=_e(e,"stats-update",t=>{J.info("[REALTIME STORE] Événement stats-update reçu",{data:t,clientId:e});const s=u.value.get("stats");s&&s(t)});d.value={unsubscribe:()=>{t(),s(),n(),i(),a()}},J.info("[REALTIME STORE] Abonnement dashboard établi avec succès sur canal privé client",{clientId:e})}catch(t){throw J.error("[REALTIME STORE] Erreur lors de l'abonnement dashboard",{error:t.message,clientId:e}),t}},unsubscribeFromDashboardEvents:()=>{d.value&&(J.info("[REALTIME STORE] Désabonnement des événements dashboard"),d.value.unsubscribe(),d.value=null,u.value.clear())},registerDashboardHandler:(e,t)=>{u.value.set(e,t)},unregisterDashboardHandler:e=>{u.value.delete(e)},addNotification:R,markNotificationAsRead:e=>{const t=h.value.find(t=>t.id===e);t&&!t.read&&(t.read=!0,p.value=Math.max(0,p.value-1))},markAllNotificationsAsRead:()=>{h.value.forEach(e=>{e.read=!0}),p.value=0,J.info("[REALTIME STORE] Toutes les notifications marquées comme lues")},clearNotifications:()=>{h.value=[],p.value=0,J.info("[REALTIME STORE] Notifications effacées")}}}),Re=i("clientDashboard",()=>{const e=n(!1),t=n(null),s=n(null),i=n(null),r=n([]),o=n([]),l=n([]),c=n([]),u=n([]),d=n(!1),h=n(null),p=n(!1),f=a(()=>!!s.value),g=a(()=>{var e,t;return(null==(t=null==(e=s.value)?void 0:e.licenses)?void 0:t.total)||0}),v=a(()=>{var e,t;return(null==(t=null==(e=s.value)?void 0:e.licenses)?void 0:t.active)||0}),m=a(()=>{var e;return(null==(e=s.value)?void 0:e.invoices.total_due)||0}),y=a(()=>{var e,t;return((null==(e=s.value)?void 0:e.tickets.open)||0)+((null==(t=s.value)?void 0:t.tickets.in_progress)||0)}),b=async()=>{var e;try{const t=Zo(),s=Ee();if(!t.isAuthenticated||!(null==(e=t.user)?void 0:e.id))return void J.warn("[DASHBOARD STORE] Utilisateur non authentifié, impossible d'initialiser le temps réel");if(d.value)return void J.warn("[DASHBOARD STORE] Temps réel déjà initialisé");const n=t.user.id;if(J.info("[DASHBOARD STORE] Initialisation des mises à jour temps réel",{clientId:n}),!s.isReady)return J.warn("[DASHBOARD STORE] Service temps réel non prêt, tentative d'initialisation différée"),void setTimeout(()=>{d.value||b()},2e3);await s.subscribeToDashboardEvents(n),s.registerDashboardHandler("service",w),s.registerDashboardHandler("invoice",k),s.registerDashboardHandler("ticket",_),s.registerDashboardHandler("stats",C),d.value=!0,J.info("[DASHBOARD STORE] Temps réel initialisé avec succès")}catch(t){J.error("[DASHBOARD STORE] Erreur lors de l'initialisation du temps réel",{error:t.message}),d.value=!1}},w=async e=>{J.info("[DASHBOARD STORE] Événement service ignoré (services supprimés)",{event:e})},k=async e=>{if(J.info("[DASHBOARD STORE] Mise à jour facture reçue",{event:e}),J.info("[DASHBOARD STORE] 📊 ANALYSE STRUCTURE EVENT REÇU:",{event_keys:Object.keys(e),"event.action":e.action,"event.entity_type":e.entity_type,"event.data_exists":!!e.data,"event.data_keys":e.data?Object.keys(e.data):null,"event.data.invoice_exists":e.data?!!e.data.invoice:null,"event.invoice_exists":!!e.invoice,structure_complete:JSON.stringify(e,null,2)}),!e.data.invoice)return void J.error("[DASHBOARD STORE] ❌ AUCUNE DONNÉE FACTURE TROUVÉE dans event.data.invoice");p.value=!0;const t=e.data.invoice,n=e.action||e.data.action;switch(J.info("[DASHBOARD STORE] Traitement événement facture",{action:n,invoiceId:t.id,invoiceNumber:t.number}),n){case"invoice_create":case"create":case"created":J.info("[DASHBOARD STORE] Facture créée - récupération liste complète prévue",{invoiceId:t.id,invoiceNumber:t.number});break;case"invoice_update":case"update":case"updated":const e=o.value.findIndex(e=>e.id===t.id);-1!==e&&(o.value[e]={...o.value[e],...t},J.info("[DASHBOARD STORE] Facture mise à jour",{invoiceId:t.id,newStatus:t.status,number:o.value[e].number}));break;default:const s=o.value.findIndex(e=>e.id===t.id);-1!==s&&(o.value[s]={...o.value[s],...t},J.info("[DASHBOARD STORE] Facture mise à jour (action inconnue)",{invoiceId:t.id,action:n,number:o.value[s].number}))}if(s.value)if("invoice_create"===n||"create"===n||"created"===n){J.info("[DASHBOARD STORE] Récupération des données après modification de facture",{action:n});try{const e=await ce.getOverview();r.value=e.recent_licenses||[],o.value=e.recent_invoices,l.value=e.recent_tickets,c.value=e.unpaid_invoices,u.value=e.open_tickets;const t=await ce.getStats();s.value=t,J.info("[DASHBOARD STORE] Toutes les données mises à jour après modification facture",{services:t.services,recentInvoicesCount:e.recent_invoices.length,action:n})}catch(i){J.error("[DASHBOARD STORE] Erreur lors de la récupération des données",{error:i});const e=o.value.filter(e=>"unpaid"===e.status).length,t=o.value.filter(e=>"unpaid"===e.status).reduce((e,t)=>e+t.amount,0);s.value.invoices.unpaid=e,s.value.invoices.total_due=t}}else{const e=o.value.filter(e=>"unpaid"===e.status).length,t=o.value.filter(e=>"unpaid"===e.status).reduce((e,t)=>e+t.amount,0);s.value.invoices.unpaid=e,s.value.invoices.total_due=t,J.info("[DASHBOARD STORE] Statistiques factures recalculées localement",{unpaid:e,totalDue:t,action:n})}h.value=(new Date).toISOString(),setTimeout(()=>{p.value=!1},1e3)},_=async e=>{if(J.info("[DASHBOARD STORE] Mise à jour ticket reçue",{event:e}),!e.data.ticket)return;p.value=!0;const t=e.data.ticket,n=e.action||e.data.action;switch(J.info("[DASHBOARD STORE] Traitement événement ticket",{action:n,ticketId:t.id,ticketTitle:t.title}),n){case"ticket_create":case"create":case"created":J.info("[DASHBOARD STORE] Ticket créé - récupération liste complète prévue",{ticketId:t.id,ticketTitle:t.title});break;case"ticket_update":case"update":case"updated":const e=l.value.findIndex(e=>e.id===t.id);-1!==e&&(l.value[e]={...l.value[e],...t},J.info("[DASHBOARD STORE] Ticket mis à jour",{ticketId:t.id,newStatus:t.status,title:l.value[e].title}));break;default:const s=l.value.findIndex(e=>e.id===t.id);-1!==s&&(l.value[s]={...l.value[s],...t},J.info("[DASHBOARD STORE] Ticket mis à jour (action inconnue)",{ticketId:t.id,action:n,title:l.value[s].title}))}if(s.value)if("ticket_create"===n||"create"===n||"created"===n){J.info("[DASHBOARD STORE] Récupération des données après modification de ticket",{action:n});try{const e=await ce.getOverview();recentServices.value=e.recent_services,o.value=e.recent_invoices,l.value=e.recent_tickets,c.value=e.unpaid_invoices,u.value=e.open_tickets;const t=await ce.getStats();s.value=t,J.info("[DASHBOARD STORE] Toutes les données mises à jour après modification ticket",{services:t.services,recentTicketsCount:e.recent_tickets.length,action:n})}catch(i){J.error("[DASHBOARD STORE] Erreur lors de la récupération des données",{error:i});const e=l.value.filter(e=>"open"===e.status).length,t=l.value.filter(e=>"answered"===e.status).length;s.value.tickets.open=e,s.value.tickets.in_progress=t}}else{const e=l.value.filter(e=>"open"===e.status).length,t=l.value.filter(e=>"answered"===e.status).length;s.value.tickets.open=e,s.value.tickets.in_progress=t,J.info("[DASHBOARD STORE] Statistiques tickets recalculées localement",{open:e,inProgress:t,action:n})}h.value=(new Date).toISOString(),setTimeout(()=>{p.value=!1},1e3)},C=e=>{J.info("[DASHBOARD STORE] Mise à jour statistiques reçue",{event:e}),e.data.stats&&(p.value=!0,s.value=e.data.stats,h.value=(new Date).toISOString(),setTimeout(()=>{p.value=!1},1e3))};return{loading:e,error:t,stats:s,overview:i,recentLicenses:r,recentInvoices:o,recentTickets:l,unpaidInvoices:c,openTickets:u,realtimeInitialized:d,lastUpdate:h,isUpdating:p,hasData:f,totalLicenses:g,activeLicenses:v,totalUnpaidAmount:m,openTicketsCount:y,fetchStats:async()=>{e.value=!0,t.value=null;try{s.value=await ce.getStats()}catch(n){throw t.value=n.message||"Erreur lors de la récupération des statistiques",n}finally{e.value=!1}},fetchOverview:async()=>{e.value=!0,t.value=null;try{i.value=await ce.getOverview(),r.value=i.value.recent_licenses||[],o.value=i.value.recent_invoices,l.value=i.value.recent_tickets,c.value=i.value.unpaid_invoices,u.value=i.value.open_tickets}catch(s){throw t.value=s.message||"Erreur lors de la récupération de la vue d'ensemble",s}finally{e.value=!1}},fetchDashboardData:async()=>{e.value=!0,t.value=null;try{const[e,t]=await Promise.all([ce.getStats(),ce.getOverview()]);s.value=e,i.value=t,r.value=t.recent_licenses||[],o.value=t.recent_invoices,l.value=t.recent_tickets,c.value=t.unpaid_invoices,u.value=t.open_tickets}catch(n){throw t.value=n.message||"Erreur lors de la récupération des données du dashboard",n}finally{e.value=!1}},fetchRecentLicenses:async()=>{try{const e=await ce.getOverview();r.value=e.recent_licenses||[]}catch(e){throw t.value=e.message||"Erreur lors de la récupération des licences récentes",e}},fetchRecentInvoices:async()=>{try{o.value=await ce.getRecentInvoices()}catch(e){throw t.value=e.message||"Erreur lors de la récupération des factures récentes",e}},fetchRecentTickets:async()=>{try{l.value=await ce.getRecentTickets()}catch(e){throw t.value=e.message||"Erreur lors de la récupération des tickets récents",e}},clearError:()=>{t.value=null},resetData:()=>{s.value=null,i.value=null,r.value=[],o.value=[],l.value=[],c.value=[],u.value=[],t.value=null,e.value=!1,d.value=!1,h.value=null,p.value=!1},initRealtimeUpdates:b,stopRealtimeUpdates:()=>{const e=Ee();J.info("[DASHBOARD STORE] Arrêt des mises à jour temps réel"),e.unsubscribeFromDashboardEvents(),e.unregisterDashboardHandler("service"),e.unregisterDashboardHandler("invoice"),e.unregisterDashboardHandler("ticket"),e.unregisterDashboardHandler("stats"),d.value=!1},handleServiceUpdate:w,handleInvoiceUpdate:k,handleTicketUpdate:_,handleStatsUpdate:C}}),Te={id:"client-dashboard"},Se={class:"header-box"},Oe={class:"stats-grid box-grid"},Ae={class:"stat-card card-box"},Ie={class:"stat-number"},Me={class:"stat-label"},Le={class:"stat-card card-box"},Pe={class:"stat-number"},Ne={class:"stat-label"},Ue={class:"stat-card card-box"},De={class:"stat-number"},xe={class:"stat-label"},Be={class:"stat-card card-box"},qe={class:"stat-number"},Ve={class:"stat-label"},He={class:"dashboard-grid box-grid"},Ge={class:"card card-box"},je={class:"card-header"},$e={class:"card-title"},Fe={class:"card-body"},We={key:0,class:"loading-state"},ze={key:1,class:"license-list"},Je={class:"license-info"},Ke={class:"license-details"},Ye={class:"license-key"},Qe=["onClick"],Xe={class:"license-domains"},Ze={class:"license-meta"},et={class:"license-date"},tt={class:"license-status"},st={key:0,class:"no-data"},nt={class:"card card-box"},it={class:"card-header"},at={class:"card-title"},rt={class:"card-body"},ot={key:0,class:"loading-state"},lt={key:1,class:"invoice-list"},ct={class:"invoice-info"},ut={class:"invoice-details"},dt={class:"invoice-number"},ht=["onClick"],pt={class:"invoice-date"},ft={class:"invoice-meta"},gt={class:"invoice-amount"},vt={class:"invoice-status"},mt={key:0,class:"no-data"},yt={class:"card card-box"},bt={class:"card-header"},wt={class:"card-title"},kt={class:"card-body"},_t={key:0,class:"loading-state"},Ct={key:1,class:"ticket-list"},Et={class:"ticket-info"},Rt={class:"ticket-details"},Tt={class:"ticket-title"},St=["onClick"],Ot={class:"ticket-id"},At={class:"ticket-meta"},It={class:"ticket-date"},Mt={class:"ticket-status"},Lt={key:0,class:"no-data"},Pt=r({__name:"DashboardView",setup(e){const t=m(),s=Re(),n=Ee(),i=Zo();function a(e){return new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e)}function r(e){const t=new Date(e);return new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"long",hour:"2-digit",minute:"2-digit"}).format(t)}function k(e){return{active:"status-badge status-success",suspended:"status-badge status-danger",paid:"status-badge status-success",unpaid:"status-badge status-warning",open:"status-badge status-info",in_progress:"status-badge status-warning",resolved:"status-badge status-success",closed:"status-badge status-secondary"}[e]||"status-badge status-default"}function _(){t.push("/licenses")}function C(){t.push("/billing")}function E(){t.push("/support")}return o(async()=>{try{J.info("[DASHBOARD] Chargement des données du dashboard"),await s.fetchDashboardData(),J.info("[DASHBOARD] Données du dashboard chargées avec succès"),i.isAuthenticated&&!n.initialized&&(await n.init(),J.info("[DASHBOARD] Service temps réel initialisé")),i.isAuthenticated&&!s.realtimeInitialized&&(await s.initRealtimeUpdates(),J.info("[DASHBOARD] Temps réel dashboard initialisé avec succès"))}catch(e){J.error("[DASHBOARD] Erreur lors du chargement des données",{error:e})}}),l(()=>{s.realtimeInitialized&&(s.stopRealtimeUpdates(),J.info("[DASHBOARD] Nettoyage du composant dashboard"))}),(e,n)=>{var i;return y(),c("div",Te,[u("div",Se,[u("h1",null,d(e.$t("dashboard.title")),1)]),u("div",Oe,[u("div",Ae,[n[0]||(n[0]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-key"})],-1)),u("div",Ie,d(h(s).totalLicenses),1),u("div",Me,d(e.$t("dashboard.my_licenses")),1)]),u("div",Le,[n[1]||(n[1]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-file-invoice"})],-1)),u("div",Pe,d((null==(i=h(s).stats)?void 0:i.invoices.unpaid)||0),1),u("div",Ne,d(e.$t("dashboard.unpaid_invoices")),1)]),u("div",Ue,[n[2]||(n[2]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-headset"})],-1)),u("div",De,d(h(s).openTicketsCount),1),u("div",xe,d(e.$t("dashboard.open_tickets")),1)]),u("div",Be,[n[3]||(n[3]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-credit-card"})],-1)),u("div",qe,d(a(h(s).totalUnpaidAmount)),1),u("div",Ve,d(e.$t("dashboard.total_due")),1)])]),u("div",He,[u("div",Ge,[u("div",je,[u("h3",$e,[n[4]||(n[4]=u("i",{class:"fas fa-key"},null,-1)),p(" "+d(e.$t("dashboard.my_licenses")),1)]),u("button",{onClick:_,class:"btn btn-sm btn-outline"},d(e.$t("dashboard.view_all")),1)]),u("div",Fe,[h(s).loading?(y(),c("div",We,n[5]||(n[5]=[u("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):(y(),c("div",ze,[(y(!0),c(g,null,v(h(s).recentLicenses,s=>(y(),c("div",{key:s.id,class:"license-item"},[u("div",Je,[n[6]||(n[6]=u("div",{class:"license-icon"},[u("i",{class:"fas fa-key"})],-1)),u("div",Ke,[u("div",Ye,[u("a",{onClick:b(e=>{return n=s.id,void t.push(`/licenses/${n}`);var n},["prevent"]),href:"#",class:"license-link"},d(s.license_key),9,Qe)]),u("div",Xe,d(s.allowed_domains),1)])]),u("div",Ze,[u("div",et,d(r(s.created_at)),1),u("div",tt,[u("span",{class:w(k(s.status))},d(e.$t("status."+s.status)),3)])])]))),128)),0===h(s).recentLicenses.length?(y(),c("div",st,d(e.$t("licenses.no_licenses")),1)):f("",!0)]))])]),u("div",nt,[u("div",it,[u("h3",at,[n[7]||(n[7]=u("i",{class:"fas fa-file-invoice"},null,-1)),p(" "+d(e.$t("dashboard.recent_invoices")),1)]),u("button",{onClick:C,class:"btn btn-sm btn-outline"},d(e.$t("dashboard.view_all")),1)]),u("div",rt,[h(s).loading?(y(),c("div",ot,n[8]||(n[8]=[u("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):(y(),c("div",lt,[(y(!0),c(g,null,v(h(s).recentInvoices,s=>(y(),c("div",{key:s.id,class:"invoice-item"},[u("div",ct,[n[9]||(n[9]=u("div",{class:"invoice-icon"},[u("i",{class:"fas fa-file-invoice"})],-1)),u("div",ut,[u("div",dt,[u("a",{onClick:b(e=>{return n=s.id,void t.push(`/billing/invoice/${n}`);var n},["prevent"]),href:"#",class:"invoice-link"},d(e.$t("billing.invoice_number"))+" #"+d(s.number),9,ht)]),u("div",pt,d(r(s.created_at)),1)])]),u("div",ft,[u("div",gt,d(a(s.amount)),1),u("div",vt,[u("span",{class:w(k(s.status))},d(e.$t("status."+s.status)),3)])])]))),128)),0===h(s).recentInvoices.length?(y(),c("div",mt,d(e.$t("billing.no_invoices")),1)):f("",!0)]))])])]),u("div",yt,[u("div",bt,[u("h3",wt,[n[10]||(n[10]=u("i",{class:"fas fa-headset"},null,-1)),p(" "+d(e.$t("dashboard.recent_tickets")),1)]),u("button",{onClick:E,class:"btn btn-sm btn-outline"},d(e.$t("dashboard.view_all")),1)]),u("div",kt,[h(s).loading?(y(),c("div",_t,n[11]||(n[11]=[u("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):(y(),c("div",Ct,[(y(!0),c(g,null,v(h(s).recentTickets,s=>(y(),c("div",{key:s.id,class:"ticket-item"},[u("div",Et,[n[12]||(n[12]=u("div",{class:"ticket-icon"},[u("i",{class:"fas fa-headset"})],-1)),u("div",Rt,[u("div",Tt,[u("a",{onClick:b(e=>{return n=s.id,void t.push(`/support/ticket/${n}`);var n},["prevent"]),href:"#",class:"ticket-link"},d(s.title),9,St)]),u("div",Ot," #"+d(s.id),1)])]),u("div",At,[u("div",It,d(r(s.created_at)),1),u("div",Mt,[u("span",{class:w(k(s.status))},d(e.$t("status."+s.status)),3)])])]))),128)),0===h(s).recentTickets.length?(y(),c("div",Lt,d(e.$t("support.no_tickets")),1)):f("",!0)]))])])])}}}),Nt=(e,t)=>{const s=e.__vccOpts||e;for(const[n,i]of t)s[n]=i;return s},Ut=Nt(Pt,[["__scopeId","data-v-dcf5c35a"]]),Dt=i("invoices",()=>{const e=n([]),t=n(!1),s=n(null),i=n(null),r=n(!1),o=a(()=>t=>e.value.find(e=>e.id===t)||null),l=a(()=>t=>e.value.filter(e=>e.status===t)),c=a(()=>e.value.filter(e=>"unpaid"===e.status)),u=a(()=>e.value.filter(e=>"paid"===e.status)),d=a(()=>e.value.filter(e=>"overdue"===e.status)),h=a(()=>c.value.reduce((e,t)=>e+t.amount,0)),p=a(()=>u.value.reduce((e,t)=>e+t.amount,0)),f=a(()=>e.value.length),g=async()=>{var n,a;t.value=!0,s.value=null;try{const t=await F.routes.client.invoice.list();return e.value=t.data,i.value=(new Date).toISOString(),J.info("[INVOICES STORE] Factures chargées",{count:e.value.length,unpaid:c.value.length,paid:u.value.length,overdue:d.value.length,totalDue:h.value}),t.data}catch(r){throw J.error("[INVOICES STORE] Erreur lors du chargement des factures",{error:r}),s.value=(null==(a=null==(n=r.response)?void 0:n.data)?void 0:a.message)||"Erreur lors du chargement des factures",r}finally{t.value=!1}};return{invoices:e,loading:t,error:s,lastUpdate:i,isUpdating:r,getInvoiceById:o,getInvoicesByStatus:l,unpaidInvoices:c,paidInvoices:u,overdueInvoices:d,totalDue:h,totalPaid:p,totalInvoices:f,fetchInvoices:g,refreshInvoices:async()=>(J.info("[INVOICES STORE] Actualisation des factures"),await g()),handleInvoiceUpdate:async t=>{if(J.info("[INVOICES STORE] Mise à jour facture reçue",{event:t}),J.info("[INVOICES STORE] 📊 ANALYSE STRUCTURE EVENT REÇU:",{event_keys:Object.keys(t),"event.action":t.action,"event.entity_type":t.entity_type,"event.data_exists":!!t.data,"event.data_keys":t.data?Object.keys(t.data):null,"event.data.invoice_exists":t.data?!!t.data.invoice:null,"event.invoice_exists":!!t.invoice,structure_complete:JSON.stringify(t,null,2)}),!t.data.invoice)return void J.error("[INVOICES STORE] ❌ AUCUNE DONNÉE FACTURE TROUVÉE dans event.data.invoice");r.value=!0;const s=t.data.invoice,n=t.action||t.data.action,a={id:s.id,client_id:s.client_id,number:s.number||`INV-${s.id}`,amount:parseFloat(s.amount||0),status:s.status||"unpaid",created_at:s.created_at,due_date:s.due_date,notes:s.notes||"",updated_at:s.updated_at};switch(J.info("[INVOICES STORE] Traitement événement facture",{action:n,invoiceId:a.id,invoiceNumber:a.number,invoiceStatus:a.status,invoiceAmount:a.amount}),n){case"invoice_create":case"create":case"created":J.info("[INVOICES STORE] Facture créée - récupération liste complète prévue",{invoiceId:a.id,invoiceNumber:a.number});try{await g(),J.info("[INVOICES STORE] Liste des factures mise à jour après création")}catch(o){J.error("[INVOICES STORE] Erreur lors de la récupération après création",{error:o});-1===e.value.findIndex(e=>e.id===a.id)&&e.value.unshift(a)}break;case"invoice_update":case"update":case"updated":const t=e.value.findIndex(e=>e.id===a.id);if(-1!==t){const s=e.value[t].status;e.value[t]={...e.value[t],...a},J.info("[INVOICES STORE] Facture mise à jour",{invoiceId:a.id,oldStatus:s,newStatus:a.status,invoiceNumber:e.value[t].number})}break;default:const s=e.value.findIndex(e=>e.id===a.id);-1!==s&&(e.value[s]={...e.value[s],...a},J.info("[INVOICES STORE] Facture mise à jour (action inconnue)",{invoiceId:a.id,action:n,invoiceNumber:e.value[s].number}))}i.value=(new Date).toISOString(),setTimeout(()=>{r.value=!1},1e3)},clearError:()=>{s.value=null},reset:()=>{e.value=[],t.value=!1,s.value=null,i.value=null,r.value=!1}}}),xt=e=>{if(!e)return!1;if("string"==typeof e&&""===e.trim())return!1;const t=new Date(e);return!isNaN(t.getTime())},Bt=e=>{if(!xt(e))return"Date non définie";try{const t=new Date(e);return new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"short",year:"numeric",timeZone:"Europe/Paris"}).format(t)}catch(t){return J.warn("[DATE_UTILS] Erreur lors du formatage de la date (court)",{date:e,error:t}),"Date invalide"}},qt=e=>{if(!xt(e))return"Date non définie";try{const t=new Date(e);return new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"long",year:"numeric",timeZone:"Europe/Paris"}).format(t)}catch(t){return J.warn("[DATE_UTILS] Erreur lors du formatage de la date (long)",{date:e,error:t}),"Date invalide"}},Vt=(e,t=!0)=>{if(!(e=>{if(null==e)return!1;if("string"==typeof e&&""===e.trim())return!1;const t=Number(e);return!isNaN(t)&&isFinite(t)})(e))return t?"Gratuit":"0,00 €";try{const s=Number(e);return 0===s&&t?"Gratuit":new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(s)}catch(s){return J.warn("[DATE_UTILS] Erreur lors du formatage du prix",{value:e,error:s}),t?"Gratuit":"0,00 €"}},Ht={id:"client-billing"},Gt={class:"billing-header"},jt={class:"billing-actions"},$t={class:"btn btn-outline btn-sm"},Ft={class:"btn btn-primary btn-sm"},Wt={class:"billing-summary"},zt={class:"summary-card"},Jt={class:"summary-value"},Kt={class:"summary-label"},Yt={class:"summary-card"},Qt={class:"summary-value"},Xt={class:"summary-label"},Zt={class:"summary-card"},es={class:"summary-value"},ts={class:"summary-label"},ss={class:"summary-card"},ns={class:"summary-value"},is={class:"summary-label"},as={class:"billing-filters"},rs={class:"filter-group"},os={class:"filter-label"},ls={value:""},cs={value:"paid"},us={value:"unpaid"},ds={value:"overdue"},hs={value:"draft"},ps={class:"filter-group"},fs={class:"filter-label"},gs={value:""},vs={value:"current-month"},ms={value:"last-month"},ys={value:"current-year"},bs={value:"last-year"},ws={class:"filter-group"},ks={class:"filter-label"},_s=["placeholder"],Cs={class:"invoices-table-container"},Es={key:0,class:"loading-state"},Rs={key:1,class:"error-state"},Ts={key:2,class:"empty-state"},Ss={key:3,class:"invoices-table"},Os=["onClick"],As={class:"invoice-actions"},Is=["onClick"],Ms=["onClick"],Ls=["onClick"],Ps=r({__name:"BillingView",setup(e){const t=m(),s=Dt(),i=Ee(),r=Zo(),R=n(""),T=n(""),S=n(""),O=a(()=>s.loading),A=a(()=>s.error),I=a(()=>s.invoices),M=async()=>{try{await s.fetchInvoices(),J.info("[BILLING VIEW] Factures chargées depuis le store",{count:I.value.length})}catch(e){J.error("[BILLING VIEW] Erreur lors du chargement des factures",{error:e})}},L=a(()=>s.totalInvoices),P=a(()=>s.unpaidInvoices),N=a(()=>s.totalDue),U=a(()=>s.totalPaid),D=a(()=>{let e=I.value;if(R.value&&(e=e.filter(e=>e.status===R.value)),T.value){const t=new Date;e=e.filter(e=>{const s=new Date(e.created_at);switch(T.value){case"current-month":return s.getMonth()===t.getMonth()&&s.getFullYear()===t.getFullYear();case"last-month":const e=new Date(t.getFullYear(),t.getMonth()-1);return s.getMonth()===e.getMonth()&&s.getFullYear()===e.getFullYear();case"current-year":return s.getFullYear()===t.getFullYear();case"last-year":return s.getFullYear()===t.getFullYear()-1;default:return!0}})}if(S.value){const t=S.value.toLowerCase();e=e.filter(e=>e.number.toLowerCase().includes(t)||e.notes&&e.notes.toLowerCase().includes(t))}return e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}),x=()=>{},B=e=>Vt(e,!1),q=Bt,V=e=>({paid:"status-badge status-paid",unpaid:"status-badge status-unpaid",draft:"status-badge status-draft",cancelled:"status-badge status-cancelled"}[e]||"status-badge"),H=e=>({paid:"Payée",unpaid:"Impayée",draft:"Brouillon",cancelled:"Annulée"}[e]||e),G=e=>{t.push(`/billing/invoice/${e}`)};return o(async()=>{var e;await M();const t=null==(e=r.user)?void 0:e.id;if(t)if(i.initialized)await i.subscribeToDashboardEvents(t),i.registerDashboardHandler("invoice-page",s.handleInvoiceUpdate),J.info("[BILLING VIEW] Handler temps réel enregistré avec clé invoice-page",{clientId:t});else{const e=i.$subscribe((n,a)=>{a.initialized&&(i.subscribeToDashboardEvents(t).then(()=>{i.registerDashboardHandler("invoice-page",s.handleInvoiceUpdate),J.info("[BILLING VIEW] Handler temps réel enregistré (après initialisation) avec clé invoice-page",{clientId:t})}),e())})}else J.error("[BILLING VIEW] ID client manquant - abandon initialisation temps réel",{user:r.user})}),l(()=>{i.unregisterDashboardHandler("invoice-page"),J.info("[BILLING VIEW] Handler temps réel supprimé pour clé invoice-page")}),(e,t)=>(y(),c("div",Ht,[u("div",Gt,[u("h1",null,[t[4]||(t[4]=u("i",{class:"fas fa-file-invoice"},null,-1)),p(" "+d(e.$t("billing.title")),1)]),u("div",jt,[u("button",$t,[t[5]||(t[5]=u("i",{class:"fas fa-download"},null,-1)),p(" "+d(e.$t("common.export")),1)]),u("button",Ft,[t[6]||(t[6]=u("i",{class:"fas fa-credit-card"},null,-1)),p(" "+d(e.$t("billing.pay_unpaid")),1)])])]),u("div",Wt,[u("div",zt,[t[7]||(t[7]=u("div",{class:"summary-icon"},[u("i",{class:"fas fa-file-invoice"})],-1)),u("div",Jt,d(L.value),1),u("div",Kt,d(e.$t("billing.total_invoices")),1)]),u("div",Yt,[t[8]||(t[8]=u("div",{class:"summary-icon"},[u("i",{class:"fas fa-exclamation-triangle"})],-1)),u("div",Qt,d(P.value.length),1),u("div",Xt,d(e.$t("billing.unpaid_invoices")),1)]),u("div",Zt,[t[9]||(t[9]=u("div",{class:"summary-icon"},[u("i",{class:"fas fa-euro-sign"})],-1)),u("div",es,d(B(N.value)),1),u("div",ts,d(e.$t("billing.amount_due")),1)]),u("div",ss,[t[10]||(t[10]=u("div",{class:"summary-icon"},[u("i",{class:"fas fa-chart-line"})],-1)),u("div",ns,d(B(U.value)),1),u("div",is,d(e.$t("billing.total_paid")),1)])]),u("div",as,[u("div",rs,[u("label",os,d(e.$t("common.status")),1),k(u("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>R.value=e),class:"filter-select",onChange:x},[u("option",ls,d(e.$t("billing.all_statuses")),1),u("option",cs,d(e.$t("status.paid")),1),u("option",us,d(e.$t("status.unpaid")),1),u("option",ds,d(e.$t("status.overdue")),1),u("option",hs,d(e.$t("billing.draft")),1)],544),[[_,R.value]])]),u("div",ps,[u("label",fs,d(e.$t("billing.period")),1),k(u("select",{"onUpdate:modelValue":t[1]||(t[1]=e=>T.value=e),class:"filter-select",onChange:x},[u("option",gs,d(e.$t("billing.all_periods")),1),u("option",vs,d(e.$t("billing.current_month")),1),u("option",ms,d(e.$t("billing.last_month")),1),u("option",ys,d(e.$t("billing.current_year")),1),u("option",bs,d(e.$t("billing.last_year")),1)],544),[[_,T.value]])]),u("div",ws,[u("label",ks,d(e.$t("common.search")),1),k(u("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>S.value=e),type:"text",class:"filter-select",placeholder:e.$t("billing.search_placeholder"),onInput:x},null,40,_s),[[C,S.value]])])]),u("div",Cs,[t[21]||(t[21]=E('<div class="table-header" data-v-fe32b381><h3 class="table-title" data-v-fe32b381><i class="fas fa-list" data-v-fe32b381></i> Historique des Factures </h3><div class="billing-actions" data-v-fe32b381><button class="btn btn-outline btn-sm" data-v-fe32b381><i class="fas fa-filter" data-v-fe32b381></i> Filtres avancés </button></div></div>',1)),O.value?(y(),c("div",Es,t[11]||(t[11]=[u("i",{class:"fas fa-spinner fa-spin"},null,-1),u("p",null,"Chargement de vos factures...",-1)]))):A.value?(y(),c("div",Rs,[t[13]||(t[13]=u("i",{class:"fas fa-exclamation-triangle"},null,-1)),t[14]||(t[14]=u("h3",null,"Erreur de chargement",-1)),u("p",null,d(A.value),1),u("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=e=>M())},t[12]||(t[12]=[u("i",{class:"fas fa-redo"},null,-1),p(" Réessayer ")]))])):0===D.value.length?(y(),c("div",Ts,[t[15]||(t[15]=u("i",{class:"fas fa-file-invoice"},null,-1)),t[16]||(t[16]=u("h3",null,"Aucune facture trouvée",-1)),u("p",null,d(S.value||R.value||T.value?"Aucune facture ne correspond à vos critères de recherche.":"Vous n'avez pas encore de factures."),1)])):(y(),c("table",Ss,[t[20]||(t[20]=u("thead",null,[u("tr",null,[u("th",null,"Numéro"),u("th",null,"Date"),u("th",null,"Description"),u("th",null,"Montant"),u("th",null,"Statut"),u("th",null,"Échéance"),u("th",null,"Actions")])],-1)),u("tbody",null,[(y(!0),c(g,null,v(D.value,e=>{return y(),c("tr",{key:e.id},[u("td",null,[u("a",{href:"#",class:"invoice-number",onClick:b(t=>G(e.id),["prevent"])}," #"+d(e.number),9,Os)]),u("td",null,d(h(q)(e.created_at)),1),u("td",null,d(e.notes||"Facture #"+e.number),1),u("td",null,[u("span",{class:w(["invoice-amount",(s=e.status,"unpaid"===s?"unpaid":"paid"===s?"paid":"cancelled"===s?"cancelled":"")])},d(B(e.amount)),3)]),u("td",null,[u("span",{class:w(V(e.status))},d(H(e.status)),3)]),u("td",null,d(h(q)(e.due_date)),1),u("td",null,[u("div",As,[u("button",{class:"btn btn-outline btn-sm",onClick:t=>G(e.id)},t[17]||(t[17]=[u("i",{class:"fas fa-eye"},null,-1),p(" Voir ")]),8,Is),u("button",{class:"btn btn-outline btn-sm",onClick:t=>{return s=e.id,void J.info("[BILLING] Téléchargement facture demandé",{invoiceId:s});var s}},t[18]||(t[18]=[u("i",{class:"fas fa-download"},null,-1),p(" PDF ")]),8,Ms),"unpaid"===e.status?(y(),c("button",{key:0,class:"btn btn-success btn-sm",onClick:t=>{return s=e.id,void J.info("[BILLING] Paiement facture demandé",{invoiceId:s});var s}},t[19]||(t[19]=[u("i",{class:"fas fa-credit-card"},null,-1),p(" Payer ")]),8,Ls)):f("",!0)])])]);var s}),128))])]))])]))}}),Ns=Nt(Ps,[["__scopeId","data-v-fe32b381"]]),Us=i("tickets",()=>{const e=n([]),t=n(!1),s=n(null),i=n(null),r=n(!1),o=a(()=>t=>e.value.find(e=>e.id===t)||null),l=a(()=>t=>e.value.filter(e=>e.status===t)),c=a(()=>e.value.filter(e=>"open"===e.status)),u=a(()=>e.value.filter(e=>"in-progress"===e.status)),d=a(()=>e.value.filter(e=>"resolved"===e.status)),h=a(()=>e.value.filter(e=>"closed"===e.status)),p=async()=>{var n,a;t.value=!0,s.value=null;try{const t=await F.routes.client.ticket.list();return e.value=t.data.map(e=>({...e,id:parseInt(String(e.id)),client_id:parseInt(String(e.client_id)),title:e.subject||e.title})),i.value=(new Date).toISOString(),J.info("[TICKETS STORE] Tickets chargés avec conversion IDs",{count:e.value.length,ticketIds:e.value.map(e=>({id:e.id,title:e.title})),open:c.value.length,inProgress:u.value.length,resolved:d.value.length,closed:h.value.length}),e.value}catch(r){throw J.error("[TICKETS STORE] Erreur lors du chargement des tickets",{error:r}),s.value=(null==(a=null==(n=r.response)?void 0:n.data)?void 0:a.message)||"Erreur lors du chargement des tickets",r}finally{t.value=!1}};return{tickets:e,loading:t,error:s,lastUpdate:i,isUpdating:r,getTicketById:o,getTicketsByStatus:l,openTickets:c,inProgressTickets:u,resolvedTickets:d,closedTickets:h,fetchTickets:p,refreshTickets:async()=>(J.info("[TICKETS STORE] Actualisation des tickets"),await p()),handleTicketUpdate:async t=>{if(J.info("[TICKETS STORE] Mise à jour ticket reçue",{event:t}),!t.data.ticket)return void J.error("[TICKETS STORE] ❌ AUCUNE DONNÉE TICKET TROUVÉE dans event.data.ticket");const s=t.data.ticket,n=t.action||t.data.action;J.info("[TICKETS STORE] Traitement événement ticket",{action:n,ticketId:s.id,ticketTitle:s.title||s.subject,currentTicketsCount:e.value.length});const a={id:parseInt(String(s.id)),title:s.subject||s.title,status:s.status,priority:s.priority,department_name:s.department_name,created_at:s.created_at,updated_at:s.updated_at,last_reply_at:s.last_reply_at,client_id:parseInt(String(s.client_id))};switch(J.info("[TICKETS STORE] Ticket converti",{originalId:s.id,convertedId:a.id,title:a.title,status:a.status}),n){case"ticket_create":case"create":case"created":-1===e.value.findIndex(e=>e.id===a.id)&&(e.value.unshift(a),J.info("[TICKETS STORE] Nouveau ticket ajouté",{ticketId:a.id,ticketTitle:a.title}));break;case"ticket_update":case"update":case"updated":J.info("[TICKETS STORE] Recherche ticket à mettre à jour",{searchId:a.id,currentTickets:e.value.map(e=>({id:e.id,title:e.title}))});const t=e.value.findIndex(e=>e.id===a.id);if(-1!==t){const s={...e.value[t]};e.value[t]={...e.value[t],...a},J.info("[TICKETS STORE] ✅ Ticket mis à jour avec succès",{ticketId:a.id,oldTitle:s.title,newTitle:e.value[t].title,oldStatus:s.status,newStatus:e.value[t].status,updateIndex:t})}else J.error("[TICKETS STORE] ❌ Ticket non trouvé pour mise à jour",{searchId:a.id,availableIds:e.value.map(e=>e.id)});break;case"ticket_delete":case"delete":case"deleted":const s=e.value.findIndex(e=>e.id===a.id);-1!==s&&(e.value.splice(s,1),J.info("[TICKETS STORE] Ticket supprimé",{ticketId:a.id,ticketTitle:a.title}));break;default:J.warn("[TICKETS STORE] Action non reconnue",{action:n})}i.value=(new Date).toISOString()}}}),Ds={id:"client-support"},xs={class:"support-header"},Bs={class:"support-actions"},qs={class:"quick-actions"},Vs={class:"action-title"},Hs={class:"action-description"},Gs={class:"action-card"},js={class:"action-title"},$s={class:"action-description"},Fs={class:"action-card"},Ws={class:"action-title"},zs={class:"action-description"},Js={class:"action-card"},Ks={class:"action-title"},Ys={class:"action-description"},Qs={class:"tickets-section"},Xs={class:"tickets-container"},Zs={class:"tickets-header"},en={class:"tickets-filters"},tn={key:0,class:"loading-state"},sn={key:1,class:"error-state"},nn={key:2,class:"empty-state"},an={key:3,class:"tickets-list"},rn={class:"ticket-info"},on={class:"ticket-icon"},ln={class:"ticket-details"},cn={class:"ticket-title"},un=["onClick"],dn={class:"ticket-id"},hn={class:"ticket-meta"},pn={class:"ticket-date"},fn={class:"ticket-status"},gn={class:"form-group"},vn={class:"form-group"},mn=["value"],yn={class:"form-group"},bn={class:"form-group"},wn=r({__name:"SupportView",setup(e){const t=m(),s=Us(),i=Ee(),r=Zo(),f=n([]),R=n(""),T=n(""),S=n(""),O=n(!1),A=n(!1),I=n(""),M=n(null),L=n({subject:"",department_id:"",priority:"medium",message:""}),P=async()=>{var e,t;try{M.value=null,await s.fetchTickets(),J.info("[SUPPORT] Tickets chargés depuis le store",{count:s.tickets.length})}catch(n){J.error("[SUPPORT] Erreur lors du chargement des tickets",{error:n}),M.value=(null==(t=null==(e=n.response)?void 0:e.data)?void 0:t.message)||"Erreur lors du chargement des tickets"}},N=async()=>{try{const e=await F.routes.client.department.list();f.value=e.data,J.info("[SUPPORT] Départements chargés",{count:f.value.length})}catch(e){J.error("[SUPPORT] Erreur lors du chargement des départements",{error:e}),f.value=[{id:1,name:"Support Technique",description:"Problèmes techniques",email:"<EMAIL>",active:!0,created_at:"",updated_at:""},{id:2,name:"Facturation",description:"Questions de facturation",email:"<EMAIL>",active:!0,created_at:"",updated_at:""},{id:3,name:"Commercial",description:"Questions commerciales",email:"<EMAIL>",active:!0,created_at:"",updated_at:""}]}},U=a(()=>{let e=s.tickets;return R.value&&(e=e.filter(e=>e.status===R.value)),T.value&&(e=e.filter(e=>e.priority===T.value)),e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}),D=()=>{},x=e=>({open:"Ouvert",answered:"Répondu","customer-reply":"En attente",closed:"Fermé"}[e]||e),B=Bt,q=e=>{if(!e)return"fas fa-ticket-alt";return{"Support Technique":"fas fa-cog",Technique:"fas fa-cog",Facturation:"fas fa-euro-sign",Commercial:"fas fa-handshake","Général":"fas fa-question-circle",Demande:"fas fa-hand-paper"}[e]||"fas fa-ticket-alt"},V=()=>{O.value=!1,I.value="",M.value=null,L.value={subject:"",department_id:"",priority:"medium",message:""}},H=async()=>{var e,t;if(L.value.subject&&L.value.message)try{A.value=!0,M.value=null,J.info("[SUPPORT] Création du ticket",{subject:L.value.subject,priority:L.value.priority});const e={subject:L.value.subject,message:L.value.message,priority:L.value.priority,department_id:L.value.department_id?parseInt(L.value.department_id):void 0};await F.routes.client.ticket.create(e),I.value="Ticket créé avec succès",V(),await s.refreshTickets()}catch(n){J.error("[SUPPORT] Erreur lors de la création du ticket",{error:n,ticketData:L.value}),M.value=(null==(t=null==(e=n.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la création du ticket"}finally{A.value=!1}else M.value="Veuillez remplir tous les champs obligatoires"};return o(async()=>{await Promise.all([P(),N()]),await(async()=>{var e;const t=null==(e=r.user)?void 0:e.id;if(t)if(i.initialized)await i.subscribeToDashboardEvents(t),i.registerDashboardHandler("ticket-page",s.handleTicketUpdate),J.info("[SUPPORT VIEW] Handler temps réel enregistré avec clé ticket-page",{clientId:t});else{const e=i.$subscribe((n,a)=>{a.initialized&&(i.subscribeToDashboardEvents(t).then(()=>{i.registerDashboardHandler("ticket-page",s.handleTicketUpdate),J.info("[SUPPORT VIEW] Handler temps réel enregistré (après initialisation) avec clé ticket-page",{clientId:t})}),e())})}else J.error("[SUPPORT VIEW] ID client manquant - abandon initialisation temps réel",{user:r.user})})()}),l(()=>{i.unregisterDashboardHandler("ticket-page"),J.info("[SUPPORT VIEW] Handler temps réel supprimé avec clé ticket-page")}),(e,n)=>(y(),c("div",Ds,[u("div",xs,[u("h1",null,[n[11]||(n[11]=u("i",{class:"fas fa-headset"},null,-1)),p(" "+d(e.$t("support.title")),1)]),u("div",Bs,[n[13]||(n[13]=u("button",{class:"btn btn-outline btn-sm"},[u("i",{class:"fas fa-book"}),p(" Base de connaissances ")],-1)),u("button",{class:"btn btn-primary btn-sm",onClick:n[0]||(n[0]=e=>O.value=!0)},[n[12]||(n[12]=u("i",{class:"fas fa-plus"},null,-1)),p(" "+d(e.$t("support.new_ticket")),1)])])]),u("div",qs,[u("div",{class:"action-card",onClick:n[1]||(n[1]=e=>O.value=!0)},[n[14]||(n[14]=u("div",{class:"action-icon"},[u("i",{class:"fas fa-plus"})],-1)),u("div",Vs,d(e.$t("support.create_ticket")),1),u("div",Hs,d(e.$t("support.create_ticket_desc")),1)]),u("div",Gs,[n[15]||(n[15]=u("div",{class:"action-icon"},[u("i",{class:"fas fa-book"})],-1)),u("div",js,d(e.$t("support.knowledge_base")),1),u("div",$s,d(e.$t("support.knowledge_base_desc")),1)]),u("div",Fs,[n[16]||(n[16]=u("div",{class:"action-icon"},[u("i",{class:"fas fa-comments"})],-1)),u("div",Ws,d(e.$t("support.live_chat")),1),u("div",zs,d(e.$t("support.live_chat_desc")),1)]),u("div",Js,[n[17]||(n[17]=u("div",{class:"action-icon"},[u("i",{class:"fas fa-phone"})],-1)),u("div",Ks,d(e.$t("support.phone_support")),1),u("div",Ys,d(e.$t("support.phone_support_desc")),1)])]),u("div",Qs,[u("div",Xs,[u("div",Zs,[n[20]||(n[20]=u("h3",{class:"tickets-title"},[u("i",{class:"fas fa-ticket-alt"}),p(" Mes Tickets de Support ")],-1)),u("div",en,[k(u("select",{"onUpdate:modelValue":n[2]||(n[2]=e=>R.value=e),class:"filter-select",onChange:D},n[18]||(n[18]=[E('<option value="" data-v-df8ea63a>Tous les statuts</option><option value="open" data-v-df8ea63a>Ouvert</option><option value="in-progress" data-v-df8ea63a>En cours</option><option value="resolved" data-v-df8ea63a>Résolu</option><option value="closed" data-v-df8ea63a>Fermé</option>',5)]),544),[[_,R.value]]),k(u("select",{"onUpdate:modelValue":n[3]||(n[3]=e=>T.value=e),class:"filter-select",onChange:D},n[19]||(n[19]=[E('<option value="" data-v-df8ea63a>Toutes les priorités</option><option value="low" data-v-df8ea63a>Faible</option><option value="medium" data-v-df8ea63a>Moyenne</option><option value="high" data-v-df8ea63a>Haute</option><option value="urgent" data-v-df8ea63a>Urgente</option>',5)]),544),[[_,T.value]])])]),h(s).loading?(y(),c("div",tn,n[21]||(n[21]=[u("i",{class:"fas fa-spinner fa-spin"},null,-1),u("p",null,"Chargement de vos tickets...",-1)]))):M.value?(y(),c("div",sn,[n[23]||(n[23]=u("i",{class:"fas fa-exclamation-triangle"},null,-1)),n[24]||(n[24]=u("h3",null,"Erreur de chargement",-1)),u("p",null,d(M.value),1),u("button",{class:"btn btn-primary",onClick:n[4]||(n[4]=e=>P())},n[22]||(n[22]=[u("i",{class:"fas fa-redo"},null,-1),p(" Réessayer ")]))])):0===U.value.length?(y(),c("div",nn,[n[26]||(n[26]=u("i",{class:"fas fa-ticket-alt"},null,-1)),n[27]||(n[27]=u("h3",null,"Aucun ticket trouvé",-1)),u("p",null,d(S.value||R.value||T.value?"Aucun ticket ne correspond à vos critères.":"Vous n'avez pas encore de tickets de support."),1),u("button",{class:"btn btn-primary",onClick:n[5]||(n[5]=e=>O.value=!0)},n[25]||(n[25]=[u("i",{class:"fas fa-plus"},null,-1),p(" Créer votre premier ticket ")]))])):(y(),c("div",an,[(y(!0),c(g,null,v(U.value,e=>{return y(),c("div",{key:e.id,class:"ticket-item"},[u("div",rn,[u("div",on,[u("i",{class:w(q(e.department_name))},null,2)]),u("div",ln,[u("div",cn,[u("a",{href:"#",onClick:b(s=>{return n=e.id,void t.push(`/support/ticket/${n}`);var n},["prevent"])},d(e.title),9,un)]),u("div",dn," #"+d(e.id)+" • "+d(e.department_name||"Général"),1)])]),u("div",hn,[u("div",pn,d(h(B)(e.created_at)),1),u("div",fn,[u("span",{class:w((s=e.status,{open:"status-badge status-open","in-progress":"status-badge status-in-progress",resolved:"status-badge status-resolved",closed:"status-badge status-closed"}[s]||"status-badge"))},d(x(e.status)),3)])])]);var s}),128))]))])]),u("div",{class:w(["modal-overlay",{show:O.value}]),onClick:V},[u("div",{class:"modal-content",onClick:n[10]||(n[10]=b(()=>{},["stop"]))},[u("div",{class:"modal-header"},[n[29]||(n[29]=u("h3",{class:"modal-title"},"Créer un Nouveau Ticket",-1)),u("button",{class:"modal-close",onClick:V},n[28]||(n[28]=[u("i",{class:"fas fa-times"},null,-1)]))]),u("form",{onSubmit:b(H,["prevent"])},[u("div",gn,[n[30]||(n[30]=u("label",{class:"form-label"},"Sujet",-1)),k(u("input",{"onUpdate:modelValue":n[6]||(n[6]=e=>L.value.subject=e),type:"text",class:"form-input",placeholder:"Décrivez brièvement votre problème...",required:""},null,512),[[C,L.value.subject]])]),u("div",vn,[n[32]||(n[32]=u("label",{class:"form-label"},"Département",-1)),k(u("select",{"onUpdate:modelValue":n[7]||(n[7]=e=>L.value.department_id=e),class:"form-select",required:""},[n[31]||(n[31]=u("option",{value:""},"Sélectionnez un département",-1)),(y(!0),c(g,null,v(f.value,e=>(y(),c("option",{key:e.id,value:e.id.toString()},d(e.name),9,mn))),128))],512),[[_,L.value.department_id]])]),u("div",yn,[n[34]||(n[34]=u("label",{class:"form-label"},"Priorité",-1)),k(u("select",{"onUpdate:modelValue":n[8]||(n[8]=e=>L.value.priority=e),class:"form-select",required:""},n[33]||(n[33]=[E('<option value="" data-v-df8ea63a>Sélectionnez une priorité</option><option value="low" data-v-df8ea63a>Faible</option><option value="medium" data-v-df8ea63a>Moyenne</option><option value="high" data-v-df8ea63a>Haute</option><option value="urgent" data-v-df8ea63a>Urgente</option>',5)]),512),[[_,L.value.priority]])]),u("div",bn,[n[35]||(n[35]=u("label",{class:"form-label"},"Description",-1)),k(u("textarea",{"onUpdate:modelValue":n[9]||(n[9]=e=>L.value.message=e),class:"form-textarea",placeholder:"Décrivez votre problème en détail...",required:""},null,512),[[C,L.value.message]])]),u("div",{class:"modal-actions"},[u("button",{type:"button",class:"btn btn-outline",onClick:V}," Annuler "),n[36]||(n[36]=u("button",{type:"submit",class:"btn btn-primary"},[u("i",{class:"fas fa-paper-plane"}),p(" Créer le Ticket ")],-1))])],32)])],2)]))}}),kn=Nt(wn,[["__scopeId","data-v-df8ea63a"]]),_n={id:"client-account"},Cn={class:"account-header"},En={class:"account-actions"},Rn={class:"btn btn-outline btn-sm"},Tn={key:0,class:"success-message"},Sn={key:1,class:"error-message"},On={class:"account-grid"},An={class:"account-card"},In={class:"card-header"},Mn={class:"card-title"},Ln={class:"card-body"},Pn={class:"profile-avatar"},Nn={class:"avatar-container"},Un={key:0,class:"avatar-upload"},Dn={class:"profile-info"},xn={class:"form-row"},Bn={class:"form-group"},qn=["disabled"],Vn={class:"form-group"},Hn=["disabled"],Gn={class:"form-group"},jn=["disabled"],$n={class:"form-row"},Fn={class:"form-group"},Wn=["disabled"],zn={class:"form-group"},Jn=["disabled"],Kn={class:"form-group"},Yn=["disabled"],Qn={class:"form-row"},Xn={class:"form-group"},Zn=["disabled"],ei={class:"form-group"},ti=["disabled"],si={class:"form-group"},ni=["disabled"],ii={key:0,class:"form-actions"},ai=["disabled"],ri={key:0,class:"fas fa-spinner fa-spin"},oi={key:1,class:"fas fa-save"},li={class:"account-card"},ci={class:"card-body"},ui={class:"security-item"},di={class:"security-status"},hi={class:"account-card"},pi={class:"card-body"},fi={class:"notification-item"},gi={class:"notification-item"},vi={class:"notification-item"},mi={class:"notification-item"},yi={class:"modal-header"},bi={class:"modal-body"},wi={class:"form-group"},ki={class:"form-group"},_i={class:"form-group"},Ci={class:"modal-actions"},Ei=["disabled"],Ri={key:0,class:"fas fa-spinner fa-spin"},Ti={key:1,class:"fas fa-save"},Si=Nt(r({__name:"AccountView",setup(e){const t=n(!0),s=n(!1),i=n(!1),a=n(!1),r=n(""),l=n(""),g=n({current_password:"",new_password:"",confirm_password:""}),v=R({firstName:"",lastName:"",email:"",phone:"",company:"",address:"",city:"",zipCode:"",country:"",createdAt:""}),m=n({...v}),T=R({email:!0,billing:!0,services:!0,marketing:!1}),S=qt,O=e=>{const t={FR:"France",BE:"Belgique",CH:"Suisse",CA:"Canada",US:"États-Unis",LU:"Luxembourg",MC:"Monaco"};return t[e]?t[e]:e},A=async()=>{var e,t;if(s.value){i.value=!0,l.value="";try{const e={firstname:v.firstName,lastname:v.lastName,email:v.email,phone:v.phone,company:v.company,address:v.address,city:v.city,postal_code:v.zipCode,country:v.country};await F.routes.client.profile.update(e),m.value={...v},s.value=!1,r.value="Profil mis à jour avec succès !",setTimeout(()=>{r.value=""},3e3)}catch(n){l.value=(null==(t=null==(e=n.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la sauvegarde. Veuillez réessayer.",setTimeout(()=>{l.value=""},5e3)}finally{i.value=!1}}},I=()=>{Object.assign(v,m.value),s.value=!1,l.value=""},M=async()=>{var e,t;if(g.value.new_password===g.value.confirm_password)if(g.value.new_password.length<8)l.value="Le mot de passe doit contenir au moins 8 caractères";else{i.value=!0,r.value="",l.value="";try{await F.routes.client.profile.changePassword({current_password:g.value.current_password,new_password:g.value.new_password}),r.value="Mot de passe changé avec succès",a.value=!1,g.value={current_password:"",new_password:"",confirm_password:""},setTimeout(()=>{r.value=""},3e3)}catch(s){l.value=(null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors du changement de mot de passe"}finally{i.value=!1}}else l.value="Les mots de passe ne correspondent pas"},L=e=>{T[e]=!T[e],r.value="Préférences de notification mises à jour",setTimeout(()=>{r.value=""},2e3)},P=()=>{const e=JSON.stringify(v,null,2),t=new Blob([e],{type:"application/json"}),s=URL.createObjectURL(t),n=document.createElement("a");n.href=s,n.download="mon-profil.json",n.click(),URL.revokeObjectURL(s)};return o(()=>{(async()=>{var e,s;try{t.value=!0,l.value="";const e=(await F.routes.client.profile.get()).data;v.firstName=e.firstname,v.lastName=e.lastname,v.email=e.email,v.phone=e.phone||"",v.company=e.company||"",v.address=e.address||"",v.city=e.city||"",v.zipCode=e.postal_code||"",v.country=O(e.country||""),v.createdAt=e.created_at,m.value={...v}}catch(n){l.value=(null==(s=null==(e=n.response)?void 0:e.data)?void 0:s.message)||"Erreur lors du chargement du profil"}finally{t.value=!1}})()}),(e,t)=>(y(),c("div",_n,[u("div",Cn,[u("h1",null,[t[22]||(t[22]=u("i",{class:"fas fa-user-cog"},null,-1)),p(" "+d(e.$t("account.title")),1)]),u("div",En,[u("button",Rn,[t[23]||(t[23]=u("i",{class:"fas fa-download"},null,-1)),p(" "+d(e.$t("common.export"))+" mes données ",1)]),u("button",{class:"btn btn-primary btn-sm",onClick:A},[t[24]||(t[24]=u("i",{class:"fas fa-save"},null,-1)),p(" "+d(e.$t("common.save")),1)])])]),r.value?(y(),c("div",Tn,[t[25]||(t[25]=u("i",{class:"fas fa-check-circle"},null,-1)),p(" "+d(r.value),1)])):f("",!0),l.value?(y(),c("div",Sn,[t[26]||(t[26]=u("i",{class:"fas fa-exclamation-triangle"},null,-1)),p(" "+d(l.value),1)])):f("",!0),u("div",On,[u("div",An,[u("div",In,[u("h3",Mn,[t[27]||(t[27]=u("i",{class:"fas fa-user"},null,-1)),p(" "+d(e.$t("account.personal_info")),1)]),u("button",{class:"btn btn-outline btn-sm",onClick:t[0]||(t[0]=e=>s.value=!s.value)},[u("i",{class:w(s.value?"fas fa-times":"fas fa-edit")},null,2),p(" "+d(s.value?e.$t("common.cancel"):e.$t("common.edit")),1)])]),u("div",Ln,[u("div",Pn,[u("div",Nn,[t[29]||(t[29]=u("div",{class:"avatar-image"},[u("i",{class:"fas fa-user"})],-1)),s.value?(y(),c("div",Un,t[28]||(t[28]=[u("i",{class:"fas fa-camera"},null,-1)]))):f("",!0)]),u("div",Dn,[u("h3",null,d(v.firstName)+" "+d(v.lastName),1),u("p",null,d(e.$t("account.member_since"))+" "+d(h(S)(v.createdAt)),1)])]),u("form",{onSubmit:b(A,["prevent"])},[u("div",xn,[u("div",Bn,[t[30]||(t[30]=u("label",{class:"form-label"},"Prénom",-1)),k(u("input",{"onUpdate:modelValue":t[1]||(t[1]=e=>v.firstName=e),type:"text",class:"form-input",disabled:!s.value,required:""},null,8,qn),[[C,v.firstName]])]),u("div",Vn,[t[31]||(t[31]=u("label",{class:"form-label"},"Nom",-1)),k(u("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>v.lastName=e),type:"text",class:"form-input",disabled:!s.value,required:""},null,8,Hn),[[C,v.lastName]])])]),u("div",Gn,[t[32]||(t[32]=u("label",{class:"form-label"},"Email",-1)),k(u("input",{"onUpdate:modelValue":t[3]||(t[3]=e=>v.email=e),type:"email",class:"form-input",disabled:!s.value,required:""},null,8,jn),[[C,v.email]])]),u("div",$n,[u("div",Fn,[t[33]||(t[33]=u("label",{class:"form-label"},"Téléphone",-1)),k(u("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>v.phone=e),type:"tel",class:"form-input",disabled:!s.value},null,8,Wn),[[C,v.phone]])]),u("div",zn,[t[34]||(t[34]=u("label",{class:"form-label"},"Société",-1)),k(u("input",{"onUpdate:modelValue":t[5]||(t[5]=e=>v.company=e),type:"text",class:"form-input",disabled:!s.value},null,8,Jn),[[C,v.company]])])]),u("div",Kn,[t[35]||(t[35]=u("label",{class:"form-label"},"Adresse",-1)),k(u("input",{"onUpdate:modelValue":t[6]||(t[6]=e=>v.address=e),type:"text",class:"form-input",disabled:!s.value},null,8,Yn),[[C,v.address]])]),u("div",Qn,[u("div",Xn,[t[36]||(t[36]=u("label",{class:"form-label"},"Ville",-1)),k(u("input",{"onUpdate:modelValue":t[7]||(t[7]=e=>v.city=e),type:"text",class:"form-input",disabled:!s.value},null,8,Zn),[[C,v.city]])]),u("div",ei,[t[37]||(t[37]=u("label",{class:"form-label"},"Code Postal",-1)),k(u("input",{"onUpdate:modelValue":t[8]||(t[8]=e=>v.zipCode=e),type:"text",class:"form-input",disabled:!s.value},null,8,ti),[[C,v.zipCode]])])]),u("div",si,[t[39]||(t[39]=u("label",{class:"form-label"},"Pays",-1)),k(u("select",{"onUpdate:modelValue":t[9]||(t[9]=e=>v.country=e),class:"form-select",disabled:!s.value},t[38]||(t[38]=[E('<option value="" data-v-bf9f5224>Sélectionnez un pays</option><option value="France" data-v-bf9f5224>France</option><option value="Belgique" data-v-bf9f5224>Belgique</option><option value="Suisse" data-v-bf9f5224>Suisse</option><option value="Canada" data-v-bf9f5224>Canada</option><option value="États-Unis" data-v-bf9f5224>États-Unis</option><option value="Luxembourg" data-v-bf9f5224>Luxembourg</option><option value="Monaco" data-v-bf9f5224>Monaco</option>',8)]),8,ni),[[_,v.country]])]),s.value?(y(),c("div",ii,[u("button",{type:"button",class:"btn btn-outline",onClick:I}," Annuler "),u("button",{type:"submit",class:"btn btn-primary",disabled:i.value},[i.value?(y(),c("i",ri)):(y(),c("i",oi)),p(" "+d(i.value?"Sauvegarde...":"Sauvegarder"),1)],8,ai)])):f("",!0)],32)])]),u("div",li,[t[43]||(t[43]=u("div",{class:"card-header"},[u("h3",{class:"card-title"},[u("i",{class:"fas fa-shield-alt"}),p(" Sécurité ")])],-1)),u("div",ci,[u("div",ui,[t[41]||(t[41]=u("div",{class:"security-info"},[u("div",{class:"security-icon"},[u("i",{class:"fas fa-key"})]),u("div",{class:"security-details"},[u("h4",null,"Mot de passe"),u("p",null,"Dernière modification il y a 3 mois")])],-1)),u("div",di,[t[40]||(t[40]=u("div",{class:"status-indicator status-warning"},null,-1)),u("button",{class:"btn btn-outline btn-sm",onClick:t[10]||(t[10]=e=>a.value=!0)}," Modifier ")])]),t[42]||(t[42]=E('<div class="security-item" data-v-bf9f5224><div class="security-info" data-v-bf9f5224><div class="security-icon" data-v-bf9f5224><i class="fas fa-mobile-alt" data-v-bf9f5224></i></div><div class="security-details" data-v-bf9f5224><h4 data-v-bf9f5224>Authentification à deux facteurs</h4><p data-v-bf9f5224>Protection supplémentaire de votre compte</p></div></div><div class="security-status" data-v-bf9f5224><div class="status-indicator status-inactive" data-v-bf9f5224></div><button class="btn btn-primary btn-sm" data-v-bf9f5224> Activer </button></div></div><div class="security-item" data-v-bf9f5224><div class="security-info" data-v-bf9f5224><div class="security-icon" data-v-bf9f5224><i class="fas fa-history" data-v-bf9f5224></i></div><div class="security-details" data-v-bf9f5224><h4 data-v-bf9f5224>Historique de connexion</h4><p data-v-bf9f5224>Dernière connexion: Aujourd&#39;hui à 14:30</p></div></div><div class="security-status" data-v-bf9f5224><div class="status-indicator status-active" data-v-bf9f5224></div><button class="btn btn-outline btn-sm" data-v-bf9f5224> Voir l&#39;historique </button></div></div>',2))])])]),u("div",hi,[t[48]||(t[48]=u("div",{class:"card-header"},[u("h3",{class:"card-title"},[u("i",{class:"fas fa-bell"}),p(" Préférences de Notification ")])],-1)),u("div",pi,[u("div",fi,[t[44]||(t[44]=u("div",{class:"notification-info"},[u("div",{class:"notification-title"},"Notifications par email"),u("div",{class:"notification-description"},"Recevoir les notifications importantes par email")],-1)),u("div",{class:w(["toggle-switch",{active:T.email}]),onClick:t[11]||(t[11]=e=>L("email"))},null,2)]),u("div",gi,[t[45]||(t[45]=u("div",{class:"notification-info"},[u("div",{class:"notification-title"},"Alertes de facturation"),u("div",{class:"notification-description"},"Être notifié des nouvelles factures et échéances")],-1)),u("div",{class:w(["toggle-switch",{active:T.billing}]),onClick:t[12]||(t[12]=e=>L("billing"))},null,2)]),u("div",vi,[t[46]||(t[46]=u("div",{class:"notification-info"},[u("div",{class:"notification-title"},"Mises à jour de services"),u("div",{class:"notification-description"},"Recevoir les informations sur vos services")],-1)),u("div",{class:w(["toggle-switch",{active:T.services}]),onClick:t[13]||(t[13]=e=>L("services"))},null,2)]),u("div",mi,[t[47]||(t[47]=u("div",{class:"notification-info"},[u("div",{class:"notification-title"},"Newsletter marketing"),u("div",{class:"notification-description"},"Recevoir nos offres et actualités")],-1)),u("div",{class:w(["toggle-switch",{active:T.marketing}]),onClick:t[14]||(t[14]=e=>L("marketing"))},null,2)])])]),u("div",{class:"account-card"},[t[52]||(t[52]=u("div",{class:"card-header"},[u("h3",{class:"card-title"},[u("i",{class:"fas fa-exclamation-triangle"}),p(" Zone de Danger ")])],-1)),u("div",{class:"card-body"},[t[51]||(t[51]=u("p",{style:{color:"var(--text-muted)","margin-bottom":"1rem"}}," Ces actions sont irréversibles. Procédez avec prudence. ",-1)),u("div",{style:{display:"flex",gap:"1rem","flex-wrap":"wrap"}},[u("button",{class:"btn btn-outline btn-sm",onClick:P},t[49]||(t[49]=[u("i",{class:"fas fa-download"},null,-1),p(" Exporter toutes mes données ")])),t[50]||(t[50]=u("button",{class:"btn btn-danger btn-sm"},[u("i",{class:"fas fa-user-times"}),p(" Supprimer mon compte ")],-1))])])]),a.value?(y(),c("div",{key:2,class:"modal show",onClick:t[21]||(t[21]=e=>a.value=!1)},[t[59]||(t[59]=u("div",{class:"modal-backdrop"},null,-1)),u("div",{class:"modal-content",onClick:t[20]||(t[20]=b(()=>{},["stop"]))},[u("div",yi,[t[54]||(t[54]=u("h3",null,[u("i",{class:"fas fa-key"}),p(" Changer le mot de passe ")],-1)),u("button",{class:"modal-close",onClick:t[15]||(t[15]=e=>a.value=!1)},t[53]||(t[53]=[u("i",{class:"fas fa-times"},null,-1)]))]),u("div",bi,[u("form",{onSubmit:b(M,["prevent"])},[u("div",wi,[t[55]||(t[55]=u("label",{for:"current_password"},"Mot de passe actuel",-1)),k(u("input",{id:"current_password","onUpdate:modelValue":t[16]||(t[16]=e=>g.value.current_password=e),type:"password",class:"form-input",required:"",autocomplete:"current-password"},null,512),[[C,g.value.current_password]])]),u("div",ki,[t[56]||(t[56]=u("label",{for:"new_password"},"Nouveau mot de passe",-1)),k(u("input",{id:"new_password","onUpdate:modelValue":t[17]||(t[17]=e=>g.value.new_password=e),type:"password",class:"form-input",required:"",minlength:"8",autocomplete:"new-password"},null,512),[[C,g.value.new_password]]),t[57]||(t[57]=u("small",{class:"form-help"},"Minimum 8 caractères",-1))]),u("div",_i,[t[58]||(t[58]=u("label",{for:"confirm_password"},"Confirmer le nouveau mot de passe",-1)),k(u("input",{id:"confirm_password","onUpdate:modelValue":t[18]||(t[18]=e=>g.value.confirm_password=e),type:"password",class:"form-input",required:"",autocomplete:"new-password"},null,512),[[C,g.value.confirm_password]])]),u("div",Ci,[u("button",{type:"button",class:"btn btn-outline",onClick:t[19]||(t[19]=e=>a.value=!1)}," Annuler "),u("button",{type:"submit",class:"btn btn-primary",disabled:i.value},[i.value?(y(),c("i",Ri)):(y(),c("i",Ti)),p(" "+d(i.value?"Sauvegarde...":"Changer le mot de passe"),1)],8,Ei)])],32)])])])):f("",!0)]))}}),[["__scopeId","data-v-bf9f5224"]]),Oi={class:"auth-container"},Ai={class:"auth-card"},Ii={key:0,class:"form-error"},Mi={key:0,class:"form-error"},Li={class:"auth-options"},Pi={class:"remember-me"},Ni={key:0,class:"form-error"},Ui=["disabled"],Di={key:0,class:"loading-spinner"},xi={class:"auth-links"},Bi=r({__name:"LoginView",setup(e){const t=m(),s=Zo(),i=n(!1),r=n({email:"",password:"",remember:!1}),l=n({email:"",password:"",general:""}),h=a(()=>r.value.email&&r.value.password&&!l.value.email&&!l.value.password),g=()=>{r.value.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.value.email)?l.value.email="":l.value.email="Format d'email invalide":l.value.email="L'adresse email est requise"},v=()=>{r.value.password?r.value.password.length<6?l.value.password="Le mot de passe doit contenir au moins 6 caractères":l.value.password="":l.value.password="Le mot de passe est requis"},_=e=>{l.value[e]&&(l.value[e]=""),l.value.general&&(l.value.general="")},R=async()=>{var e,n,a;if(g(),v(),h.value){i.value=!0,l.value.general="";try{await s.login(r.value.email,r.value.password,r.value.remember);const e=t.currentRoute.value.query.redirect||"/dashboard";await t.push(e)}catch(o){J.error("[AUTH] Erreur de connexion",{error:o,email:r.value.email}),401===(null==(e=o.response)?void 0:e.status)?l.value.general="Email ou mot de passe incorrect":403===(null==(n=o.response)?void 0:n.status)?l.value.general="Compte suspendu ou inactif":(null==(a=o.response)?void 0:a.status)>=500?l.value.general="Erreur serveur. Veuillez réessayer plus tard.":l.value.general=o.message||"Une erreur est survenue lors de la connexion"}finally{i.value=!1}}};return o(async()=>{s.isAuthenticated&&await t.push("/dashboard")}),(e,t)=>{const s=A("router-link");return y(),c("div",Oi,[u("div",Ai,[t[14]||(t[14]=E('<div class="auth-header"><div class="auth-logo"><i class="fas fa-user-circle"></i></div><h1 class="auth-title">Connexion Client</h1><p class="auth-subtitle">Accédez à votre espace client TechCMS</p></div>',1)),u("form",{onSubmit:b(R,["prevent"]),class:"auth-form"},[u("div",{class:w(["form-group",{error:l.value.email,valid:!l.value.email&&r.value.email}])},[t[6]||(t[6]=u("label",{for:"email",class:"form-label"},[u("i",{class:"fas fa-envelope"}),p(" Adresse email ")],-1)),k(u("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=e=>r.value.email=e),type:"email",class:w(["form-input",{error:l.value.email}]),placeholder:"<EMAIL>",required:"",autocomplete:"email",onBlur:g,onInput:t[1]||(t[1]=e=>_("email"))},null,34),[[C,r.value.email]]),l.value.email?(y(),c("div",Ii,[t[5]||(t[5]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.email),1)])):f("",!0)],2),u("div",{class:w(["form-group",{error:l.value.password,valid:!l.value.password&&r.value.password}])},[t[8]||(t[8]=u("label",{for:"password",class:"form-label"},[u("i",{class:"fas fa-lock"}),p(" Mot de passe ")],-1)),k(u("input",{id:"password","onUpdate:modelValue":t[2]||(t[2]=e=>r.value.password=e),type:"password",class:w(["form-input",{error:l.value.password}]),placeholder:"Votre mot de passe",required:"",autocomplete:"current-password",onBlur:v,onInput:t[3]||(t[3]=e=>_("password"))},null,34),[[C,r.value.password]]),l.value.password?(y(),c("div",Mi,[t[7]||(t[7]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.password),1)])):f("",!0)],2),u("div",Li,[u("label",Pi,[k(u("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>r.value.remember=e),type:"checkbox"},null,512),[[S,r.value.remember]]),t[9]||(t[9]=p(" Se souvenir de moi "))]),T(s,{to:"/forgot-password",class:"forgot-password"},{default:O(()=>t[10]||(t[10]=[p(" Mot de passe oublié ? ")])),_:1,__:[10]})]),l.value.general?(y(),c("div",Ni,[t[11]||(t[11]=u("i",{class:"fas fa-exclamation-triangle"},null,-1)),p(" "+d(l.value.general),1)])):f("",!0),u("button",{type:"submit",class:w(["auth-button",{loading:i.value}]),disabled:i.value||!h.value},[i.value?(y(),c("span",Di)):f("",!0),p(" "+d(i.value?"Connexion...":"Se connecter"),1)],10,Ui)],32),u("div",xi,[u("p",null,[t[13]||(t[13]=p(" Pas encore de compte ? ")),T(s,{to:"/register",class:"auth-link"},{default:O(()=>t[12]||(t[12]=[p(" Créer un compte ")])),_:1,__:[12]})])])])])}}}),qi={class:"auth-container"},Vi={class:"auth-card"},Hi={class:"form-row"},Gi={key:0,class:"form-error"},ji={key:0,class:"form-error"},$i={key:0,class:"form-error"},Fi={class:"form-row"},Wi={key:0,class:"form-error"},zi={key:0,class:"form-error"},Ji={class:"form-row"},Ki={key:0,class:"form-error"},Yi={key:0,class:"form-error"},Qi={key:0,class:"form-error"},Xi={class:"form-row"},Zi={key:0,class:"form-error"},ea={key:0,class:"form-error"},ta={class:"remember-me"},sa={key:0,class:"form-error"},na={key:0,class:"form-error"},ia={key:1,class:"form-success"},aa=["disabled"],ra={key:0,class:"loading-spinner"},oa={class:"auth-links"},la=r({__name:"RegisterView",setup(e){const t=m(),s=Zo(),i=n(!1),r=n(""),o=n({firstname:"",lastname:"",email:"",company:"",phone:"",address:"",postal_code:"",city:"",country:"",password:"",passwordConfirmation:"",acceptTerms:!1}),l=n({firstname:"",lastname:"",email:"",company:"",phone:"",address:"",postal_code:"",city:"",country:"",password:"",passwordConfirmation:"",terms:"",general:""}),h=a(()=>o.value.firstname&&o.value.lastname&&o.value.email&&o.value.phone&&o.value.address&&o.value.postal_code&&o.value.city&&o.value.country&&o.value.password&&o.value.passwordConfirmation&&o.value.acceptTerms&&!Object.values(l.value).some(e=>""!==e)),g=()=>{o.value.firstname.trim()?o.value.firstname.trim().length<2?l.value.firstname="Le prénom doit contenir au moins 2 caractères":l.value.firstname="":l.value.firstname="Le prénom est requis"},v=()=>{o.value.lastname.trim()?o.value.lastname.trim().length<2?l.value.lastname="Le nom doit contenir au moins 2 caractères":l.value.lastname="":l.value.lastname="Le nom est requis"},R=()=>{o.value.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(o.value.email)?l.value.email="":l.value.email="Format d'email invalide":l.value.email="L'adresse email est requise"},I=()=>{if(o.value.phone.trim()){/^[\+]?[0-9\s\-\(\)]{10,}$/.test(o.value.phone)?l.value.phone="":l.value.phone="Format de téléphone invalide"}else l.value.phone="Le numéro de téléphone est requis"},M=()=>{o.value.address.trim()?o.value.address.trim().length<5?l.value.address="L'adresse doit contenir au moins 5 caractères":l.value.address="":l.value.address="L'adresse est requise"},L=()=>{o.value.postal_code.trim()?/^[0-9]{5}$/.test(o.value.postal_code.trim())?l.value.postal_code="":l.value.postal_code="Le code postal doit contenir 5 chiffres":l.value.postal_code="Le code postal est requis"},P=()=>{o.value.city.trim()?o.value.city.trim().length<2?l.value.city="La ville doit contenir au moins 2 caractères":l.value.city="":l.value.city="La ville est requise"},N=()=>{o.value.country?l.value.country="":l.value.country="Le pays est requis"},U=()=>{o.value.password?o.value.password.length<8?l.value.password="Le mot de passe doit contenir au moins 8 caractères":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(o.value.password)?l.value.password="":l.value.password="Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre":l.value.password="Le mot de passe est requis",o.value.passwordConfirmation&&D()},D=()=>{o.value.passwordConfirmation?o.value.password!==o.value.passwordConfirmation?l.value.passwordConfirmation="Les mots de passe ne correspondent pas":l.value.passwordConfirmation="":l.value.passwordConfirmation="La confirmation du mot de passe est requise"},x=e=>{l.value[e]&&(l.value[e]=""),l.value.general&&(l.value.general="")},B=async()=>{var e,n;if(g(),v(),R(),I(),M(),L(),P(),N(),U(),D(),o.value.acceptTerms?l.value.terms="":l.value.terms="Vous devez accepter les conditions d'utilisation",h.value){i.value=!0,l.value.general="",r.value="";try{J.info("[AUTH] Début inscription",{email:o.value.email,firstName:o.value.firstName}),await s.register(o.value),J.info("[AUTH] Inscription réussie",{email:o.value.email}),r.value="Compte créé avec succès ! Redirection vers la connexion...",setTimeout(()=>{t.push("/client/login")},2e3)}catch(a){J.error("[AUTH] Erreur inscription",{error:a,email:o.value.email}),409===(null==(e=a.response)?void 0:e.status)?l.value.email="Cette adresse email est déjà utilisée":(null==(n=a.response)?void 0:n.status)>=500?l.value.general="Erreur serveur. Veuillez réessayer plus tard.":l.value.general=a.message||"Une erreur est survenue lors de l'inscription"}finally{i.value=!1}}};return(e,t)=>{const s=A("router-link");return y(),c("div",qi,[u("div",Vi,[t[53]||(t[53]=E('<div class="auth-header"><div class="auth-logo"><i class="fas fa-user-plus"></i></div><h1 class="auth-title">Créer un compte</h1><p class="auth-subtitle">Rejoignez TechCMS et accédez à nos services</p></div>',1)),u("form",{onSubmit:b(B,["prevent"]),class:"auth-form"},[u("div",Hi,[u("div",{class:w(["form-group",{error:l.value.firstname,valid:!l.value.firstname&&o.value.firstname}])},[t[23]||(t[23]=u("label",{for:"firstname",class:"form-label"},[u("i",{class:"fas fa-user"}),p(" Prénom ")],-1)),k(u("input",{id:"firstname","onUpdate:modelValue":t[0]||(t[0]=e=>o.value.firstname=e),type:"text",class:w(["form-input",{error:l.value.firstname}]),placeholder:"Votre prénom",required:"",autocomplete:"given-name",onBlur:g,onInput:t[1]||(t[1]=e=>x("firstname"))},null,34),[[C,o.value.firstname]]),l.value.firstname?(y(),c("div",Gi,[t[22]||(t[22]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.firstname),1)])):f("",!0)],2),u("div",{class:w(["form-group",{error:l.value.lastname,valid:!l.value.lastname&&o.value.lastname}])},[t[25]||(t[25]=u("label",{for:"lastname",class:"form-label"},[u("i",{class:"fas fa-user"}),p(" Nom ")],-1)),k(u("input",{id:"lastname","onUpdate:modelValue":t[2]||(t[2]=e=>o.value.lastname=e),type:"text",class:w(["form-input",{error:l.value.lastname}]),placeholder:"Votre nom",required:"",autocomplete:"family-name",onBlur:v,onInput:t[3]||(t[3]=e=>x("lastname"))},null,34),[[C,o.value.lastname]]),l.value.lastname?(y(),c("div",ji,[t[24]||(t[24]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.lastname),1)])):f("",!0)],2)]),u("div",{class:w(["form-group",{error:l.value.email,valid:!l.value.email&&o.value.email}])},[t[27]||(t[27]=u("label",{for:"email",class:"form-label"},[u("i",{class:"fas fa-envelope"}),p(" Adresse email ")],-1)),k(u("input",{id:"email","onUpdate:modelValue":t[4]||(t[4]=e=>o.value.email=e),type:"email",class:w(["form-input",{error:l.value.email}]),placeholder:"<EMAIL>",required:"",autocomplete:"email",onBlur:R,onInput:t[5]||(t[5]=e=>x("email"))},null,34),[[C,o.value.email]]),l.value.email?(y(),c("div",$i,[t[26]||(t[26]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.email),1)])):f("",!0)],2),u("div",Fi,[u("div",{class:w(["form-group",{error:l.value.phone,valid:!l.value.phone&&o.value.phone}])},[t[29]||(t[29]=u("label",{for:"phone",class:"form-label"},[u("i",{class:"fas fa-phone"}),p(" Téléphone ")],-1)),k(u("input",{id:"phone","onUpdate:modelValue":t[6]||(t[6]=e=>o.value.phone=e),type:"tel",class:w(["form-input",{error:l.value.phone}]),placeholder:"+33 1 23 45 67 89",required:"",autocomplete:"tel",onBlur:I,onInput:t[7]||(t[7]=e=>x("phone"))},null,34),[[C,o.value.phone]]),l.value.phone?(y(),c("div",Wi,[t[28]||(t[28]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.phone),1)])):f("",!0)],2),u("div",{class:w(["form-group",{error:l.value.company}])},[t[30]||(t[30]=u("label",{for:"company",class:"form-label"},[u("i",{class:"fas fa-building"}),p(" Entreprise (optionnel) ")],-1)),k(u("input",{id:"company","onUpdate:modelValue":t[8]||(t[8]=e=>o.value.company=e),type:"text",class:"form-input",placeholder:"Nom de votre entreprise",autocomplete:"organization"},null,512),[[C,o.value.company]])],2)]),u("div",{class:w(["form-group",{error:l.value.address,valid:!l.value.address&&o.value.address}])},[t[32]||(t[32]=u("label",{for:"address",class:"form-label"},[u("i",{class:"fas fa-map-marker-alt"}),p(" Adresse ")],-1)),k(u("input",{id:"address","onUpdate:modelValue":t[9]||(t[9]=e=>o.value.address=e),type:"text",class:w(["form-input",{error:l.value.address}]),placeholder:"123 Rue de la Paix",required:"",autocomplete:"street-address",onBlur:M,onInput:t[10]||(t[10]=e=>x("address"))},null,34),[[C,o.value.address]]),l.value.address?(y(),c("div",zi,[t[31]||(t[31]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.address),1)])):f("",!0)],2),u("div",Ji,[u("div",{class:w(["form-group",{error:l.value.postal_code,valid:!l.value.postal_code&&o.value.postal_code}])},[t[34]||(t[34]=u("label",{for:"postal_code",class:"form-label"},[u("i",{class:"fas fa-mail-bulk"}),p(" Code postal ")],-1)),k(u("input",{id:"postal_code","onUpdate:modelValue":t[11]||(t[11]=e=>o.value.postal_code=e),type:"text",class:w(["form-input",{error:l.value.postal_code}]),placeholder:"75001",required:"",autocomplete:"postal-code",onBlur:L,onInput:t[12]||(t[12]=e=>x("postal_code"))},null,34),[[C,o.value.postal_code]]),l.value.postal_code?(y(),c("div",Ki,[t[33]||(t[33]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.postal_code),1)])):f("",!0)],2),u("div",{class:w(["form-group",{error:l.value.city,valid:!l.value.city&&o.value.city}])},[t[36]||(t[36]=u("label",{for:"city",class:"form-label"},[u("i",{class:"fas fa-city"}),p(" Ville ")],-1)),k(u("input",{id:"city","onUpdate:modelValue":t[13]||(t[13]=e=>o.value.city=e),type:"text",class:w(["form-input",{error:l.value.city}]),placeholder:"Paris",required:"",autocomplete:"address-level2",onBlur:P,onInput:t[14]||(t[14]=e=>x("city"))},null,34),[[C,o.value.city]]),l.value.city?(y(),c("div",Yi,[t[35]||(t[35]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.city),1)])):f("",!0)],2)]),u("div",{class:w(["form-group",{error:l.value.country,valid:!l.value.country&&o.value.country}])},[t[39]||(t[39]=u("label",{for:"country",class:"form-label"},[u("i",{class:"fas fa-globe"}),p(" Pays ")],-1)),k(u("select",{id:"country","onUpdate:modelValue":t[15]||(t[15]=e=>o.value.country=e),class:w(["form-input",{error:l.value.country}]),required:"",autocomplete:"country",onBlur:N,onChange:t[16]||(t[16]=e=>x("country"))},t[37]||(t[37]=[E('<option value="">Sélectionnez un pays</option><option value="FR">France</option><option value="BE">Belgique</option><option value="CH">Suisse</option><option value="CA">Canada</option><option value="US">États-Unis</option><option value="GB">Royaume-Uni</option><option value="DE">Allemagne</option><option value="ES">Espagne</option><option value="IT">Italie</option><option value="NL">Pays-Bas</option>',11)]),34),[[_,o.value.country]]),l.value.country?(y(),c("div",Qi,[t[38]||(t[38]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.country),1)])):f("",!0)],2),u("div",Xi,[u("div",{class:w(["form-group",{error:l.value.password,valid:!l.value.password&&o.value.password}])},[t[41]||(t[41]=u("label",{for:"password",class:"form-label"},[u("i",{class:"fas fa-lock"}),p(" Mot de passe ")],-1)),k(u("input",{id:"password","onUpdate:modelValue":t[17]||(t[17]=e=>o.value.password=e),type:"password",class:w(["form-input",{error:l.value.password}]),placeholder:"Choisissez un mot de passe sécurisé",required:"",autocomplete:"new-password",onBlur:U,onInput:t[18]||(t[18]=e=>x("password"))},null,34),[[C,o.value.password]]),l.value.password?(y(),c("div",Zi,[t[40]||(t[40]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.password),1)])):f("",!0)],2),u("div",{class:w(["form-group",{error:l.value.passwordConfirmation,valid:!l.value.passwordConfirmation&&o.value.passwordConfirmation}])},[t[43]||(t[43]=u("label",{for:"passwordConfirmation",class:"form-label"},[u("i",{class:"fas fa-lock"}),p(" Confirmer le mot de passe ")],-1)),k(u("input",{id:"passwordConfirmation","onUpdate:modelValue":t[19]||(t[19]=e=>o.value.passwordConfirmation=e),type:"password",class:w(["form-input",{error:l.value.passwordConfirmation}]),placeholder:"Confirmez votre mot de passe",required:"",autocomplete:"new-password",onBlur:D,onInput:t[20]||(t[20]=e=>x("passwordConfirmation"))},null,34),[[C,o.value.passwordConfirmation]]),l.value.passwordConfirmation?(y(),c("div",ea,[t[42]||(t[42]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.passwordConfirmation),1)])):f("",!0)],2)]),u("div",{class:w(["form-group",{error:l.value.terms}])},[u("label",ta,[k(u("input",{"onUpdate:modelValue":t[21]||(t[21]=e=>o.value.acceptTerms=e),type:"checkbox",required:""},null,512),[[S,o.value.acceptTerms]]),t[44]||(t[44]=p(" J'accepte les ")),t[45]||(t[45]=u("a",{href:"/terms",target:"_blank",class:"auth-link"},"conditions d'utilisation",-1)),t[46]||(t[46]=p(" et la ")),t[47]||(t[47]=u("a",{href:"/privacy",target:"_blank",class:"auth-link"},"politique de confidentialité",-1))]),l.value.terms?(y(),c("div",sa,[t[48]||(t[48]=u("i",{class:"fas fa-exclamation-circle"},null,-1)),p(" "+d(l.value.terms),1)])):f("",!0)],2),l.value.general?(y(),c("div",na,[t[49]||(t[49]=u("i",{class:"fas fa-exclamation-triangle"},null,-1)),p(" "+d(l.value.general),1)])):f("",!0),r.value?(y(),c("div",ia,[t[50]||(t[50]=u("i",{class:"fas fa-check-circle"},null,-1)),p(" "+d(r.value),1)])):f("",!0),u("button",{type:"submit",class:w(["auth-button",{loading:i.value}]),disabled:i.value||!h.value},[i.value?(y(),c("span",ra)):f("",!0),p(" "+d(i.value?"Création...":"Créer mon compte"),1)],10,aa)],32),u("div",oa,[u("p",null,[t[52]||(t[52]=p(" Déjà un compte ? ")),T(s,{to:"/auth/login",class:"auth-link"},{default:O(()=>t[51]||(t[51]=[p(" Se connecter ")])),_:1,__:[51]})])])])])}}}),ca=i("licenses",()=>{const e=n([]),t=n(null),s=n([]),i=n(!1),r=n(null),o=n(null),l=n(!1),c=n({total_licenses:0,active_licenses:0,expired_licenses:0,expiring_soon:0}),u=a(()=>t=>e.value.find(e=>e.id===t)||null),d=a(()=>e.value.filter(e=>"active"===e.status)),h=a(()=>e.value.filter(e=>"expired"===e.status)),p=a(()=>{const t=new Date,s=new Date(t.getTime()+2592e6);return e.value.filter(e=>{if(!e.expires_at||"active"!==e.status)return!1;const n=new Date(e.expires_at);return n<=s&&n>t})}),f=a(()=>e.value.length>0),g=()=>{c.value={total_licenses:e.value.length,active_licenses:d.value.length,expired_licenses:h.value.length,expiring_soon:p.value.length}};return{licenses:e,currentLicense:t,licenseUsage:s,loading:i,error:r,lastUpdate:o,isUpdating:l,stats:c,getLicenseById:u,activeLicenses:d,expiredLicenses:h,expiringLicenses:p,hasLicenses:f,fetchLicenses:async()=>{if(!i.value){i.value=!0,r.value=null;try{J.info("[LICENSES STORE] Récupération des licences...");const t=await F.routes.client.license.list();if(!t.data.success)throw new Error(t.data.message||"Erreur lors de la récupération des licences");e.value=t.data.data,g(),o.value=(new Date).toISOString(),J.info("[LICENSES STORE] Licences récupérées avec succès",{count:e.value.length})}catch(t){r.value=t.message||"Erreur lors de la récupération des licences",J.error("[LICENSES STORE] Erreur lors de la récupération des licences",t)}finally{i.value=!1}}},fetchLicenseDetail:async e=>{i.value=!0,r.value=null;try{J.info("[LICENSES STORE] Récupération du détail de la licence",{id:e});const n=await F.routes.client.license.get(e);if(n.data.success)return t.value=n.data.data,s.value=n.data.usage||[],J.info("[LICENSES STORE] Détail de la licence récupéré avec succès",{id:e}),n.data.data;throw new Error(n.data.message||"Erreur lors de la récupération du détail")}catch(n){throw r.value=n.message||"Erreur lors de la récupération du détail",J.error("[LICENSES STORE] Erreur lors de la récupération du détail",n),n}finally{i.value=!1}},verifyLicense:async(e,t)=>{try{J.info("[LICENSES STORE] Vérification de licence",{licenseKey:e,domain:t});const s=await F.routes.client.license.verify(e,t),n={success:s.data.success,message:s.data.message,license:s.data.license,verification_date:(new Date).toISOString()};return J.info("[LICENSES STORE] Vérification terminée",n),n}catch(s){return J.error("[LICENSES STORE] Erreur lors de la vérification",s),{success:!1,message:s.message||"Erreur lors de la vérification",verification_date:(new Date).toISOString()}}},handleRealtimeEvent:s=>{var n;if(!l.value){l.value=!0;try{if(J.debug("[LICENSES STORE] Événement temps réel reçu",s),"license_updated"===s.type&&s.data){const i=s.data,a=e.value.findIndex(e=>e.id===i.id);-1!==a&&(e.value[a]=i,g(),(null==(n=t.value)?void 0:n.id)===i.id&&(t.value=i),J.info("[LICENSES STORE] Licence mise à jour via temps réel",{id:i.id}))}}catch(i){J.error("[LICENSES STORE] Erreur lors du traitement de l'événement temps réel",i)}finally{l.value=!1}}},clearError:()=>{r.value=null},clearCurrentLicense:()=>{t.value=null,s.value=[]},formatExpiryDate:e=>{if(!e)return"Aucune expiration";const t=new Date(e),s=new Date;if(t<s)return"Expirée";const n=t.getTime()-s.getTime(),i=Math.ceil(n/864e5);return i<=30?`Expire dans ${i} jour${i>1?"s":""}`:t.toLocaleDateString("fr-FR")},getStatusColor:e=>{switch(e){case"active":return"success";case"inactive":return"warning";case"expired":return"danger";default:return"secondary"}}}}),ua={id:"client-licenses"},da={class:"header-box"},ha={class:"stats-grid box-grid"},pa={class:"stat-card card-box"},fa={class:"stat-number"},ga={class:"stat-label"},va={class:"stat-card card-box"},ma={class:"stat-number"},ya={class:"stat-label"},ba={class:"stat-card card-box"},wa={class:"stat-number"},ka={class:"stat-label"},_a={class:"stat-card card-box"},Ca={class:"stat-number"},Ea={class:"stat-label"},Ra={class:"card card-box"},Ta={class:"card-header"},Sa={class:"card-title"},Oa={class:"card-body"},Aa={key:0,class:"loading-state"},Ia={key:1,class:"error-state"},Ma={key:2,class:"empty-state"},La={key:3,class:"licenses-list"},Pa=["onClick"],Na={class:"license-header"},Ua={class:"license-key"},Da={class:"license-status"},xa={class:"license-details"},Ba={class:"license-info"},qa={class:"info-item"},Va={class:"info-item"},Ha={class:"info-item"},Ga={class:"license-actions"},ja=["onClick"],$a={key:0,class:"license-usage"},Fa={class:"usage-bar"},Wa={class:"usage-label"},za={class:"progress"},Ja={class:"usage-text"},Ka=Nt(r({__name:"LicensesView",setup(e){const t=m(),{t:s}=I(),n=ca(),i=Ee(),a=async()=>{try{await n.fetchLicenses()}catch(e){J.error("[LicensesView] Erreur lors du chargement des licences",e)}},r=()=>{n.clearError(),a()},k=e=>{t.push(`/licenses/${e}`)},_=e=>{e.type.startsWith("license_")&&n.handleRealtimeEvent(e)};return o(async()=>{J.info("[LicensesView] Composant monté"),await a(),i.subscribe("licenses",_)}),l(()=>{J.info("[LicensesView] Composant démonté"),i.unsubscribe("licenses",_),n.clearError()}),(e,t)=>(y(),c("div",ua,[u("div",da,[u("h1",null,d(e.$t("licenses.title")),1)]),u("div",ha,[u("div",pa,[t[0]||(t[0]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-key"})],-1)),u("div",fa,d(h(n).stats.total_licenses),1),u("div",ga,d(e.$t("licenses.stats.total")),1)]),u("div",va,[t[1]||(t[1]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-check-circle"})],-1)),u("div",ma,d(h(n).stats.active_licenses),1),u("div",ya,d(e.$t("licenses.stats.active")),1)]),u("div",ba,[t[2]||(t[2]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-exclamation-triangle"})],-1)),u("div",wa,d(h(n).stats.expiring_soon),1),u("div",ka,d(e.$t("licenses.stats.expiring_soon")),1)]),u("div",_a,[t[3]||(t[3]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-times-circle"})],-1)),u("div",Ca,d(h(n).stats.expired_licenses),1),u("div",Ea,d(e.$t("licenses.stats.expired")),1)])]),u("div",Ra,[u("div",Ta,[u("h3",Sa,[t[4]||(t[4]=u("i",{class:"fas fa-list"},null,-1)),p(" "+d(e.$t("licenses.my_licenses")),1)])]),u("div",Oa,[h(n).loading?(y(),c("div",Aa,[t[5]||(t[5]=u("div",{class:"spinner"},null,-1)),u("p",null,d(e.$t("common.loading")),1)])):h(n).error?(y(),c("div",Ia,[t[6]||(t[6]=u("div",{class:"error-icon"},[u("i",{class:"fas fa-exclamation-triangle"})],-1)),u("p",null,d(h(n).error),1),u("button",{onClick:r,class:"btn btn-primary"},d(e.$t("common.retry")),1)])):h(n).hasLicenses?(y(),c("div",La,[(y(!0),c(g,null,v(h(n).licenses,s=>(y(),c("div",{key:s.id,class:"license-item",onClick:e=>k(s.id)},[u("div",Na,[u("div",Ua,[t[8]||(t[8]=u("i",{class:"fas fa-key"},null,-1)),u("code",null,d(s.license_key),1)]),u("div",Da,[u("span",{class:w(["status-badge",`status-${h(n).getStatusColor(s.status)}`])},d(e.$t(`licenses.status.${s.status}`)),3)])]),u("div",xa,[u("div",Ba,[u("div",qa,[t[9]||(t[9]=u("i",{class:"fas fa-globe"},null,-1)),u("span",null,d(e.$t("licenses.domain_limit"))+": "+d(s.domain_limit),1)]),u("div",Va,[t[10]||(t[10]=u("i",{class:"fas fa-download"},null,-1)),u("span",null,d(e.$t("licenses.installation_limit"))+": "+d(s.installation_limit),1)]),u("div",Ha,[t[11]||(t[11]=u("i",{class:"fas fa-calendar"},null,-1)),u("span",null,d(h(n).formatExpiryDate(s.expires_at)),1)])]),u("div",Ga,[u("button",{onClick:b(e=>k(s.id),["stop"]),class:"btn btn-sm btn-outline"},d(e.$t("licenses.view_details")),9,ja)])]),void 0!==s.current_installations?(y(),c("div",$a,[u("div",Fa,[u("div",Wa,d(e.$t("licenses.installations_used")),1),u("div",za,[u("div",{class:w(["progress-bar",{"progress-bar-warning":s.current_installations/s.installation_limit>.8,"progress-bar-danger":s.current_installations/s.installation_limit>=1}]),style:M({width:Math.min(100,s.current_installations/s.installation_limit*100)+"%"})},null,6)]),u("div",Ja,d(s.current_installations)+" / "+d(s.installation_limit),1)])])):f("",!0)],8,Pa))),128))])):(y(),c("div",Ma,[t[7]||(t[7]=u("div",{class:"empty-icon"},[u("i",{class:"fas fa-key"})],-1)),u("h4",null,d(e.$t("licenses.no_licenses.title")),1),u("p",null,d(e.$t("licenses.no_licenses.description")),1)]))])])]))}}),[["__scopeId","data-v-1d288f12"]]),Ya={id:"license-detail"},Qa={class:"header-box"},Xa={class:"header-content"},Za={key:0,class:"loading-state"},er={key:1,class:"error-state"},tr={key:2,class:"license-detail-content"},sr={class:"card card-box"},nr={class:"card-header"},ir={class:"card-title"},ar={class:"card-body"},rr={class:"license-info-grid"},or={class:"info-group"},lr={class:"license-key-display"},cr=["title"],ur={class:"info-group"},dr={class:"info-group"},hr={class:"info-group"},pr={class:"info-group"},fr={class:"info-group"},gr={class:"card card-box"},vr={class:"card-header"},mr={class:"card-title"},yr={class:"card-body"},br={class:"restrictions-grid"},wr={class:"restriction-group"},kr={key:0,class:"restriction-list"},_r={key:1,class:"no-restrictions"},Cr={class:"restriction-group"},Er={key:0,class:"restriction-list"},Rr={key:1,class:"no-restrictions"},Tr={key:0,class:"card card-box"},Sr={class:"card-header"},Or={class:"card-title"},Ar={class:"card-body"},Ir={class:"usage-list"},Mr={class:"usage-info"},Lr={class:"usage-domain"},Pr={class:"usage-ip"},Nr={class:"usage-count"},Ur={class:"usage-date"},Dr={class:"card card-box"},xr={class:"card-header"},Br={class:"card-title"},qr={class:"card-body"},Vr={class:"actions-grid"},Hr=["disabled"],Gr={key:0},jr={key:1},$r=["disabled"],Fr=Nt(r({__name:"LicenseDetailView",setup(e){const t=L(),s=m(),{t:i}=I(),r=ca(),b=Ee(),k=n(!1),_=a(()=>parseInt(t.params.id)),C=a(()=>r.currentLicense),E=a(()=>{var e;return(null==(e=C.value)?void 0:e.allowed_domains)?C.value.allowed_domains.split(",").filter(e=>e.trim()):[]}),R=a(()=>{var e;return(null==(e=C.value)?void 0:e.allowed_ips)?C.value.allowed_ips.split(",").filter(e=>e.trim()):[]}),T=async()=>{try{await r.fetchLicenseDetail(_.value)}catch(e){J.error("[LicenseDetailView] Erreur lors du chargement du détail",e)}},S=()=>{r.clearError(),T()},O=()=>{s.push("/licenses")},A=async()=>{if(C.value)try{await navigator.clipboard.writeText(C.value.license_key)}catch(e){J.error("[LicenseDetailView] Erreur lors de la copie",e)}},M=async()=>{if(C.value){k.value=!0;try{const e=await r.verifyLicense(C.value.license_key,window.location.hostname);J.info("[LicenseDetailView] Résultat de la vérification",e)}catch(e){J.error("[LicenseDetailView] Erreur lors de la vérification",e)}finally{k.value=!1}}},P=()=>{T()},N=e=>new Date(e).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),U=e=>{var t;"license_updated"===e.type&&(null==(t=e.data)?void 0:t.id)===_.value&&r.handleRealtimeEvent(e)};return o(async()=>{J.info("[LicenseDetailView] Composant monté",{licenseId:_.value}),await T(),b.subscribe("license-detail",U)}),l(()=>{J.info("[LicenseDetailView] Composant démonté"),b.unsubscribe("license-detail",U),r.clearError(),r.clearCurrentLicense()}),(e,t)=>(y(),c("div",Ya,[u("div",Qa,[u("div",Xa,[u("button",{onClick:O,class:"btn btn-outline btn-sm"},[t[0]||(t[0]=u("i",{class:"fas fa-arrow-left"},null,-1)),p(" "+d(e.$t("common.back")),1)]),u("h1",null,d(e.$t("licenses.license_details")),1)])]),h(r).loading?(y(),c("div",Za,[t[1]||(t[1]=u("div",{class:"spinner"},null,-1)),u("p",null,d(e.$t("common.loading")),1)])):h(r).error?(y(),c("div",er,[t[2]||(t[2]=u("div",{class:"error-icon"},[u("i",{class:"fas fa-exclamation-triangle"})],-1)),u("p",null,d(h(r).error),1),u("button",{onClick:S,class:"btn btn-primary"},d(e.$t("common.retry")),1)])):C.value?(y(),c("div",tr,[u("div",sr,[u("div",nr,[u("h3",ir,[t[3]||(t[3]=u("i",{class:"fas fa-key"},null,-1)),p(" "+d(e.$t("licenses.license_information")),1)]),u("span",{class:w(["status-badge",`status-${h(r).getStatusColor(C.value.status)}`])},d(e.$t(`licenses.status.${C.value.status}`)),3)]),u("div",ar,[u("div",rr,[u("div",or,[u("label",null,d(e.$t("licenses.license_key")),1),u("div",lr,[u("code",null,d(C.value.license_key),1),u("button",{onClick:A,class:"btn btn-sm btn-outline",title:e.$t("common.copy")},t[4]||(t[4]=[u("i",{class:"fas fa-copy"},null,-1)]),8,cr)])]),u("div",ur,[u("label",null,d(e.$t("licenses.status.label")),1),u("span",{class:w(["status-badge",`status-${h(r).getStatusColor(C.value.status)}`])},d(e.$t(`licenses.status.${C.value.status}`)),3)]),u("div",dr,[u("label",null,d(e.$t("licenses.domain_limit")),1),u("span",null,d(C.value.domain_limit),1)]),u("div",hr,[u("label",null,d(e.$t("licenses.installation_limit")),1),u("span",null,d(C.value.installation_limit),1)]),u("div",pr,[u("label",null,d(e.$t("licenses.expires_at")),1),u("span",null,d(h(r).formatExpiryDate(C.value.expires_at)),1)]),u("div",fr,[u("label",null,d(e.$t("licenses.created_at")),1),u("span",null,d(N(C.value.created_at)),1)])])])]),u("div",gr,[u("div",vr,[u("h3",mr,[t[5]||(t[5]=u("i",{class:"fas fa-shield-alt"},null,-1)),p(" "+d(e.$t("licenses.restrictions")),1)])]),u("div",yr,[u("div",br,[u("div",wr,[u("h4",null,d(e.$t("licenses.allowed_domains")),1),E.value.length>0?(y(),c("div",kr,[(y(!0),c(g,null,v(E.value,e=>(y(),c("div",{key:e,class:"restriction-item"},[t[6]||(t[6]=u("i",{class:"fas fa-globe"},null,-1)),u("span",null,d(e),1)]))),128))])):(y(),c("div",_r,d(e.$t("licenses.no_domain_restrictions")),1))]),u("div",Cr,[u("h4",null,d(e.$t("licenses.allowed_ips")),1),R.value.length>0?(y(),c("div",Er,[(y(!0),c(g,null,v(R.value,e=>(y(),c("div",{key:e,class:"restriction-item"},[t[7]||(t[7]=u("i",{class:"fas fa-network-wired"},null,-1)),u("span",null,d(e),1)]))),128))])):(y(),c("div",Rr,d(e.$t("licenses.no_ip_restrictions")),1))])])])]),h(r).licenseUsage.length>0?(y(),c("div",Tr,[u("div",Sr,[u("h3",Or,[t[8]||(t[8]=u("i",{class:"fas fa-chart-bar"},null,-1)),p(" "+d(e.$t("licenses.current_usage")),1)])]),u("div",Ar,[u("div",Ir,[(y(!0),c(g,null,v(h(r).licenseUsage,e=>(y(),c("div",{key:`${e.domain}-${e.ip}`,class:"usage-item"},[u("div",Mr,[u("div",Lr,[t[9]||(t[9]=u("i",{class:"fas fa-globe"},null,-1)),u("span",null,d(e.domain),1)]),u("div",Pr,[t[10]||(t[10]=u("i",{class:"fas fa-network-wired"},null,-1)),u("span",null,d(e.ip),1)]),u("div",Nr,[t[11]||(t[11]=u("i",{class:"fas fa-download"},null,-1)),u("span",null,d(e.installation_count)+" installation"+d(e.installation_count>1?"s":""),1)])]),u("div",Ur,[t[12]||(t[12]=u("i",{class:"fas fa-clock"},null,-1)),u("span",null,d(N(e.last_seen)),1)])]))),128))])])])):f("",!0),u("div",Dr,[u("div",xr,[u("h3",Br,[t[13]||(t[13]=u("i",{class:"fas fa-tools"},null,-1)),p(" "+d(e.$t("licenses.actions")),1)])]),u("div",qr,[u("div",Vr,[u("button",{onClick:M,class:"btn btn-primary",disabled:k.value},[t[14]||(t[14]=u("i",{class:"fas fa-check-circle"},null,-1)),k.value?(y(),c("span",Gr,d(e.$t("licenses.verifying")),1)):(y(),c("span",jr,d(e.$t("licenses.verify_license")),1))],8,Hr),u("button",{onClick:P,class:"btn btn-outline",disabled:h(r).loading},[t[15]||(t[15]=u("i",{class:"fas fa-sync-alt"},null,-1)),p(" "+d(e.$t("licenses.refresh")),1)],8,$r)])])])])):f("",!0)]))}}),[["__scopeId","data-v-ae104061"]]),Wr=i("updates",()=>{const e=n([]),t=n(null),s=n(null),i=n(null),r=n(null),o=n(!1),l=n(!1),c=n(null),u=n("Stable"),d=n(!1),h=a(()=>{if(!u.value)return[];const t={Stable:"stable",Beta:"beta",Dev:"development","Obsolète":"deprecated"}[u.value]||u.value.toLowerCase();return e.value.filter(e=>e.status===t).sort((e,t)=>new Date(t.release_date).getTime()-new Date(e.release_date).getTime())}),p=a(()=>e.value.filter(e=>"stable"===e.status).sort((e,t)=>new Date(t.release_date).getTime()-new Date(e.release_date).getTime())[0]),f=a(()=>{var e,t;const s=(null==(e=i.value)?void 0:e.update_permissions)||!1;return J.info("[UpdatesStore] hasUpdatePermissions computed",{licenseInfo:i.value,update_permissions:null==(t=i.value)?void 0:t.update_permissions,result:s}),s}),g=a(()=>f.value&&!l.value),v=a(()=>{var e;return(null==(e=s.value)?void 0:e.percentage)||0}),m=async t=>{var s,n;o.value=!0,c.value=null;try{const i=new URLSearchParams;t&&i.append("channel",t);const a=await F.routes.client.updates.getVersions(t?{channel:t}:{});if(!(null==(s=a.data)?void 0:s.versions))throw new Error((null==(n=a.data)?void 0:n.message)||"Erreur lors de la récupération des versions");e.value=a.data.versions,J.info("[UpdatesStore] Versions récupérées",{count:e.value.length,channel:t})}catch(i){c.value=i.message||"Erreur lors de la récupération des versions",J.error("[UpdatesStore] Erreur fetchVersions",{error:i.message})}finally{o.value=!1}},y=async()=>{var e,t,s,n,a,r,o;try{J.info("[UpdatesStore] Début fetchLicenseInfo");const r=await F.routes.client.updates.getLicenseInfo();J.info("[UpdatesStore] Réponse API license-info",{status:r.status,data:r.data}),(null==(e=r.data)?void 0:e.license_info)?(i.value=r.data.license_info,J.info("[UpdatesStore] Informations de licence récupérées",{license_id:null==(t=i.value)?void 0:t.license_id,update_permissions:null==(s=i.value)?void 0:s.update_permissions,channel:null==(n=i.value)?void 0:n.channel})):J.warn("[UpdatesStore] Réponse API license-info invalide",{has_license_info:!!(null==(a=r.data)?void 0:a.license_info),response_data:r.data})}catch(l){J.error("[UpdatesStore] Erreur fetchLicenseInfo",{error:l.message,status:null==(r=l.response)?void 0:r.status,response_data:null==(o=l.response)?void 0:o.data})}},b=async()=>{var e;try{const t=await F.routes.client.updates.getStats();(null==(e=t.data)?void 0:e.success)&&t.data.stats&&(r.value=t.data.stats,J.info("[UpdatesStore] Statistiques récupérées",{stats:t.data.stats}))}catch(t){J.error("[UpdatesStore] Erreur fetchStats",{error:t.message})}},w=(e,t,s)=>new Promise(n=>{const i=new XMLHttpRequest,a=Date.now();i.open("GET",e,!0),i.responseType="blob",i.onprogress=e=>{if(e.lengthComputable){const t=e.loaded,n=e.total,i=Math.round(t/n*100),r=t/((Date.now()-a)/1e3);s({loaded:t,total:n,percentage:i,speed:r,timeRemaining:(n-t)/r})}},i.onload=()=>{if(200===i.status){const e=i.response,s=window.URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download=t.endsWith(".zip")?t:`${t}.zip`,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(s),n(!0)}else n(!1)},i.onerror=()=>n(!1),i.onabort=()=>n(!1),i.send()}),k=e=>{if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,t)*100)/100+" "+["B","KB","MB","GB"][t]};return{versions:e,currentDownload:t,downloadProgress:s,licenseInfo:i,stats:r,loading:o,downloading:l,error:c,selectedChannel:u,showSecurityOnly:d,availableVersions:h,latestVersion:p,hasUpdatePermissions:f,canDownload:g,downloadProgressPercentage:v,fetchVersions:m,fetchLicenseInfo:y,fetchStats:b,downloadVersion:async t=>{var n,i,a,r,o,u;if(!g.value)return c.value="Téléchargement non autorisé",!1;l.value=!0,s.value=null,c.value=null;try{const a=await F.routes.client.updates.downloadToken({version_id:t});if(!(null==(n=a.data)?void 0:n.download))throw new Error((null==(i=a.data)?void 0:i.message)||"Impossible d'obtenir le token de téléchargement");const{token:r,url:o,filename:l}=a.data.download,c=e.value.find(e=>e.id===t);J.info("[UpdatesStore] Début du téléchargement",{version_id:t,version:null==c?void 0:c.version,filename:l,url:o});if(await w(o,l||`techcms-${(null==c?void 0:c.version)||"update"}.zip`,e=>{s.value=e}))return await b(),J.info("[UpdatesStore] Téléchargement terminé avec succès",{version_id:t,version:null==c?void 0:c.version}),!0;throw new Error("Échec du téléchargement")}catch(d){const e=(null==(r=null==(a=d.response)?void 0:a.data)?void 0:r.message)||d.message;return c.value=e||"Erreur lors du téléchargement",J.error("[UpdatesStore] Erreur downloadVersion",{version_id:t,error:d.message,api_message:e,status:null==(o=d.response)?void 0:o.status,response_data:null==(u=d.response)?void 0:u.data}),!1}finally{l.value=!1,s.value=null}},cancelDownload:()=>{l.value&&(l.value=!1,s.value=null,J.info("[UpdatesStore] Téléchargement annulé"))},checkForUpdates:async()=>{var e,t;o.value=!0,c.value=null;try{const s=await F.routes.client.updates.check();if(null==(e=s.data)?void 0:e.success)return await m(),await y(),J.info("[UpdatesStore] Vérification des mises à jour terminée"),!0;throw new Error((null==(t=s.data)?void 0:t.message)||"Erreur lors de la vérification des mises à jour")}catch(s){return c.value=s.message||"Erreur lors de la vérification des mises à jour",J.error("[UpdatesStore] Erreur checkForUpdates",{error:s.message}),!1}finally{o.value=!1}},setChannel:e=>{u.value=e,m(e)},clearError:()=>{c.value=null},formatFileSize:k,formatSpeed:e=>k(e)+"/s",formatTimeRemaining:e=>e<60?`${Math.round(e)}s`:e<3600?`${Math.round(e/60)}m`:`${Math.round(e/3600)}h`}}),zr=[{value:"Stable",label:"Stable",description:"Versions stables et testées",color:"success"},{value:"Beta",label:"Beta",description:"Versions en test avec nouvelles fonctionnalités",color:"warning"},{value:"Dev",label:"Développement",description:"Versions de développement (non recommandées en production)",color:"info"},{value:"Obsolète",label:"Obsolète",description:"Anciennes versions non maintenues",color:"danger"}],Jr={class:"updates-view"},Kr={class:"header-section"},Yr={class:"header-actions"},Qr=["disabled"],Xr={key:0,class:"license-info-card"},Zr={class:"license-header"},eo={class:"license-details"},to={class:"detail-item"},so={class:"value"},no={class:"detail-item"},io={class:"value"},ao={class:"detail-item"},ro={class:"value"},oo={key:0,class:"detail-item"},lo={class:"value"},co={key:1,class:"stats-grid"},uo={class:"stat-card"},ho={class:"stat-content"},po={class:"stat-number"},fo={class:"stat-card"},go={class:"stat-content"},vo={class:"stat-number"},mo={class:"stat-card"},yo={class:"stat-content"},bo={class:"stat-number"},wo={class:"stat-card"},ko={class:"stat-content"},_o={class:"stat-number"},Co={class:"filters-section"},Eo={class:"filters-row"},Ro={class:"filter-group"},To=["value"],So={class:"filter-group"},Oo={class:"checkbox-label"},Ao={key:2,class:"alert alert-danger"},Io={key:3,class:"alert alert-warning"},Mo={key:4,class:"download-progress-card"},Lo={key:0,class:"progress-details"},Po={class:"progress-bar"},No={class:"progress-info"},Uo={class:"versions-section"},Do={key:0,class:"loading-state"},xo={key:1,class:"empty-state"},Bo={key:2,class:"versions-grid"},qo={class:"version-header"},Vo={class:"version-info"},Ho={class:"version-number"},Go={class:"version-date"},jo={class:"version-details"},$o={class:"detail-row"},Fo={key:0,class:"detail-row"},Wo={class:"detail-row"},zo={key:0,class:"version-changelog"},Jo=["innerHTML"],Ko={class:"version-actions"},Yo=["onClick","disabled"],Qo=Nt(r({__name:"UpdatesView",setup(e){const t=Wr(),s=a(()=>t.availableVersions.length>0),n=e=>e&&"N/A"!==e?new Date(e).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"}):"N/A",i=async()=>{await t.checkForUpdates()},r=()=>{t.setChannel(t.selectedChannel)},l=()=>{t.cancelDownload()};return o(async()=>{J.info("[UpdatesView] Vue montée"),await t.fetchLicenseInfo(),await t.fetchStats(),t.licenseInfo&&(t.selectedChannel=t.licenseInfo.channel),await t.fetchVersions(t.selectedChannel)}),(e,a)=>(y(),c("div",Jr,[u("div",Kr,[a[4]||(a[4]=u("div",{class:"header-content"},[u("h1",{class:"page-title"},[u("i",{class:"fas fa-download"}),p(" Mises à jour CMS ")]),u("p",{class:"page-description"}," Téléchargez les dernières versions de TechCMS selon votre canal de mise à jour ")],-1)),u("div",Yr,[u("button",{onClick:i,disabled:h(t).loading,class:"btn btn-outline"},[u("i",{class:w(["fas fa-sync",{"fa-spin":h(t).loading}])},null,2),a[3]||(a[3]=p(" Vérifier les mises à jour "))],8,Qr)])]),h(t).licenseInfo?(y(),c("div",Xr,[u("div",Zr,[a[5]||(a[5]=u("h3",null,[u("i",{class:"fas fa-key"}),p(" Licence utilisée pour les mises à jour ")],-1)),u("span",{class:w(["permission-badge",h(t).hasUpdatePermissions?"badge-success":"badge-danger"])},[u("i",{class:w(h(t).hasUpdatePermissions?"fas fa-check":"fas fa-times")},null,2),p(" "+d(h(t).hasUpdatePermissions?"Mises à jour autorisées":"Mises à jour non autorisées"),1)],2)]),a[10]||(a[10]=u("div",{class:"auto-selection-info"},[u("i",{class:"fas fa-info-circle"}),u("span",null,"Licence sélectionnée automatiquement selon vos droits de mise à jour")],-1)),u("div",eo,[u("div",to,[a[6]||(a[6]=u("span",{class:"label"},"Licence :",-1)),u("span",so,d(h(t).licenseInfo.license_key),1)]),u("div",no,[a[7]||(a[7]=u("span",{class:"label"},"Domaine :",-1)),u("span",io,d(h(t).licenseInfo.domain),1)]),u("div",ao,[a[8]||(a[8]=u("span",{class:"label"},"Canal :",-1)),u("span",ro,[u("span",{class:w(["channel-badge",`channel-${(h(t).licenseInfo.channel||"stable").toLowerCase()}`])},d(h(t).licenseInfo.channel||"Stable"),3)])]),h(t).licenseInfo.current_version?(y(),c("div",oo,[a[9]||(a[9]=u("span",{class:"label"},"Version actuelle :",-1)),u("span",lo,d(h(t).licenseInfo.current_version),1)])):f("",!0)])])):f("",!0),h(t).stats?(y(),c("div",co,[u("div",uo,[a[12]||(a[12]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-download"})],-1)),u("div",ho,[u("div",po,d(h(t).stats.total_downloads),1),a[11]||(a[11]=u("div",{class:"stat-label"},"Téléchargements",-1))])]),u("div",fo,[a[14]||(a[14]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-code-branch"})],-1)),u("div",go,[u("div",vo,d(h(t).stats.current_version||"N/A"),1),a[13]||(a[13]=u("div",{class:"stat-label"},"Version installée",-1))])]),u("div",mo,[a[16]||(a[16]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-clock"})],-1)),u("div",yo,[u("div",bo,d(n(h(t).stats.last_download)),1),a[15]||(a[15]=u("div",{class:"stat-label"},"Dernier téléchargement",-1))])]),u("div",wo,[a[18]||(a[18]=u("div",{class:"stat-icon"},[u("i",{class:"fas fa-layer-group"})],-1)),u("div",ko,[u("div",_o,d(h(t).stats.available_versions),1),a[17]||(a[17]=u("div",{class:"stat-label"},"Versions disponibles",-1))])])])):f("",!0),u("div",Co,[u("div",Eo,[u("div",Ro,[a[19]||(a[19]=u("label",null,"Canal de mise à jour :",-1)),k(u("select",{"onUpdate:modelValue":a[0]||(a[0]=e=>h(t).selectedChannel=e),onChange:r,class:"form-select"},[(y(!0),c(g,null,v(h(zr),e=>(y(),c("option",{key:e.value,value:e.value},d(e.label)+" - "+d(e.description),9,To))),128))],544),[[_,h(t).selectedChannel]])]),u("div",So,[u("label",Oo,[k(u("input",{"onUpdate:modelValue":a[1]||(a[1]=e=>h(t).showSecurityOnly=e),type:"checkbox",class:"form-checkbox"},null,512),[[S,h(t).showSecurityOnly]]),a[20]||(a[20]=u("span",{class:"checkbox-text"},"Mises à jour de sécurité uniquement",-1))])])])]),h(t).error?(y(),c("div",Ao,[a[22]||(a[22]=u("i",{class:"fas fa-exclamation-triangle"},null,-1)),p(" "+d(h(t).error)+" ",1),u("button",{onClick:a[2]||(a[2]=e=>h(t).clearError()),class:"alert-close"},a[21]||(a[21]=[u("i",{class:"fas fa-times"},null,-1)]))])):f("",!0),h(t).hasUpdatePermissions?f("",!0):(y(),c("div",Io,a[23]||(a[23]=[u("i",{class:"fas fa-lock"},null,-1),p(" Votre licence ne permet pas le téléchargement de mises à jour. Contactez le support pour plus d'informations. ")]))),h(t).downloading?(y(),c("div",Mo,[u("div",{class:"progress-header"},[a[25]||(a[25]=u("h3",null,[u("i",{class:"fas fa-download"}),p(" Téléchargement en cours... ")],-1)),u("button",{onClick:l,class:"btn btn-sm btn-danger"},a[24]||(a[24]=[u("i",{class:"fas fa-times"},null,-1),p(" Annuler ")]))]),h(t).downloadProgress?(y(),c("div",Lo,[u("div",Po,[u("div",{class:"progress-fill",style:M({width:h(t).downloadProgressPercentage+"%"})},null,4)]),u("div",No,[u("span",null,d(h(t).downloadProgressPercentage)+"%",1),u("span",null,d(h(t).formatFileSize(h(t).downloadProgress.loaded))+" / "+d(h(t).formatFileSize(h(t).downloadProgress.total)),1),u("span",null,d(h(t).formatSpeed(h(t).downloadProgress.speed)),1),u("span",null,d(h(t).formatTimeRemaining(h(t).downloadProgress.timeRemaining))+" restant",1)])])):f("",!0)])):f("",!0),u("div",Uo,[h(t).loading&&!h(t).downloading?(y(),c("div",Do,a[26]||(a[26]=[u("i",{class:"fas fa-spinner fa-spin"},null,-1),u("span",null,"Chargement des versions...",-1)]))):s.value?(y(),c("div",Bo,[(y(!0),c(g,null,v(h(t).availableVersions,e=>{return y(),c("div",{key:e.id,class:"version-card"},[u("div",qo,[u("div",Vo,[u("h3",Ho,d(e.version),1),u("span",{class:w(["channel-badge",`channel-${e.status.toLowerCase()}`])},d(e.status),3)]),u("div",Go,d(n(e.release_date)),1)]),u("div",jo,[u("div",$o,[a[28]||(a[28]=u("i",{class:"fas fa-hdd"},null,-1)),u("span",null,"Taille : "+d(e.file_size?h(t).formatFileSize(e.file_size):"Non disponible"),1)]),e.minimum_php_version?(y(),c("div",Fo,[a[29]||(a[29]=u("i",{class:"fas fa-arrow-up"},null,-1)),u("span",null,"PHP minimum requis : "+d(e.minimum_php_version),1)])):f("",!0),u("div",Wo,[a[30]||(a[30]=u("i",{class:"fas fa-fingerprint"},null,-1)),u("span",null,"Checksum : "+d(e.file_hash?e.file_hash.substring(0,16)+"...":"Non disponible"),1)])]),e.changelog?(y(),c("div",zo,[a[31]||(a[31]=u("h4",null,"Notes de version :",-1)),u("div",{class:"changelog-content",innerHTML:(s=e.changelog,s.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"))},null,8,Jo)])):f("",!0),u("div",Ko,[u("button",{onClick:s=>(async e=>{await t.downloadVersion(e.id)&&J.info("[UpdatesView] Version téléchargée avec succès",{version:e.version,channel:e.channel})})(e),disabled:!h(t).canDownload,class:"btn btn-primary btn-block"},a[32]||(a[32]=[u("i",{class:"fas fa-download"},null,-1),p(" Télécharger ")]),8,Yo)])]);var s}),128))])):(y(),c("div",xo,a[27]||(a[27]=[u("i",{class:"fas fa-box-open"},null,-1),u("h3",null,"Aucune version disponible",-1),u("p",null,"Aucune mise à jour n'est disponible pour le canal sélectionné",-1)])))])]))}}),[["__scopeId","data-v-dc73aab0"]]),Xo=P({history:N("/client"),routes:[{path:"/",redirect:"/dashboard"},{path:"/login",name:"login",component:Bi,meta:{public:!0,requiresAuth:!1,hideLayout:!0}},{path:"/register",name:"register",component:la,meta:{public:!0,requiresAuth:!1,hideLayout:!0}},{path:"/store",redirect:()=>(window.location.href="/pricing","/pricing")},{path:"/cart",redirect:()=>(window.location.href="/store/cart","/store/cart")},{path:"/checkout",redirect:()=>(window.location.href="/store/checkout","/store/checkout")},{path:"/dashboard",name:"dashboard",component:Ut,meta:{public:!1,requiresAuth:!0}},{path:"/licenses",name:"licenses",component:Ka,meta:{public:!1,requiresAuth:!0}},{path:"/licenses/:id",name:"license-detail",component:Fr,meta:{public:!1,requiresAuth:!0}},{path:"/updates",name:"updates",component:Qo,meta:{public:!1,requiresAuth:!0}},{path:"/billing",name:"billing",component:Ns,meta:{public:!1,requiresAuth:!0}},{path:"/support",name:"support",component:kn,meta:{public:!1,requiresAuth:!0}},{path:"/account",name:"account",component:Si,meta:{public:!1,requiresAuth:!0}},{path:"/billing/invoice/:id",name:"invoice-detail",component:()=>le(()=>import("./InvoiceDetailView-C9CSwHMy.js"),__vite__mapDeps([0,1,2,3])),meta:{public:!1,requiresAuth:!0}},{path:"/support/ticket/:id",name:"ticket-detail",component:()=>le(()=>import("./TicketDetailView-QDe1ZZQR.js"),__vite__mapDeps([4,1,2,5])),meta:{public:!1,requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"not-found",redirect:"/dashboard"}]});Xo.beforeEach(async(e,t,s)=>{J.debug("[ROUTER] Navigation",{from:t.path,to:e.path,name:e.name});const n=Zo(),i=e.meta.requiresAuth,a=e.meta.hideForAuthenticated,r=!!n.initialized&&n.isAuthenticated;J.debug("[ROUTER] État d'authentification",{isAuthenticated:r,requiresAuth:i,hideForAuthenticated:a,path:e.path}),!i||r?r&&a?s({name:"dashboard"}):(J.debug("[ROUTER] Navigation autorisée vers",{path:e.path}),s()):s({name:"login",query:{redirect:e.fullPath}})});const Zo=i("clientAuth",()=>{const e=n(null),t=n(!1),s=n(null),i=n(!1),r=a(()=>!!e.value),o=a(()=>e.value?`${e.value.firstName} ${e.value.lastName}`:""),l=async()=>{var s,n,a;J.info("[CLIENT-AUTH] checkAuth() appelée",{initialized:i.value,isAuthenticated:r.value}),t.value=!0;try{J.info("[CLIENT-AUTH] Appel API /me");const t=await F.routes.auth.me();if(J.info("[CLIENT-AUTH] Réponse API /me",{response:t}),null==(s=t.data)?void 0:s.user){if(e.value=t.data.user,null==(n=t.data)?void 0:n.token)F.setAuthToken(t.data.token);else{const e=(()=>{const e=localStorage.getItem("techcms-client-token");if(e)return e;const t=document.cookie.split(";");for(const s of t){const[e,t]=s.trim().split("=");if("techcms_client_token"===e||"auth_token"===e)return t}return null})();e&&F.setAuthToken(e)}return J.info("[CLIENT-AUTH] Client authentifié via cookie",{user:e.value}),!0}return J.info("[CLIENT-AUTH] Client non authentifié"),!1}catch(o){return 401===(null==(a=null==o?void 0:o.response)?void 0:a.status)?J.info("[CLIENT-AUTH] Non authentifié (401) - normal sur pages publiques"):J.error("[CLIENT-AUTH] Erreur lors de la vérification de l'authentification",{error:o}),e.value=null,F.setAuthToken(null),!1}finally{t.value=!1,i.value=!0}};return{user:e,loading:t,error:s,initialized:i,isAuthenticated:r,userFullName:o,login:async(n,i,a=!1)=>{var r,o,l,c;t.value=!0,s.value=null;try{const t=await F.routes.auth.login(n,i,a);if(null==(r=t.data)?void 0:r.user)return e.value=t.data.user,(null==(o=t.data)?void 0:o.token)&&(F.setAuthToken(t.data.token),localStorage.setItem("techcms-client-token",t.data.token)),!0;throw new Error("Réponse invalide du serveur")}catch(u){throw s.value=(null==(c=null==(l=u.response)?void 0:l.data)?void 0:c.message)||u.message||"Erreur de connexion",u}finally{t.value=!1}},register:async e=>{var n,i;t.value=!0,s.value=null;try{return(await F.routes.auth.register({firstname:e.firstname,lastname:e.lastname,email:e.email,company:e.company,phone:e.phone,address:e.address,postal_code:e.postal_code,city:e.city,country:e.country,password:e.password})).data}catch(a){throw s.value=(null==(i=null==(n=a.response)?void 0:n.data)?void 0:i.message)||a.message||"Erreur d'inscription",a}finally{t.value=!1}},logout:async()=>{t.value=!0;try{await F.routes.auth.logout()}catch(n){J.error("[CLIENT-AUTH] Erreur lors de la déconnexion",{error:n})}finally{e.value=null,s.value=null,t.value=!1,F.setAuthToken(null),localStorage.removeItem("techcms-client-token"),J.info("[CLIENT-AUTH] Déconnexion client effectuée"),Xo.push("/login")}},checkAuth:l,initialize:async()=>{J.info("[CLIENT-AUTH] initialize() appelée",{initialized:i.value}),J.info("[CLIENT-AUTH] Force checkAuth() pour vérifier les cookies"),await l()},clearError:()=>{s.value=null},updateUser:t=>{e.value&&(e.value={...e.value,...t})},syncCartOnLogin:()=>{}}},{persist:{key:"techcms-client-auth",storage:localStorage}}),el={class:"notification-center"},tl={key:0,class:"notification-dropdown"},sl={class:"notification-header"},nl={class:"notification-actions"},il=["title"],al=["title"],rl={class:"notification-list"},ol={key:0,class:"no-notifications"},ll=["onClick"],cl={class:"notification-icon"},ul={class:"notification-content"},dl={class:"notification-title"},hl={class:"notification-message"},pl={class:"notification-time"},fl={class:"notification-actions-item"},gl=["onClick","title"],vl=["onClick","title"],ml={key:0,class:"notification-footer"},yl=Nt(r({__name:"NotificationCenter",props:{notifications:{default:()=>[]},maxVisible:{default:10}},emits:["notificationClick","markAsRead","markAllAsRead","clearAll","remove"],setup(e,{emit:t}){const s=e,i=t,r=m(),h=Ee(),p=n(!1),k=n(!1),_=a(()=>s.notifications.slice(0,s.maxVisible)),C=a(()=>s.notifications.filter(e=>!e.read).length),E=()=>{p.value=!p.value,p.value&&(k.value=!1)},R=()=>{p.value=!1},S=e=>{h.markNotificationAsRead(e),i("markAsRead",e)},A=()=>{J.info("[NOTIFICATION CENTER] Marquer toutes comme lues"),h.markAllNotificationsAsRead(),i("markAllAsRead")},I=()=>{J.info("[NOTIFICATION CENTER] Effacer toutes les notifications"),h.clearNotifications(),i("clearAll")},M=()=>{J.info("[NOTIFICATION CENTER] Voir toutes les notifications"),r.push("/notifications"),R()},L=e=>{const t=new Date(e),s=(new Date).getTime()-t.getTime(),n=Math.floor(s/6e4),i=Math.floor(n/60),a=Math.floor(i/24);return n<1?"À l'instant":n<60?`Il y a ${n}min`:i<24?`Il y a ${i}h`:a<7?`Il y a ${a}j`:new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}).format(t)};U(()=>s.notifications.length,(e,t)=>{e>t&&!p.value&&(k.value=!0,setTimeout(()=>{k.value=!1},3e3))});const P=e=>{e.target.closest(".notification-center")||R()};return o(()=>{document.addEventListener("click",P)}),l(()=>{document.removeEventListener("click",P)}),(e,t)=>(y(),c("div",el,[u("div",{class:w(["notification-trigger",{active:p.value}]),onClick:E},[t[0]||(t[0]=u("i",{class:"fas fa-bell"},null,-1)),C.value>0?(y(),c("span",{key:0,class:w(["notification-badge",{pulse:k.value}])},d(C.value>99?"99+":C.value),3)):f("",!0)],2),T(D,{name:"notification-dropdown"},{default:O(()=>[p.value?(y(),c("div",tl,[u("div",sl,[u("h4",null,d(e.$t("notifications.title")),1),u("div",nl,[C.value>0?(y(),c("button",{key:0,onClick:A,class:"btn-mark-all-read",title:e.$t("notifications.mark_all_read")},t[1]||(t[1]=[u("i",{class:"fas fa-check-double"},null,-1)]),8,il)):f("",!0),u("button",{onClick:I,class:"btn-clear-all",title:e.$t("notifications.clear_all")},t[2]||(t[2]=[u("i",{class:"fas fa-trash"},null,-1)]),8,al)])]),u("div",rl,[0===_.value.length?(y(),c("div",ol,[t[3]||(t[3]=u("i",{class:"fas fa-bell-slash"},null,-1)),u("p",null,d(e.$t("notifications.no_notifications")),1)])):f("",!0),(y(!0),c(g,null,v(_.value,s=>{return y(),c("div",{key:s.id,class:w(["notification-item",{unread:!s.read,clickable:!!s.action_url}]),onClick:e=>(e=>{J.info("[NOTIFICATION CENTER] Clic sur notification",{notification:e}),e.read||S(e.id),e.action_url&&(r.push(e.action_url),R()),i("notificationClick",e)})(s)},[u("div",cl,[u("i",{class:w((n=s.type,{service:"fas fa-server text-blue",invoice:"fas fa-file-invoice text-orange",ticket:"fas fa-headset text-green",system:"fas fa-cog text-gray"}[n]||"fas fa-info-circle text-blue"))},null,2)]),u("div",ul,[u("div",dl,d(s.title),1),u("div",hl,d(s.message),1),u("div",pl,d(L(s.created_at)),1)]),u("div",fl,[s.read?f("",!0):(y(),c("button",{key:0,onClick:b(e=>S(s.id),["stop"]),class:"btn-mark-read",title:e.$t("notifications.mark_read")},t[4]||(t[4]=[u("i",{class:"fas fa-check"},null,-1)]),8,gl)),u("button",{onClick:b(e=>{return t=s.id,void i("remove",t);var t},["stop"]),class:"btn-remove",title:e.$t("notifications.remove")},t[5]||(t[5]=[u("i",{class:"fas fa-times"},null,-1)]),8,vl)])],10,ll);var n}),128))]),_.value.length>0?(y(),c("div",ml,[u("button",{onClick:M,class:"btn-view-all"},d(e.$t("notifications.view_all")),1)])):f("",!0)])):f("",!0)]),_:1}),p.value?(y(),c("div",{key:0,class:"notification-overlay",onClick:R})):f("",!0)]))}}),[["__scopeId","data-v-8937cb6a"]]),bl={class:"client-header"},wl={class:"header-left"},kl={class:"page-title"},_l={class:"header-right"},Cl={class:"search-bar"},El={class:"current-lang"},Rl={class:"menu-items"},Tl={key:0,class:"fas fa-check"},Sl={key:0,class:"fas fa-check"},Ol={class:"user-name"},Al={key:0,class:"user-info"},Il={class:"user-details"},Ml={class:"name"},Ll={class:"email"},Pl={key:1,class:"user-info"},Nl={key:2,class:"user-info"},Ul={class:"menu-items"},Dl=Nt(r({__name:"AppHeader",setup(e){const t=L(),s=m(),{locale:i}=I(),r=Zo(),p=Ee(),g=n(""),v=n(i.value||"fr"),_=n(!1),R=n(!1),S=n(),M=n(),P=a(()=>p.notifications),N=a(()=>({dashboard:"Tableau de bord",services:"Mes Services",invoices:"Factures",tickets:"Support",account:"Mon Compte"}[t.name]||"TechCMS Client")),U=()=>{},D=()=>{_.value=!_.value,R.value=!1},x=e=>{v.value=e,i.value=e,_.value=!1,localStorage.setItem("preferred-language",e)},B=()=>{R.value=!R.value,_.value=!1},q=e=>{J.info("[HEADER] Clic sur notification",{notification:e}),e.action_url&&s.push(e.action_url)},V=e=>{p.markNotificationAsRead(e)},H=()=>{J.info("[HEADER] Marquer toutes les notifications comme lues"),p.markAllNotificationsAsRead()},G=()=>{J.info("[HEADER] Effacer toutes les notifications"),p.clearNotifications()},j=async()=>{try{await r.logout()}catch(e){}},$=e=>{S.value&&!S.value.contains(e.target)&&(_.value=!1),M.value&&!M.value.contains(e.target)&&(R.value=!1)};return o(async()=>{document.addEventListener("click",$);const e=localStorage.getItem("preferred-language");!e||"fr"!==e&&"en"!==e||(v.value=e,i.value=e),r.initialized||await r.initialize()}),l(()=>{document.removeEventListener("click",$)}),(e,t)=>{var s;const n=A("router-link");return y(),c("div",bl,[u("div",wl,[u("h1",kl,d(N.value),1)]),u("div",_l,[u("div",Cl,[t[3]||(t[3]=u("i",{class:"fas fa-search"},null,-1)),k(u("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>g.value=e),type:"text",placeholder:"Rechercher...",onInput:U},null,544),[[C,g.value]])]),u("div",{ref_key:"languageDropdown",ref:S,class:"dropdown"},[u("button",{class:"btn-icon dropdown-toggle",onClick:D},[u("span",El,d(v.value.toUpperCase()),1)]),u("div",{class:w(["dropdown-menu",{show:_.value}])},[t[8]||(t[8]=u("div",{class:"menu-header"},[u("h3",null,"Langue")],-1)),u("div",Rl,[u("a",{href:"#",class:w(["menu-item",{active:"fr"===v.value}]),onClick:t[1]||(t[1]=b(e=>x("fr"),["prevent"]))},[t[4]||(t[4]=u("span",{class:"flag"},"🇫🇷",-1)),t[5]||(t[5]=u("span",{class:"label"},"Français",-1)),"fr"===v.value?(y(),c("i",Tl)):f("",!0)],2),u("a",{href:"#",class:w(["menu-item",{active:"en"===v.value}]),onClick:t[2]||(t[2]=b(e=>x("en"),["prevent"]))},[t[6]||(t[6]=u("span",{class:"flag"},"🇬🇧",-1)),t[7]||(t[7]=u("span",{class:"label"},"English",-1)),"en"===v.value?(y(),c("i",Sl)):f("",!0)],2)])],2)],512),T(yl,{notifications:P.value,onNotificationClick:q,onMarkAsRead:V,onMarkAllAsRead:H,onClearAll:G},null,8,["notifications"]),u("div",{ref_key:"userDropdown",ref:M,class:"user-menu"},[u("button",{class:"user-btn",onClick:B},[t[9]||(t[9]=u("div",{class:"user-avatar"},[u("i",{class:"fas fa-user-circle"})],-1)),u("span",Ol,d(h(r).isAuthenticated?h(r).userFullName:h(r).loading?"Chargement...":"Non connecté"),1),t[10]||(t[10]=u("i",{class:"fas fa-chevron-down"},null,-1))]),u("div",{class:w(["user-dropdown",{show:R.value}])},[h(r).isAuthenticated?(y(),c("div",Al,[t[11]||(t[11]=u("div",{class:"user-avatar-large"},[u("i",{class:"fas fa-user-circle"})],-1)),u("div",Il,[u("div",Ml,d(h(r).userFullName),1),u("div",Ll,d(null==(s=h(r).user)?void 0:s.email),1)])])):h(r).loading?(y(),c("div",Pl,t[12]||(t[12]=[E('<div class="user-avatar-large" data-v-1f901392><i class="fas fa-user-circle" data-v-1f901392></i></div><div class="user-details" data-v-1f901392><div class="name" data-v-1f901392>Chargement...</div><div class="email" data-v-1f901392>---</div></div>',2)]))):(y(),c("div",Nl,t[13]||(t[13]=[E('<div class="user-avatar-large" data-v-1f901392><i class="fas fa-user-circle" data-v-1f901392></i></div><div class="user-details" data-v-1f901392><div class="name" data-v-1f901392>Non connecté</div><div class="email" data-v-1f901392>---</div></div>',2)]))),t[17]||(t[17]=u("div",{class:"menu-divider"},null,-1)),u("div",Ul,[T(n,{to:"/account",class:"menu-item"},{default:O(()=>t[14]||(t[14]=[u("i",{class:"fas fa-user-cog"},null,-1),u("span",null,"Mon Compte",-1)])),_:1,__:[14]}),T(n,{to:"/settings",class:"menu-item"},{default:O(()=>t[15]||(t[15]=[u("i",{class:"fas fa-cog"},null,-1),u("span",null,"Paramètres",-1)])),_:1,__:[15]}),u("a",{href:"#",class:w(["menu-item",{disabled:h(r).loading}]),onClick:b(j,["prevent"])},t[16]||(t[16]=[u("i",{class:"fas fa-sign-out-alt"},null,-1),u("span",null,"Déconnexion",-1)]),2)])],2)],512)])])}}}),[["__scopeId","data-v-1f901392"]]),xl={class:"sidebar-header"},Bl={key:0,class:"logo-text"},ql={class:"sidebar-nav"},Vl={key:0},Hl={key:0},Gl={href:"/pricing",class:"nav-link"},jl={key:0},$l={key:0},Fl={key:0},Wl={key:0},zl={key:0},Jl={key:0,class:"sidebar-footer"},Kl={class:"user-info"},Yl={key:0,class:"user-details"},Ql={class:"user-name"},Xl={class:"user-email"},Zl={key:1,class:"user-details"},ec={class:"user-name"},tc={key:2,class:"user-details"},sc={class:"user-name"},nc=["disabled"],ic=Nt(r({__name:"AppSidebar",props:{collapsed:{type:Boolean}},emits:["sidebar-toggle"],setup(e,{emit:t}){const s=t,n=Zo(),i=()=>{s("sidebar-toggle")},a=async()=>{try{await n.logout()}catch(e){}};return o(async()=>{n.initialized||await n.initialize()}),(e,t)=>{var s;const r=A("router-link");return y(),c("aside",{class:w(["client-sidebar",{"sidebar-collapsed":e.collapsed}])},[u("div",xl,[T(r,{to:"/dashboard",class:"logo"},{default:O(()=>[t[0]||(t[0]=u("img",{src:"data:image/png;base64,Cg==",alt:"TechCMS",class:"logo-img"},null,-1)),e.collapsed?f("",!0):(y(),c("span",Bl,"TechCMS"))]),_:1,__:[0]}),u("button",{class:"sidebar-toggle",onClick:i},t[1]||(t[1]=[u("i",{class:"fas fa-bars"},null,-1)]))]),u("nav",ql,[u("ul",null,[u("li",{class:w({active:"dashboard"===e.$route.name})},[T(r,{to:"/dashboard",class:"nav-link"},{default:O(()=>[t[2]||(t[2]=u("i",{class:"fas fa-home"},null,-1)),e.collapsed?f("",!0):(y(),c("span",Vl,d(e.$t("dashboard.title")),1))]),_:1,__:[2]})],2),u("li",{class:w({active:"licenses"===e.$route.name||"license-detail"===e.$route.name})},[T(r,{to:"/licenses",class:"nav-link"},{default:O(()=>[t[3]||(t[3]=u("i",{class:"fas fa-key"},null,-1)),e.collapsed?f("",!0):(y(),c("span",Hl,d(e.$t("licenses.title")),1))]),_:1,__:[3]})],2),u("li",null,[u("a",Gl,[t[4]||(t[4]=u("i",{class:"fas fa-shopping-bag"},null,-1)),e.collapsed?f("",!0):(y(),c("span",jl,"Boutique"))])]),u("li",{class:w({active:"updates"===e.$route.name})},[T(r,{to:"/updates",class:"nav-link"},{default:O(()=>[t[5]||(t[5]=u("i",{class:"fas fa-download"},null,-1)),e.collapsed?f("",!0):(y(),c("span",$l,"Mises à jour"))]),_:1,__:[5]})],2),u("li",{class:w({active:"billing"===e.$route.name})},[T(r,{to:"/billing",class:"nav-link"},{default:O(()=>[t[6]||(t[6]=u("i",{class:"fas fa-file-invoice"},null,-1)),e.collapsed?f("",!0):(y(),c("span",Fl,d(e.$t("billing.title")),1))]),_:1,__:[6]})],2),u("li",{class:w({active:"support"===e.$route.name})},[T(r,{to:"/support",class:"nav-link"},{default:O(()=>[t[7]||(t[7]=u("i",{class:"fas fa-headset"},null,-1)),e.collapsed?f("",!0):(y(),c("span",Wl,d(e.$t("support.title")),1))]),_:1,__:[7]})],2),u("li",{class:w({active:"account"===e.$route.name})},[T(r,{to:"/account",class:"nav-link"},{default:O(()=>[t[8]||(t[8]=u("i",{class:"fas fa-user-cog"},null,-1)),e.collapsed?f("",!0):(y(),c("span",zl,d(e.$t("account.title")),1))]),_:1,__:[8]})],2)])]),e.collapsed?f("",!0):(y(),c("div",Jl,[u("div",Kl,[t[11]||(t[11]=u("div",{class:"user-avatar"},[u("i",{class:"fas fa-user-circle"})],-1)),h(n).isAuthenticated?(y(),c("div",Yl,[u("div",Ql,d(h(n).userFullName),1),u("div",Xl,d(null==(s=h(n).user)?void 0:s.email),1)])):h(n).loading?(y(),c("div",Zl,[u("div",ec,d(e.$t("common.loading")),1),t[9]||(t[9]=u("div",{class:"user-email"},"---",-1))])):(y(),c("div",tc,[u("div",sc,d(e.$t("auth.not_connected")),1),t[10]||(t[10]=u("div",{class:"user-email"},"---",-1))]))]),u("button",{class:"logout-btn",onClick:a,disabled:h(n).loading},[t[12]||(t[12]=u("i",{class:"fas fa-sign-out-alt"},null,-1)),u("span",null,d(e.$t("auth.logout")),1)],8,nc)]))],2)}}}),[["__scopeId","data-v-f6c284f0"]]),ac={class:"client-app-layout"},rc={class:"app-container"},oc=Nt(r({__name:"AppLayout",setup(e){const t=n(!1),s=()=>{t.value=!t.value};return(e,n)=>(y(),c("div",ac,[u("header",null,[T(Dl)]),u("div",rc,[T(ic,{collapsed:t.value,onSidebarToggle:s},null,8,["collapsed"]),u("main",{class:w(["app-content",{"sidebar-collapsed":t.value}])},[x(e.$slots,"default",{},void 0)],2)])]))}}),[["__scopeId","data-v-aa1a35f8"]]),lc={id:"app",class:"tech-cms-client-app"},cc={key:0,class:"auth-container"},uc={key:0,class:"realtime-error-banner"},dc=Nt(r({__name:"App",setup(e){const t=Zo(),s=Ee(),n=a(()=>s.hasError&&t.isAuthenticated);o(async()=>{J.info("TechCMS Client - Initialisation..."),await t.initialize(),t.isAuthenticated&&(J.info("TechCMS Client - Initialisation du temps réel..."),i())}),U(()=>t.isAuthenticated,e=>{e?(J.info("TechCMS Client - Utilisateur authentifié, initialisation du temps réel..."),i()):(J.info("TechCMS Client - Utilisateur déconnecté, déconnexion du temps réel..."),s.disconnect())});const i=async()=>{try{await s.init()}catch(e){J.error("TechCMS Client - Erreur d'initialisation du temps réel:",{error:e})}},r=async()=>{J.info("TechCMS Client - Tentative de reconnexion au service temps réel..."),await s.retry()};return(e,t)=>{const s=A("router-view");return y(),c("div",lc,[T(s,null,{default:O(({Component:e,route:t})=>[T(D,{name:"fade",mode:"out-in"},{default:O(()=>[t.meta.hideLayout?(y(),c("div",cc,[(y(),B(q(e)))])):(y(),B(oc,{key:1},{default:O(()=>[(y(),B(q(e)))]),_:2},1024))]),_:2},1024)]),_:1}),n.value?(y(),c("div",uc,[u("div",{class:"realtime-error-content"},[t[0]||(t[0]=u("span",null,"Connexion temps réel interrompue",-1)),u("button",{class:"retry-button",onClick:r}," Réessayer ")])])):f("",!0)])}}}),[["__scopeId","data-v-270f4b36"]]),hc={common:{loading:"Chargement...",error:"Une erreur est survenue",save:"Enregistrer",cancel:"Annuler",edit:"Modifier",delete:"Supprimer",view:"Voir",export:"Exporter",search:"Rechercher...",filter:"Filtrer",all:"Toutes",active:"Actif",inactive:"Inactif",status:"Statut",date:"Date",amount:"Montant",back:"Retour",refresh:"Actualiser",download:"Télécharger",close:"Fermer",open:"Ouvrir",retry:"Réessayer",send:"Envoyer",reply:"Répondre",month:"mois",year:"année",day:"jour",hour:"heure",minute:"minute",second:"seconde"},dashboard:{title:"Tableau de bord",client_interface:"Interface Client",my_licenses:"Mes Licences",unpaid_invoices:"Factures Impayées",open_tickets:"Tickets Ouverts",total_due:"Montant Dû",recent_services:"Services Récents",recent_invoices:"Factures Récentes",recent_tickets:"Tickets Récents",view_all:"Voir tout",no_data:"Aucune donnée disponible"},notifications:{title:"Notifications",no_notifications:"Aucune notification",mark_read:"Marquer comme lu",mark_all_read:"Tout marquer comme lu",clear_all:"Tout effacer",remove:"Supprimer",view_all:"Voir toutes les notifications",new_service:"Nouveau service activé",service_suspended:"Service suspendu",service_renewed:"Service renouvelé",new_invoice:"Nouvelle facture générée",invoice_paid:"Facture payée",invoice_overdue:"Facture en retard",new_ticket_reply:"Nouvelle réponse à votre ticket",ticket_status_changed:"Statut du ticket modifié",system_maintenance:"Maintenance système programmée"},services:{title:"Mes Services",new_service:"Nouveau Service",manage:"Gérer",filter_by_status:"Filtrer par statut",no_services:"Aucun service trouvé",service_name:"Nom du service",service_type:"Type de service",created_date:"Date de création",last_update:"Dernière mise à jour",monthly_price:"Prix mensuel",next_billing:"Prochaine facturation",billing_cycle:"Cycle de facturation",configuration:"Configuration",hostname:"Nom d'hôte",username:"Nom d'utilisateur",port:"Port",database:"Base de données",general_info:"Informations générales",recent_history:"Historique récent",no_history:"Aucun historique disponible",back_to_services:"Retour aux services",manage_service:"Gérer le service",all_statuses:"Tous les statuts",all_types:"Tous les types",type:"Type",web_hosting:"Hébergement Web",vps:"Serveur Virtuel",domain:"Nom de Domaine",email:"Email",ssl:"Certificat SSL",search_placeholder:"Rechercher un service...",loading_services:"Chargement de vos services...",no_results:"Aucun service ne correspond à vos critères de recherche.",no_active_services:"Vous n'avez pas encore de services actifs.",order_service:"Commander un service",expires_on:"Expire le",statistics:"Statistiques",renew:"Renouveler"},billing:{title:"Facturation",pay_unpaid:"Payer les impayées",unpaid:"Impayées",paid:"Payées",cancelled:"Annulées",overdue:"En retard",download:"Télécharger",pay:"Payer",pay_now:"Payer maintenant",invoice_number:"Numéro de facture",issue_date:"Date d'émission",due_date:"Date d'échéance",payment_date:"Date de paiement",total_amount:"Montant total",subtotal:"Sous-total",tax:"TVA",invoice_summary:"Résumé de la facture",service_details:"Détail des services",payment_history:"Historique des paiements",description:"Description",period:"Période",quantity:"Quantité",unit_price:"Prix unitaire",total:"Total",download_pdf:"Télécharger PDF",back_to_billing:"Retour à la facturation",payment_method:"Méthode de paiement",payment_status:"Statut du paiement",no_invoices:"Aucune facture récente",total_invoices:"Total Factures",unpaid_invoices:"Factures Impayées",amount_due:"Montant Dû",total_paid:"Total Payé",all_statuses:"Tous les statuts",draft:"Brouillon",all_periods:"Toutes les périodes",current_month:"Ce mois",last_month:"Mois dernier",current_year:"Cette année",last_year:"Année dernière",search_placeholder:"Numéro de facture..."},support:{title:"Support",new_ticket:"Nouveau Ticket",my_tickets:"Mes Tickets",priority:"Priorité",open:"Ouvert",closed:"Fermé",pending:"En attente",reply:"Répondre",subject:"Sujet",message:"Message",department:"Département",assigned_to:"Assigné à",ticket_info:"Informations du ticket",conversation:"Conversation",add_reply:"Ajouter une réponse",your_message:"Votre message",attachments:"Pièces jointes",attachments_optional:"Pièces jointes (optionnel)",file_formats:"Formats acceptés: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (max 10MB par fichier)",selected_files:"Fichiers sélectionnés:",send_reply:"Envoyer la réponse",sending:"Envoi...",close_ticket:"Fermer le ticket",reopen_ticket:"Rouvrir le ticket",ticket_closed:"Ticket fermé",ticket_closed_message:"Ce ticket a été fermé. Pour ajouter une réponse, vous devez d'abord le rouvrir.",back_to_support:"Retour au support",you:"Vous",support_team:"Support",priority_low:"Faible",priority_medium:"Moyenne",priority_high:"Élevée",priority_urgent:"Urgente",no_tickets:"Aucun ticket de support",create_ticket:"Créer un Ticket",create_ticket_desc:"Signaler un problème ou demander de l'aide",knowledge_base:"Base de Connaissances",knowledge_base_desc:"Consultez nos guides et tutoriels",live_chat:"Chat en Direct",live_chat_desc:"Discutez avec notre équipe support",phone_support:"Support Téléphonique",phone_support_desc:"Appelez-nous au +33 1 23 45 67 89"},account:{title:"Mon Compte",personal_info:"Informations personnelles",security:"Sécurité",change_password:"Changer le mot de passe",current_password:"Mot de passe actuel",new_password:"Nouveau mot de passe",confirm_password:"Confirmer le mot de passe",first_name:"Prénom",last_name:"Nom",email:"Email",company:"Entreprise",phone:"Téléphone",address:"Adresse",city:"Ville",postal_code:"Code postal",country:"Pays",last_login:"Dernière connexion",member_since:"Membre depuis",password_strength:"Force du mot de passe",last_password_change:"Dernière modification",two_factor_auth:"Authentification à deux facteurs",login_history:"Historique de connexion",edit_profile:"Modifier le profil",save_changes:"Enregistrer les modifications"},auth:{login:"Connexion",register:"Inscription",logout:"Déconnexion",email:"Email",password:"Mot de passe",remember_me:"Se souvenir de moi",forgot_password:"Mot de passe oublié ?",no_account:"Pas de compte ?",already_account:"Déjà un compte ?",sign_in:"Se connecter",sign_up:"S'inscrire",welcome_back:"Bon retour !",create_account:"Créer un compte",not_connected:"Non connecté"},status:{active:"Actif",inactive:"Inactif",suspended:"Suspendu",terminated:"Terminé",pending:"En attente",paid:"Payée",unpaid:"Impayée",cancelled:"Annulée",overdue:"En retard",open:"Ouvert",closed:"Fermé",completed:"Terminé",failed:"Échoué",expired:"Expiré"},licenses:{title:"Mes Licences",my_licenses:"Mes Licences",license_details:"Détails de la Licence",license_information:"Informations de la Licence",license_key:"Clé de Licence",domain_limit:"Limite de Domaines",installation_limit:"Limite d'Installations",installations_used:"Installations utilisées",expires_at:"Expire le",created_at:"Créée le",view_details:"Voir les détails",verify_license:"Vérifier la Licence",verifying:"Vérification...",refresh:"Actualiser",restrictions:"Restrictions",allowed_domains:"Domaines Autorisés",allowed_ips:"IPs Autorisées",no_domain_restrictions:"Aucune restriction de domaine",no_ip_restrictions:"Aucune restriction d'IP",current_usage:"Utilisation Actuelle",actions:"Actions",stats:{total:"Total",active:"Actives",expired:"Expirées",expiring_soon:"Expirent Bientôt"},status:{label:"Statut",active:"Active",inactive:"Inactive",expired:"Expirée"},no_licenses:{title:"Aucune licence",description:"Vous n'avez actuellement aucune licence associée à votre compte."}}},pc={common:{loading:"Loading...",error:"An error occurred",save:"Save",cancel:"Cancel",edit:"Edit",delete:"Delete",view:"View",export:"Export",search:"Search...",filter:"Filter",all:"All",active:"Active",inactive:"Inactive",status:"Status",date:"Date",amount:"Amount",back:"Back",refresh:"Refresh",download:"Download",close:"Close",open:"Open",retry:"Retry",send:"Send",reply:"Reply",month:"month",year:"year",day:"day",hour:"hour",minute:"minute",second:"second"},dashboard:{title:"Dashboard",client_interface:"Client Interface",my_licenses:"My Licenses",unpaid_invoices:"Unpaid Invoices",open_tickets:"Open Tickets",total_due:"Total Due",recent_services:"Recent Services",recent_invoices:"Recent Invoices",recent_tickets:"Recent Tickets",view_all:"View All",no_data:"No data available"},services:{title:"My Services",new_service:"New Service",manage:"Manage",filter_by_status:"Filter by status",no_services:"No services found",service_name:"Service name",service_type:"Service type",created_date:"Created date",last_update:"Last update",monthly_price:"Monthly price",next_billing:"Next billing",billing_cycle:"Billing cycle",configuration:"Configuration",hostname:"Hostname",username:"Username",port:"Port",database:"Database",general_info:"General information",recent_history:"Recent history",no_history:"No history available",back_to_services:"Back to services",manage_service:"Manage service",all_statuses:"All statuses",all_types:"All types",type:"Type",web_hosting:"Web Hosting",vps:"Virtual Server",domain:"Domain Name",email:"Email",ssl:"SSL Certificate",search_placeholder:"Search for a service...",loading_services:"Loading your services...",no_results:"No services match your search criteria.",no_active_services:"You don't have any active services yet.",order_service:"Order a service",expires_on:"Expires on",statistics:"Statistics",renew:"Renew"},billing:{title:"Billing",pay_unpaid:"Pay Unpaid",unpaid:"Unpaid",paid:"Paid",cancelled:"Cancelled",overdue:"Overdue",download:"Download",pay:"Pay",pay_now:"Pay now",invoice_number:"Invoice number",issue_date:"Issue date",due_date:"Due date",payment_date:"Payment date",total_amount:"Total amount",subtotal:"Subtotal",tax:"Tax",invoice_summary:"Invoice summary",service_details:"Service details",payment_history:"Payment history",description:"Description",period:"Period",quantity:"Quantity",unit_price:"Unit price",total:"Total",download_pdf:"Download PDF",back_to_billing:"Back to billing",payment_method:"Payment method",payment_status:"Payment status",no_invoices:"No recent invoices",total_invoices:"Total Invoices",unpaid_invoices:"Unpaid Invoices",amount_due:"Amount Due",total_paid:"Total Paid",all_statuses:"All statuses",draft:"Draft",all_periods:"All periods",current_month:"This month",last_month:"Last month",current_year:"This year",last_year:"Last year",search_placeholder:"Invoice number..."},support:{title:"Support",new_ticket:"New Ticket",my_tickets:"My Tickets",priority:"Priority",open:"Open",closed:"Closed",pending:"Pending",reply:"Reply",subject:"Subject",message:"Message",department:"Department",assigned_to:"Assigned to",ticket_info:"Ticket information",conversation:"Conversation",add_reply:"Add reply",your_message:"Your message",attachments:"Attachments",attachments_optional:"Attachments (optional)",file_formats:"Accepted formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (max 10MB per file)",selected_files:"Selected files:",send_reply:"Send reply",sending:"Sending...",close_ticket:"Close ticket",reopen_ticket:"Reopen ticket",ticket_closed:"Ticket closed",ticket_closed_message:"This ticket has been closed. To add a reply, you must first reopen it.",back_to_support:"Back to support",you:"You",support_team:"Support",priority_low:"Low",priority_medium:"Medium",priority_high:"High",priority_urgent:"Urgent",no_tickets:"No support tickets",create_ticket:"Create Ticket",create_ticket_desc:"Report an issue or request help",knowledge_base:"Knowledge Base",knowledge_base_desc:"Browse our guides and tutorials",live_chat:"Live Chat",live_chat_desc:"Chat with our support team",phone_support:"Phone Support",phone_support_desc:"Call us at +33 1 23 45 67 89"},account:{title:"My Account",personal_info:"Personal information",security:"Security",change_password:"Change password",current_password:"Current password",new_password:"New password",confirm_password:"Confirm password",first_name:"First name",last_name:"Last name",email:"Email",company:"Company",phone:"Phone",address:"Address",city:"City",postal_code:"Postal code",country:"Country",last_login:"Last login",member_since:"Member since",password_strength:"Password strength",last_password_change:"Last password change",two_factor_auth:"Two-factor authentication",login_history:"Login history",edit_profile:"Edit profile",save_changes:"Save changes"},auth:{login:"Login",register:"Register",logout:"Logout",email:"Email",password:"Password",remember_me:"Remember me",forgot_password:"Forgot password?",no_account:"No account?",already_account:"Already have an account?",sign_in:"Sign in",sign_up:"Sign up",welcome_back:"Welcome back!",create_account:"Create account",not_connected:"Not connected"},status:{active:"Active",inactive:"Inactive",suspended:"Suspended",terminated:"Terminated",pending:"Pending",paid:"Paid",unpaid:"Unpaid",cancelled:"Cancelled",overdue:"Overdue",open:"Open",closed:"Closed",completed:"Completed",failed:"Failed",expired:"Expired"},licenses:{title:"My Licenses",my_licenses:"My Licenses",license_details:"License Details",license_information:"License Information",license_key:"License Key",domain_limit:"Domain Limit",installation_limit:"Installation Limit",installations_used:"Installations used",expires_at:"Expires on",created_at:"Created on",view_details:"View details",verify_license:"Verify License",verifying:"Verifying...",refresh:"Refresh",restrictions:"Restrictions",allowed_domains:"Allowed Domains",allowed_ips:"Allowed IPs",no_domain_restrictions:"No domain restrictions",no_ip_restrictions:"No IP restrictions",current_usage:"Current Usage",actions:"Actions",stats:{total:"Total",active:"Active",expired:"Expired",expiring_soon:"Expiring Soon"},status:{label:"Status",active:"Active",inactive:"Inactive",expired:"Expired"},no_licenses:{title:"No licenses",description:"You currently have no licenses associated with your account."}}},fc=V({legacy:!1,locale:localStorage.getItem("language")||"fr",fallbackLocale:"en",messages:{fr:hc,en:pc}});J.info("Démarrage de l'application"),J.info("Création de l'application Vue");const gc=H(dc);gc.config.errorHandler=(e,t,s)=>{const n=t?t.$.type.name:"UnknownComponent";J.error(`Erreur non capturée dans le composant: ${n}`,{error:e.toString(),stack:e.stack,info:s})},J.info("Initialisation de Pinia");const vc=G();vc.use(re),gc.use(vc),J.info("Initialisation du Router"),gc.use(Xo),J.info("Initialisation de i18n"),gc.use(fc),J.info("FormKit a été retiré car nous utilisons maintenant des composants standard"),J.info("[CLIENT] Store auth sera initialisé par le router guard selon la route"),J.info("Montage de l'application"),gc.mount("#vue-app"),J.info("Application montée avec succès");export{F as A,Nt as _,Zo as a,J as l,Ee as u};
