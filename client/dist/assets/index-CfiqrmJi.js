const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/InvoiceDetailView-DWf_kU5s.js","assets/vendor-hWiorkjf.js","assets/utils-CG2kLDjF.js","assets/InvoiceDetailView-oG9dMU0-.css","assets/TicketDetailView-CdHdZWHy.js","assets/TicketDetailView-GH8e5TaN.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty;import{a as t,c as s}from"./utils-CG2kLDjF.js";import{d as n,r as a,c as i,a as r,u as o,w as l,o as c,b as u,e as d,f as h,g as p,h as f,i as v,n as m,t as g,T as y,j as b,F as w,k as _,l as k,m as C,p as S,q as E,s as R,v as T,x as A,y as O,z as I,A as M,B as L,C as P,D as N,E as U,G as x,H as D,I as B,J as V,K as q,L as H,M as G}from"./vendor-hWiorkjf.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const j=t.create({baseURL:"",headers:{"Content-Type":"application/json",Accept:"application/json"},timeout:3e4});j.defaults.withCredentials=!0;const F={auth:{login:(e,t,s=!1)=>j.post("/api/v1/client/auth/login",{email:e,password:t,remember:s}),register:e=>j.post("/api/v1/client/auth/register",e),logout:()=>j.post("/api/v1/client/auth/logout"),me:()=>j.get("/api/v1/client/auth/me"),forgotPassword:e=>j.post("/api/v1/client/auth/forgot-password",{email:e}),resetPassword:(e,t,s)=>j.post("/api/v1/client/auth/reset-password",{token:e,password:t,password_confirmation:s})},client:{dashboard:{getStats:()=>j.get("/api/v1/client/dashboard/stats"),getOverview:()=>j.get("/api/v1/client/dashboard/overview")},service:{list:e=>j.get("/api/v1/client/services",{params:e}),get:e=>j.get(`/api/v1/client/services/${e}`),getById:e=>j.get(`/api/v1/client/services/${e}`),getHistory:e=>j.get(`/api/v1/client/services/${e}/history`),getUsage:e=>j.get(`/api/v1/client/services/${e}/usage`),getRecent:()=>j.get("/api/v1/client/services/recent")},invoice:{list:e=>j.get("/api/v1/client/invoices",{params:e}),get:e=>j.get(`/api/v1/client/invoices/${e}`),getById:e=>j.get(`/api/v1/client/invoices/${e}`),downloadPdf:e=>j.get(`/api/v1/client/invoices/${e}/pdf`,{responseType:"blob"}),getRecent:()=>j.get("/api/v1/client/invoices/recent"),getUnpaid:()=>j.get("/api/v1/client/invoices/unpaid")},ticket:{list:e=>j.get("/api/v1/client/tickets",{params:e}),get:e=>j.get(`/api/v1/client/tickets/${e}`),getById:e=>j.get(`/api/v1/client/tickets/${e}`),getMessages:(e,t)=>j.get(`/api/v1/client/tickets/${e}/messages`,{params:t}),create:e=>j.post("/api/v1/client/tickets",e),addReply:(e,t)=>j.post(`/api/v1/client/tickets/${e}/messages`,t),close:e=>j.put(`/api/v1/client/tickets/${e}/close`),reopen:e=>j.put(`/api/v1/client/tickets/${e}/reopen`),getRecent:()=>j.get("/api/v1/client/tickets/recent"),getOpen:()=>j.get("/api/v1/client/tickets/open")},license:{list:()=>j.get("/api/v1/client/licenses"),get:e=>j.get(`/api/v1/client/licenses/${e}`),verify:(e,t)=>j.post("/api/v1/license/verify",{license_key:e,domain:t}),getStatus:e=>j.post("/api/v1/license/status",{license_key:e})},shop:{getTemplates:e=>j.get("/api/v1/website/templates",{params:e}),getTemplate:e=>j.get(`/api/v1/website/templates/${e}`),createOrder:e=>j.post("/api/v1/client/orders",e),getOrders:e=>j.get("/api/v1/client/orders",{params:e}),getOrder:e=>j.get(`/api/v1/client/orders/${e}`)},updates:{getVersions:e=>j.get("/api/v1/client/updates/versions",{params:e}),getLicenseInfo:()=>j.get("/api/v1/client/updates/license-info"),getStats:()=>j.get("/api/v1/client/updates/stats"),check:()=>j.post("/api/v1/client/updates/check"),downloadToken:e=>j.post("/api/v1/client/updates/download-token",e)},profile:{get:()=>j.get("/api/v1/client/profile"),update:e=>j.put("/api/v1/client/profile",e),changePassword:e=>j.post("/api/v1/client/profile/password",e)},getRealtimeToken:()=>j.get("/api/v1/client/realtime/token"),department:{list:()=>j.get("/api/v1/client/departments")},logging:{log:(e,t,s)=>j.post("/api/v1/client/log",{level:e,message:t,context:s})}}};class ${static get client(){return j}static setAuthToken(e){e?j.defaults.headers.common.Authorization=`Bearer ${e}`:delete j.defaults.headers.common.Authorization}static setupInterceptors(){j.interceptors.response.use(e=>e,e=>{var t;return 401===(null==(t=e.response)?void 0:t.status)&&(window.location.href="/client/login"),Promise.reject(e)})}}var W;async function z(e,t,s={}){try{await $.routes.client.logging.log(e,t,{...s,url:window.location.href})}catch(n){}}((t,s,n)=>{s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[s]=n})($,"symbol"!=typeof(W="routes")?W+"":W,F),$.setupInterceptors();const J={info:(e,t)=>{z("info",e,t)},debug:(e,t)=>{},warn:(e,t)=>{z("warning",e,t)},error:(e,t)=>{z("error",e,t)}},K=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,Y=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,Q=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function X(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t}function Z(e,t){if(null==e)return;let s=e;for(let n=0;n<t.length;n++){if(null==s||null==s[t[n]])return;s=s[t[n]]}return s}function ee(e,t,s){if(0===s.length)return t;const n=s[0];return s.length>1&&(t=ee("object"==typeof e&&null!==e&&Object.prototype.hasOwnProperty.call(e,n)?e[n]:Number.isInteger(Number(s[1]))?[]:{},t,Array.prototype.slice.call(s,1))),Number.isInteger(Number(n))&&Array.isArray(e)?e.slice()[n]:Object.assign({},e,{[n]:t})}function te(e,t){if(null==e||0===t.length)return e;if(1===t.length){if(null==e)return e;if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.slice.call(e,0).splice(t[0],1);const s={};for(const t in e)s[t]=e[t];return delete s[t[0]],s}if(null==e[t[0]]){if(Number.isInteger(t[0])&&Array.isArray(e))return Array.prototype.concat.call([],e);const s={};for(const t in e)s[t]=e[t];return s}return ee(e,te(e[t[0]],Array.prototype.slice.call(t,1)),[t[0]])}function se(e,t){return t.map(e=>e.split(".")).map(t=>[t,Z(e,t)]).filter(e=>void 0!==e[1]).reduce((e,t)=>ee(e,t[1],t[0]),{})}function ne(e,t){return t.map(e=>e.split(".")).reduce((e,t)=>te(e,t),e)}function ae(e,{storage:t,serializer:s,key:n,debug:a,pick:i,omit:r,beforeHydrate:o,afterHydrate:l},c,u=!0){try{u&&(null==o||o(c));const a=t.getItem(n);if(a){const t=s.deserialize(a),n=i?se(t,i):t,o=r?ne(n,r):n;e.$patch(o)}u&&(null==l||l(c))}catch(d){}}function ie(e,{storage:t,serializer:s,key:n,debug:a,pick:i,omit:r}){try{const a=i?se(e,i):e,o=r?ne(a,r):a,l=s.serialize(o);t.setItem(n,l)}catch(o){}}var re=function(e={}){return function(t){!function(e,t,s){const{pinia:n,store:a,options:{persist:i=s}}=e;if(!i)return;if(!(a.$id in n.state.value)){const e=n._s.get(a.$id.replace("__hot:",""));return void(e&&Promise.resolve().then(()=>e.$persist()))}const r=(Array.isArray(i)?i:!0===i?[{}]:[i]).map(t);a.$hydrate=({runHooks:t=!0}={})=>{r.forEach(s=>{ae(a,s,e,t)})},a.$persist=()=>{r.forEach(e=>{ie(a.$state,e)})},r.forEach(t=>{ae(a,t,e),a.$subscribe((e,s)=>ie(s,t),{detached:!0})})}(t,s=>({key:(e.key?e.key:e=>e)(s.key??t.store.$id),debug:s.debug??e.debug??!1,serializer:s.serializer??e.serializer??{serialize:e=>JSON.stringify(e),deserialize:e=>function(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const s=e.trim();if(s.length<=9)switch(s.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!Q.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(K.test(e)||Y.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,X)}return JSON.parse(e)}catch(n){if(t.strict)throw n;return e}}(e)},storage:s.storage??e.storage??window.localStorage,beforeHydrate:s.beforeHydrate,afterHydrate:s.afterHydrate,pick:s.pick,omit:s.omit}),e.auto??!1)}}();const oe=n("clientAuth",()=>{const e=a(null),t=a(!1),s=a(null),n=a(!1),r=i(()=>!!e.value),o=i(()=>e.value?`${e.value.firstName} ${e.value.lastName}`:""),l=async()=>{var s,a;if(n.value)return r.value;t.value=!0;try{J.info("[CLIENT-AUTH] Appel API /me");const t=await $.routes.auth.me();if(J.info("[CLIENT-AUTH] Réponse API /me",{response:t}),null==(s=t.data)?void 0:s.user){if(e.value=t.data.user,null==(a=t.data)?void 0:a.token)$.setAuthToken(t.data.token);else{const e=(()=>{const e=localStorage.getItem("techcms-client-token");if(e)return e;const t=document.cookie.split(";");for(const s of t){const[e,t]=s.trim().split("=");if("techcms_client_token"===e||"auth_token"===e)return t}return null})();e&&$.setAuthToken(e)}return J.info("[CLIENT-AUTH] Client authentifié via cookie",{user:e.value}),!0}return J.info("[CLIENT-AUTH] Client non authentifié"),!1}catch(i){return J.error("[CLIENT-AUTH] Erreur lors de la vérification de l'authentification",{error:i}),e.value=null,$.setAuthToken(null),!1}finally{t.value=!1,n.value=!0}};return{user:e,loading:t,error:s,initialized:n,isAuthenticated:r,userFullName:o,login:async(n,a,i=!1)=>{var r,o,l,c;t.value=!0,s.value=null;try{const t=await $.routes.auth.login(n,a,i);if(null==(r=t.data)?void 0:r.user)return e.value=t.data.user,(null==(o=t.data)?void 0:o.token)&&($.setAuthToken(t.data.token),localStorage.setItem("techcms-client-token",t.data.token)),!0;throw new Error("Réponse invalide du serveur")}catch(u){throw s.value=(null==(c=null==(l=u.response)?void 0:l.data)?void 0:c.message)||u.message||"Erreur de connexion",u}finally{t.value=!1}},register:async e=>{var n,a;t.value=!0,s.value=null;try{return(await $.routes.auth.register({firstname:e.firstname,lastname:e.lastname,email:e.email,company:e.company,phone:e.phone,address:e.address,postal_code:e.postal_code,city:e.city,country:e.country,password:e.password})).data}catch(i){throw s.value=(null==(a=null==(n=i.response)?void 0:n.data)?void 0:a.message)||i.message||"Erreur d'inscription",i}finally{t.value=!1}},logout:async()=>{t.value=!0;try{await $.routes.auth.logout()}catch(n){}finally{e.value=null,s.value=null,t.value=!1,$.setAuthToken(null),localStorage.removeItem("techcms-client-token")}},checkAuth:l,initialize:async()=>{n.value||await l()},clearError:()=>{s.value=null},updateUser:t=>{e.value&&(e.value={...e.value,...t})},syncCartOnLogin:()=>{}}},{persist:{key:"techcms-client-auth",storage:localStorage}});var le,ce={exports:{}};
/*@license Copyright 2015-2022 Ably Real-time Ltd (ably.com)

Ably JavaScript Library v2.9.0
https://github.com/ably/ably-js

Released under the Apache Licence v2.0*/var ue,de=le?ce.exports:(le=1,ue=()=>{var e,t={},n={exports:t},a=Object.defineProperty,i=Object.defineProperties,r=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyDescriptors,l=Object.getOwnPropertyNames,c=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,d=Object.prototype.propertyIsEnumerable,h=(e,t,s)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s,p=(e,t)=>{for(var s in t||(t={}))u.call(t,s)&&h(e,s,t[s]);if(c)for(var s of c(t))d.call(t,s)&&h(e,s,t[s]);return e},f=(e,t)=>i(e,o(t)),v=(e,t)=>{for(var s in t)a(e,s,{get:t[s],enumerable:!0})},m={};v(m,{ErrorInfo:()=>E,Realtime:()=>sn,Rest:()=>Es,default:()=>ea,makeProtocolMessageFromDeserialized:()=>hs,msgpack:()=>Qn}),n.exports=(e=m,((e,t,s,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of l(t))u.call(e,i)||i===s||a(e,i,{get:()=>t[i],enumerable:!(n=r(t,i))||n.enumerable});return e})(a({},"__esModule",{value:!0}),e));var g=class{},y=void 0!==s?s:"undefined"!=typeof window?window:self;function b(e,t){return`${e}`.padStart(t?3:2,"0")}function w(e){return g.Config.logTimestamps?function(t){const s=new Date;e(b(s.getHours())+":"+b(s.getMinutes())+":"+b(s.getSeconds())+"."+b(s.getMilliseconds(),1)+" "+t)}:function(t){e(t)}}var _=class e{constructor(){this.deprecated=(e,t)=>{this.deprecationWarning(`${e} is deprecated and will be removed in a future version. ${t}`)},this.shouldLog=e=>e<=this.logLevel,this.setLog=(e,t)=>{void 0!==e&&(this.logLevel=e),void 0!==t&&(this.logHandler=this.logErrorHandler=t)},this.logLevel=e.defaultLogLevel,this.logHandler=e.defaultLogHandler,this.logErrorHandler=e.defaultLogErrorHandler}static initLogHandlers(){const[t,s]=(()=>{var e;let t,s;return"function"==typeof(null==(e=null==y?void 0:y.console)?void 0:e.log)?(t=function(...e){},s=console.warn?function(...e){}:t):t=s=function(){},[t,s].map(w)})();this.defaultLogHandler=t,this.defaultLogErrorHandler=s,this.defaultLogger=new e}static logActionNoStrip(e,t,s,n){e.logAction(t,s,n)}logAction(e,t,s){this.shouldLog(e)&&(1===e?this.logErrorHandler:this.logHandler)("Ably: "+t+": "+s,e)}renamedClientOption(e,t){this.deprecationWarning(`The \`${e}\` client option has been renamed to \`${t}\`. Please update your code to use \`${t}\` instead. \`${e}\` will be removed in a future version.`)}renamedMethod(e,t,s){this.deprecationWarning(`\`${e}\`’s \`${t}\` method has been renamed to \`${s}\`. Please update your code to use \`${s}\` instead. \`${t}\` will be removed in a future version.`)}deprecationWarning(e){this.shouldLog(1)&&this.logErrorHandler(`Ably: Deprecation warning - ${e}`,1)}};_.defaultLogLevel=1,_.LOG_NONE=0,_.LOG_ERROR=1,_.LOG_MAJOR=2,_.LOG_MINOR=3,_.LOG_MICRO=4,_.logAction=(e,t,s,n)=>{_.logActionNoStrip(e,t,s,n)};var k=_,C={};function S(e){let t="["+e.constructor.name;return e.message&&(t+=": "+e.message),e.statusCode&&(t+="; statusCode="+e.statusCode),e.code&&(t+="; code="+e.code),e.cause&&(t+="; cause="+Q(e.cause)),!e.href||e.message&&e.message.indexOf("help.ably.io")>-1||(t+="; see "+e.href+" "),t+="]",t}v(C,{Format:()=>W,allSame:()=>$,allToLowerCase:()=>re,allToUpperCase:()=>oe,arrChooseN:()=>se,arrDeleteValue:()=>q,arrEquals:()=>ve,arrIntersect:()=>B,arrIntersectOb:()=>V,arrPopRandomElement:()=>z,arrWithoutValue:()=>H,cheapRandStr:()=>ee,containsValue:()=>x,copy:()=>A,createMissingPluginError:()=>me,dataSizeBytes:()=>Z,decodeBody:()=>ae,encodeBody:()=>ie,ensureArray:()=>O,forInOwnNonNullProperties:()=>F,getBackoffCoefficient:()=>le,getGlobalObject:()=>de,getJitterCoefficient:()=>ce,getRetryTime:()=>ue,inherits:()=>U,inspectBody:()=>X,inspectError:()=>Q,intersect:()=>D,isEmpty:()=>M,isErrorInfoOrPartialErrorInfo:()=>Y,isNil:()=>L,isObject:()=>I,keysArray:()=>G,matchDerivedChannel:()=>pe,mixin:()=>T,parseQueryString:()=>K,prototypicalClone:()=>N,randomString:()=>te,shallowClone:()=>P,shallowEquals:()=>he,throwMissingPluginError:()=>ge,toBase64:()=>fe,toQueryString:()=>J,valuesArray:()=>j,whenPromiseSettles:()=>ne,withTimeoutAsync:()=>ye});var E=class e extends Error{constructor(t,s,n,a){super(t),void 0!==Object.setPrototypeOf&&Object.setPrototypeOf(this,e.prototype),this.code=s,this.statusCode=n,this.cause=a}toString(){return S(this)}static fromValues(t){const{message:s,code:n,statusCode:a}=t;if("string"!=typeof s||"number"!=typeof n||"number"!=typeof a)throw new Error("ErrorInfo.fromValues(): invalid values: "+g.Config.inspect(t));const i=Object.assign(new e(s,n,a),t);return i.code&&!i.href&&(i.href="https://help.ably.io/error/"+i.code),i}},R=class e extends Error{constructor(t,s,n,a){super(t),void 0!==Object.setPrototypeOf&&Object.setPrototypeOf(this,e.prototype),this.code=s,this.statusCode=n,this.cause=a}toString(){return S(this)}static fromValues(t){const{message:s,code:n,statusCode:a}=t;if("string"!=typeof s||!L(n)&&"number"!=typeof n||!L(a)&&"number"!=typeof a)throw new Error("PartialErrorInfo.fromValues(): invalid values: "+g.Config.inspect(t));const i=Object.assign(new e(s,n,a),t);return i.code&&!i.href&&(i.href="https://help.ably.io/error/"+i.code),i}};function T(e,...t){for(let s=0;s<t.length;s++){const n=t[s];if(!n)break;for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e}function A(e){return T({},e)}function O(e){return L(e)?[]:Array.isArray(e)?e:[e]}function I(e){return"[object Object]"==Object.prototype.toString.call(e)}function M(e){for(const t in e)return!1;return!0}function L(e){return null==e}function P(e){const t=new Object;for(const s in e)t[s]=e[s];return t}function N(e,t){class s{}s.prototype=e;const n=new s;return t&&T(n,t),n}var U=function(e,t){g.Config.inherits?g.Config.inherits(e,t):(e.super_=t,e.prototype=N(t.prototype,{constructor:e}))};function x(e,t){for(const s in e)if(e[s]==t)return!0;return!1}function D(e,t){return Array.isArray(t)?B(e,t):V(e,t)}function B(e,t){const s=[];for(let n=0;n<e.length;n++){const a=e[n];-1!=t.indexOf(a)&&s.push(a)}return s}function V(e,t){const s=[];for(let n=0;n<e.length;n++){const a=e[n];a in t&&s.push(a)}return s}function q(e,t){const s=e.indexOf(t),n=-1!=s;return n&&e.splice(s,1),n}function H(e,t){const s=e.slice();return q(s,t),s}function G(e,t){const s=[];for(const n in e)t&&!Object.prototype.hasOwnProperty.call(e,n)||s.push(n);return s}function j(e,t){const s=[];for(const n in e)t&&!Object.prototype.hasOwnProperty.call(e,n)||s.push(e[n]);return s}function F(e,t){for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&e[s]&&t(s)}function $(e,t){if(0===e.length)return!0;const s=e[0][t];return e.every(function(e){return e[t]===s})}var W=(e=>(e.msgpack="msgpack",e.json="json",e))(W||{});function z(e){return e.splice((t=e,Math.floor(Math.random()*t.length)),1)[0];var t}function J(e){const t=[];if(e)for(const s in e)t.push(encodeURIComponent(s)+"="+encodeURIComponent(e[s]));return t.length?"?"+t.join("&"):""}function K(e){let t;const s=/([^?&=]+)=?([^&]*)/g,n={};for(;t=s.exec(e);)n[decodeURIComponent(t[1])]=decodeURIComponent(t[2]);return n}function Y(e){return"object"==typeof e&&null!==e&&(e instanceof E||e instanceof R)}function Q(e){var t,s;return e instanceof Error||"ErrorInfo"===(null==(t=null==e?void 0:e.constructor)?void 0:t.name)||"PartialErrorInfo"===(null==(s=null==e?void 0:e.constructor)?void 0:s.name)?e.toString():g.Config.inspect(e)}function X(e){return g.BufferUtils.isBuffer(e)?e.toString():"string"==typeof e?e:g.Config.inspect(e)}function Z(e){if(g.BufferUtils.isBuffer(e))return g.BufferUtils.byteLength(e);if("string"==typeof e)return g.Config.stringByteSize(e);if("number"==typeof e)return 8;if("boolean"==typeof e)return 1;throw new Error("Expected input of Utils.dataSizeBytes to be a string, a number, a boolean or a buffer, but was: "+typeof e)}function ee(){return String(Math.random()).substr(2)}var te=async e=>{const t=await g.Config.getRandomArrayBuffer(e);return g.BufferUtils.base64Encode(t)};function se(e,t){const s=Math.min(t,e.length),n=e.slice(),a=[];for(let i=0;i<s;i++)a.push(z(n));return a}function ne(e,t){e.then(e=>{null==t||t(null,e)}).catch(e=>{null==t||t(e)})}function ae(e,t,s){return"msgpack"==s?(t||ge("MsgPack"),t.decode(e)):JSON.parse(String(e))}function ie(e,t,s){return"msgpack"==s?(t||ge("MsgPack"),t.encode(e,!0)):JSON.stringify(e)}function re(e){return e.map(function(e){return e&&e.toLowerCase()})}function oe(e){return e.map(function(e){return e&&e.toUpperCase()})}function le(e){return Math.min((e+2)/3,2)}function ce(){return 1-.2*Math.random()}function ue(e,t){return e*le(t)*ce()}function de(){return void 0!==s?s:"undefined"!=typeof window?window:self}function he(e,t){return Object.keys(e).every(s=>e[s]===t[s])&&Object.keys(t).every(s=>t[s]===e[s])}function pe(e){const t=e.match(/^(\[([^?]*)(?:(.*))\])?(.+)$/);if(!t||!t.length||t.length<5)throw new E("regex match failed",400,40010);if(t[2])throw new E(`cannot use a derived option with a ${t[2]} channel`,400,40010);return{qualifierParam:t[3]||"",channelName:t[4]}}function fe(e){const t=g.BufferUtils,s=t.utf8Encode(e);return t.base64Encode(s)}function ve(e,t){return e.length===t.length&&e.every(function(e,s){return e===t[s]})}function me(e){return new E(`${e} plugin not provided`,40019,400)}function ge(e){throw me(e)}async function ye(e,t=5e3,s="Timeout expired"){const n=new E(s,5e4,500);return Promise.race([e,new Promise((e,s)=>setTimeout(()=>s(n),t))])}var be="2.9.0",we={ENVIRONMENT:"",REST_HOST:"rest.ably.io",REALTIME_HOST:"realtime.ably.io",FALLBACK_HOSTS:["A.ably-realtime.com","B.ably-realtime.com","C.ably-realtime.com","D.ably-realtime.com","E.ably-realtime.com"],PORT:80,TLS_PORT:443,TIMEOUTS:{disconnectedRetryTimeout:15e3,suspendedRetryTimeout:3e4,httpRequestTimeout:1e4,httpMaxRetryDuration:15e3,channelRetryTimeout:15e3,fallbackRetryTimeout:6e5,connectionStateTtl:12e4,realtimeRequestTimeout:1e4,recvTimeout:9e4,webSocketConnectTimeout:1e4,webSocketSlowTimeout:4e3},httpMaxRetryCount:3,maxMessageSize:65536,version:be,protocolVersion:3,agent:"ably-js/"+be,getHost:_e,getPort:function(e,t){return t||e.tls?e.tlsPort:e.port},getHttpScheme:function(e){return e.tls?"https://":"http://"},environmentFallbackHosts:ke,getFallbackHosts:Ce,getHosts:function(e,t){const s=[e.restHost].concat(Ce(e));return t?s.map(t=>_e(e,t,!0)):s},checkHost:Se,objectifyOptions:function(e,t,s,n,a){if(void 0===e){const e=t?`${s} must be initialized with either a client options object, an Ably API key, or an Ably Token`:`${s} must be initialized with a client options object`;throw k.logAction(n,k.LOG_ERROR,`${s}()`,e),new Error(e)}let i;if("string"==typeof e)if(-1==e.indexOf(":")){if(!t){const e=`${s} cannot be initialized with just an Ably Token; you must provide a client options object with a \`plugins\` property. (Set this Ably Token as the object’s \`token\` property.)`;throw k.logAction(n,k.LOG_ERROR,`${s}()`,e),new Error(e)}i={token:e}}else{if(!t){const e=`${s} cannot be initialized with just an Ably API key; you must provide a client options object with a \`plugins\` property. (Set this Ably API key as the object’s \`key\` property.)`;throw k.logAction(n,k.LOG_ERROR,`${s}()`,e),new Error(e)}i={key:e}}else i=e;return a&&(i=f(p({},i),{plugins:p(p({},a),i.plugins)})),i},normaliseOptions:function(e,t,s){const n=null!=s?s:k.defaultLogger;"function"==typeof e.recover&&!0===e.closeOnUnload&&(k.logAction(n,k.LOG_ERROR,"Defaults.normaliseOptions","closeOnUnload was true and a session recovery function was set - these are mutually exclusive, so unsetting the latter"),e.recover=void 0),"closeOnUnload"in e||(e.closeOnUnload=!e.recover),"queueMessages"in e||(e.queueMessages=!0);const a=e.environment&&String(e.environment).toLowerCase()||we.ENVIRONMENT,i=!a||"production"===a;e.fallbackHosts||e.restHost||e.realtimeHost||e.port||e.tlsPort||(e.fallbackHosts=i?we.FALLBACK_HOSTS:ke(a));const r=e.restHost||(i?we.REST_HOST:a+"-"+we.REST_HOST),o=function(e,t,s,n){return e.realtimeHost?e.realtimeHost:e.restHost?(k.logAction(n,k.LOG_MINOR,"Defaults.normaliseOptions",'restHost is set to "'+e.restHost+'" but realtimeHost is not set, so setting realtimeHost to "'+e.restHost+'" too. If this is not what you want, please set realtimeHost explicitly.'),e.restHost):t?we.REALTIME_HOST:s+"-"+we.REALTIME_HOST}(e,i,a,n);(e.fallbackHosts||[]).concat(r,o).forEach(Se),e.port=e.port||we.PORT,e.tlsPort=e.tlsPort||we.TLS_PORT,"tls"in e||(e.tls=!0);const l=function(e){const t={};for(const s in we.TIMEOUTS)t[s]=e[s]||we.TIMEOUTS[s];return t}(e);e.useBinaryProtocol=!!t&&("useBinaryProtocol"in e?g.Config.supportsBinary&&e.useBinaryProtocol:g.Config.preferBinary);const c={};e.clientId&&(c["X-Ably-ClientId"]=g.BufferUtils.base64Encode(g.BufferUtils.utf8Encode(e.clientId))),"idempotentRestPublishing"in e||(e.idempotentRestPublishing=!0);let u=null,d=e.connectivityCheckUrl;if(e.connectivityCheckUrl){let[t,s]=e.connectivityCheckUrl.split("?");u=s?K(s):{},-1===t.indexOf("://")&&(t="https://"+t),d=t}let h=e.wsConnectivityCheckUrl;return h&&-1===h.indexOf("://")&&(h="wss://"+h),f(p({},e),{realtimeHost:o,restHost:r,maxMessageSize:e.maxMessageSize||we.maxMessageSize,timeouts:l,connectivityCheckParams:u,connectivityCheckUrl:d,wsConnectivityCheckUrl:h,headers:c})},defaultGetHeaders:function(e,{format:t=Ae.format,protocolVersion:s=Ae.protocolVersion}={}){return{accept:Te[t],"X-Ably-Version":s.toString(),"Ably-Agent":Ee(e)}},defaultPostHeaders:function(e,{format:t=Ae.format,protocolVersion:s=Ae.protocolVersion}={}){let n;return{accept:n=Te[t],"content-type":n,"X-Ably-Version":s.toString(),"Ably-Agent":Ee(e)}}};function _e(e,t,s){return t=s?t==e.restHost&&e.realtimeHost||t||e.realtimeHost:t||e.restHost}function ke(e){return[e+"-a-fallback.ably-realtime.com",e+"-b-fallback.ably-realtime.com",e+"-c-fallback.ably-realtime.com",e+"-d-fallback.ably-realtime.com",e+"-e-fallback.ably-realtime.com"]}function Ce(e){const t=e.fallbackHosts,s=void 0!==e.httpMaxRetryCount?e.httpMaxRetryCount:we.httpMaxRetryCount;return t?se(t,s):[]}function Se(e){if("string"!=typeof e)throw new E("host must be a string; was a "+typeof e,4e4,400);if(!e.length)throw new E("host must not be zero-length",4e4,400)}function Ee(e){let t=we.agent;if(e.agents)for(var s in e.agents)t+=" "+s+"/"+e.agents[s];return t}function Re(e,t,s){const n=s||{};if(n.cipher){e||ge("Crypto");const s=e.getCipher(n.cipher,t);n.cipher=s.cipherParams,n.channelCipher=s.cipher}else"cipher"in n&&(n.cipher=void 0,n.channelCipher=null);return n}var Te={json:"application/json",xml:"application/xml",html:"text/html",msgpack:"application/x-msgpack",text:"text/plain"},Ae={format:"json",protocolVersion:we.protocolVersion},Oe=we,Ie=class e{constructor(e,t){this.logger=e,this.members=t||[]}call(e,t){for(const n of this.members)if(n)try{n(e,t)}catch(s){k.logAction(this.logger,k.LOG_ERROR,"Multicaster multiple callback handler","Unexpected exception: "+s+"; stack = "+s.stack)}}push(...e){this.members.push(...e)}createPromise(){return new Promise((e,t)=>{this.push((s,n)=>{s?t(s):e(n)})})}resolveAll(e){this.call(null,e)}rejectAll(e){this.call(e)}static create(t,s){const n=new e(t,s);return Object.assign((e,t)=>n.call(e,t),{push:e=>n.push(e),createPromise:()=>n.createPromise(),resolveAll:e=>n.resolveAll(e),rejectAll:e=>n.rejectAll(e)})}},Me=(e=>(e.Get="get",e.Delete="delete",e.Post="post",e.Put="put",e.Patch="patch",e))(Me||{}),Le=Me,Pe=(e=>(e[e.Success=200]="Success",e[e.NoContent=204]="NoContent",e[e.BadRequest=400]="BadRequest",e[e.Unauthorized=401]="Unauthorized",e[e.Forbidden=403]="Forbidden",e[e.RequestTimeout=408]="RequestTimeout",e[e.InternalServerError=500]="InternalServerError",e))(Pe||{}),Ne=Pe,Ue=Math.pow(2,17);function xe(e){return Y(e)?(e.code||(403===e.statusCode?e.code=40300:(e.code=40170,e.statusCode=401)),e):new E(Q(e),e.code||40170,e.statusCode||401)}function De(e){if(!e)return"";"string"==typeof e&&(e=JSON.parse(e));const t=Object.create(null),s=G(e,!0);if(!s)return"";s.sort();for(let n=0;n<s.length;n++)t[s[n]]=e[s[n]].sort();return JSON.stringify(t)}function Be(e,t){if(e.authCallback)k.logAction(t,k.LOG_MINOR,"Auth()","using token auth with authCallback");else if(e.authUrl)k.logAction(t,k.LOG_MINOR,"Auth()","using token auth with authUrl");else if(e.key)k.logAction(t,k.LOG_MINOR,"Auth()","using token auth with client-side signing");else{if(!e.tokenDetails){const e="authOptions must include valid authentication parameters";throw k.logAction(t,k.LOG_ERROR,"Auth()",e),new Error(e)}k.logAction(t,k.LOG_MINOR,"Auth()","using token auth with supplied token only")}}function Ve(e){return e.useTokenAuth||!function(e){return"useTokenAuth"in e&&!e.useTokenAuth}(e)&&(e.authCallback||e.authUrl||e.token||e.tokenDetails)}var qe=0,He=class{constructor(e,t){if(this.authOptions={},this.client=e,this.tokenParams=t.defaultTokenParams||{},this.currentTokenRequestId=null,this.waitingForTokenRequest=null,Ve(t))(function(e){return!e.key&&!e.authCallback&&!e.authUrl})(t)&&k.logAction(this.logger,k.LOG_ERROR,"Auth()","Warning: library initialized with a token literal without any way to renew the token when it expires (no authUrl, authCallback, or key). See https://help.ably.io/error/40171 for help"),this._saveTokenOptions(t.defaultTokenParams,t),Be(this.authOptions,this.logger);else{if(!t.key){const e="No authentication options provided; need one of: key, authUrl, or authCallback (or for testing only, token or tokenDetails)";throw k.logAction(this.logger,k.LOG_ERROR,"Auth()",e),new E(e,40160,401)}k.logAction(this.logger,k.LOG_MINOR,"Auth()","anonymous, using basic auth"),this._saveBasicOptions(t)}}get logger(){return this.client.logger}async authorize(e,t){if(t&&t.key&&this.authOptions.key!==t.key)throw new E("Unable to update auth options with incompatible key",40102,401);try{let s=await this._forceNewToken(null!=e?e:null,null!=t?t:null);return this.client.connection?new Promise((e,t)=>{this.client.connection.connectionManager.onAuthUpdated(s,(s,n)=>s?t(s):e(n))}):s}catch(s){throw this.client.connection&&s.statusCode===Ne.Forbidden&&this.client.connection.connectionManager.actOnErrorFromAuthorize(s),s}}async _forceNewToken(e,t){this.tokenDetails=null,this._saveTokenOptions(e,t),Be(this.authOptions,this.logger);try{return this._ensureValidAuthCredentials(!0)}finally{delete this.tokenParams.timestamp,delete this.authOptions.queryTime}}async requestToken(e,t){const s=t||this.authOptions,n=e||A(this.tokenParams);let a,i=this.client;if(s.authCallback)k.logAction(this.logger,k.LOG_MINOR,"Auth.requestToken()","using token auth with authCallback"),a=s.authCallback;else if(s.authUrl)k.logAction(this.logger,k.LOG_MINOR,"Auth.requestToken()","using token auth with authUrl"),a=(e,t)=>{const n=T({accept:"application/json, text/plain"},s.authHeaders),a=s.authMethod&&"post"===s.authMethod.toLowerCase();let i;const r=s.authUrl.indexOf("?");r>-1&&(i=K(s.authUrl.slice(r)),s.authUrl=s.authUrl.slice(0,r),a||(s.authParams=T(i,s.authParams)));const o=T({},s.authParams||{},e),l=e=>{var s,n;let a=null!=(s=e.body)?s:null,i=null;if(e.error)k.logAction(this.logger,k.LOG_MICRO,"Auth.requestToken().tokenRequestCallback","Received Error: "+Q(e.error));else{const t=null!=(n=e.headers["content-type"])?n:null;i=Array.isArray(t)?t.join(", "):t,k.logAction(this.logger,k.LOG_MICRO,"Auth.requestToken().tokenRequestCallback","Received; content-type: "+i+"; body: "+X(a))}if(e.error)return void t(e.error,null);if(e.unpacked)return void t(null,a);if(g.BufferUtils.isBuffer(a)&&(a=a.toString()),!i)return void t(new E("authUrl response is missing a content-type header",40170,401),null);const r=i.indexOf("application/json")>-1,o=i.indexOf("text/plain")>-1||i.indexOf("application/jwt")>-1;if(r||o){if(r){if(a.length>Ue)return void t(new E("authUrl response exceeded max permitted length",40170,401),null);try{a=JSON.parse(a)}catch(l){return void t(new E("Unexpected error processing authURL response; err = "+l.message,40170,401),null)}}t(null,a,i)}else t(new E("authUrl responded with unacceptable content-type "+i+", should be either text/plain, application/jwt or application/json",40170,401),null)};if(k.logAction(this.logger,k.LOG_MICRO,"Auth.requestToken().tokenRequestCallback","Requesting token from "+s.authUrl+"; Params: "+JSON.stringify(o)+"; method: "+(a?"POST":"GET")),a){const e=n||{};e["content-type"]="application/x-www-form-urlencoded";const t=J(o).slice(1);ne(this.client.http.doUri(Le.Post,s.authUrl,e,t,i),(e,t)=>l(e||t))}else ne(this.client.http.doUri(Le.Get,s.authUrl,n||{},null,o),(e,t)=>l(e||t))};else{if(!s.key){const e="Need a new token, but authOptions does not include any way to request one (no authUrl, authCallback, or key)";throw k.logAction(this.logger,k.LOG_ERROR,"Auth()","library initialized with a token literal without any way to renew the token when it expires (no authUrl, authCallback, or key). See https://help.ably.io/error/40171 for help"),new E(e,40171,403)}k.logAction(this.logger,k.LOG_MINOR,"Auth.requestToken()","using token auth with client-side signing"),a=(e,t)=>{ne(this.createTokenRequest(e,s),(e,s)=>t(e,null!=s?s:null))}}"capability"in n&&(n.capability=De(n.capability));const r=(e,t)=>{const n="/keys/"+e.keyName+"/requestToken",a=Oe.defaultPostHeaders(this.client.options);s.requestHeaders&&T(a,s.requestHeaders),k.logAction(this.logger,k.LOG_MICRO,"Auth.requestToken().requestToken","Sending POST to "+n+"; Token params: "+JSON.stringify(e)),ne(this.client.http.do(Le.Post,function(e){return i.baseUri(e)+n},a,JSON.stringify(e),null),(e,s)=>e?t(e):t(s.error,s.body,s.unpacked))};return new Promise((e,t)=>{let i=!1,o=this.client.options.timeouts.realtimeRequestTimeout,l=setTimeout(()=>{i=!0;const e="Token request callback timed out after "+o/1e3+" seconds";k.logAction(this.logger,k.LOG_ERROR,"Auth.requestToken()",e),t(new E(e,40170,401))},o);a(n,(n,a,o)=>{if(i)return;if(clearTimeout(l),n)return k.logAction(this.logger,k.LOG_ERROR,"Auth.requestToken()","token request signing call returned error; err = "+Q(n)),void t(xe(n));if("string"==typeof a)return void(0===a.length?t(new E("Token string is empty",40170,401)):a.length>Ue?t(new E("Token string exceeded max permitted length (was "+a.length+" bytes)",40170,401)):"undefined"===a||"null"===a?t(new E("Token string was literal null/undefined",40170,401)):"{"!==a[0]||o&&o.indexOf("application/jwt")>-1?e({token:a}):t(new E("Token was double-encoded; make sure you're not JSON-encoding an already encoded token request or details",40170,401)));if("object"!=typeof a||null===a){const e="Expected token request callback to call back with a token string or token request/details object, but got a "+typeof a;return k.logAction(this.logger,k.LOG_ERROR,"Auth.requestToken()",e),void t(new E(e,40170,401))}const c=JSON.stringify(a).length;if(c>Ue&&!s.suppressMaxLengthCheck)t(new E("Token request/details object exceeded max permitted stringified size (was "+c+" bytes)",40170,401));else if("issued"in a)e(a);else{if(!("keyName"in a)){const e="Expected token request callback to call back with a token string, token request object, or token details object";return k.logAction(this.logger,k.LOG_ERROR,"Auth.requestToken()",e),void t(new E(e,40170,401))}r(a,(s,n,a)=>{if(s)return k.logAction(this.logger,k.LOG_ERROR,"Auth.requestToken()","token request API call returned error; err = "+Q(s)),void t(xe(s));a||(n=JSON.parse(n)),k.logAction(this.logger,k.LOG_MINOR,"Auth.getToken()","token received"),e(n)})}})})}async createTokenRequest(e,t){t=t||this.authOptions,e=e||A(this.tokenParams);const s=t.key;if(!s)throw new E("No key specified",40101,403);const n=s.split(":"),a=n[0],i=n[1];if(!i)throw new E("Invalid key specified",40101,403);if(""===e.clientId)throw new E("clientId can’t be an empty string",40012,400);"capability"in e&&(e.capability=De(e.capability));const r=T({keyName:a},e),o=e.clientId||"",l=e.ttl||"",c=e.capability||"";r.timestamp||(r.timestamp=await this._getTimestamp(t&&t.queryTime));const u=r.nonce||(r.nonce=("000000"+Math.floor(1e16*Math.random())).slice(-16)),d=r.timestamp,h=r.keyName+"\n"+l+"\n"+c+"\n"+o+"\n"+d+"\n"+u+"\n";return r.mac=r.mac||((e,t)=>{const s=g.BufferUtils,n=s.utf8Encode(e),a=s.utf8Encode(t),i=s.hmacSha256(n,a);return s.base64Encode(i)})(h,i),k.logAction(this.logger,k.LOG_MINOR,"Auth.getTokenRequest()","generated signed request"),r}async getAuthParams(){if("basic"==this.method)return{key:this.key};{let e=await this._ensureValidAuthCredentials(!1);if(!e)throw new Error("Auth.getAuthParams(): _ensureValidAuthCredentials returned no error or tokenDetails");return{access_token:e.token}}}async getAuthHeaders(){if("basic"==this.method)return{authorization:"Basic "+this.basicKey};{const e=await this._ensureValidAuthCredentials(!1);if(!e)throw new Error("Auth.getAuthParams(): _ensureValidAuthCredentials returned no error or tokenDetails");return{authorization:"Bearer "+fe(e.token)}}}_saveBasicOptions(e){this.method="basic",this.key=e.key,this.basicKey=fe(e.key),this.authOptions=e||{},"clientId"in e&&this._userSetClientId(e.clientId)}_saveTokenOptions(e,t){this.method="token",e&&(this.tokenParams=e),t&&(t.token&&(t.tokenDetails="string"==typeof t.token?{token:t.token}:t.token),t.tokenDetails&&(this.tokenDetails=t.tokenDetails),"clientId"in t&&this._userSetClientId(t.clientId),this.authOptions=t)}async _ensureValidAuthCredentials(e){const t=this.tokenDetails;if(t){if(this._tokenClientIdMismatch(t.clientId))throw new E("Mismatch between clientId in token ("+t.clientId+") and current clientId ("+this.clientId+")",40102,403);if(!this.client.isTimeOffsetSet()||!t.expires||t.expires>=this.client.getTimestampUsingOffset())return k.logAction(this.logger,k.LOG_MINOR,"Auth.getToken()","using cached token; expires = "+t.expires),t;k.logAction(this.logger,k.LOG_MINOR,"Auth.getToken()","deleting expired token"),this.tokenDetails=null}const s=(this.waitingForTokenRequest||(this.waitingForTokenRequest=Ie.create(this.logger))).createPromise();if(null!==this.currentTokenRequestId&&!e)return s;const n=this.currentTokenRequestId=qe++;let a,i=null;try{a=await this.requestToken(this.tokenParams,this.authOptions)}catch(o){i=o}if(this.currentTokenRequestId>n)return k.logAction(this.logger,k.LOG_MINOR,"Auth._ensureValidAuthCredentials()","Discarding token request response; overtaken by newer one"),s;this.currentTokenRequestId=null;const r=this.waitingForTokenRequest;return this.waitingForTokenRequest=null,i?(null==r||r.rejectAll(i),s):(null==r||r.resolveAll(this.tokenDetails=a),s)}_userSetClientId(e){if("string"!=typeof e&&null!==e)throw new E("clientId must be either a string or null",40012,400);if("*"===e)throw new E('Can’t use "*" as a clientId as that string is reserved. (To change the default token request behaviour to use a wildcard clientId, instantiate the library with {defaultTokenParams: {clientId: "*"}}), or if calling authorize(), pass it in as a tokenParam: authorize({clientId: "*"}, authOptions)',40012,400);{const t=this._uncheckedSetClientId(e);if(t)throw t}}_uncheckedSetClientId(e){if(this._tokenClientIdMismatch(e)){const t="Unexpected clientId mismatch: client has "+this.clientId+", requested "+e,s=new E(t,40102,401);return k.logAction(this.logger,k.LOG_ERROR,"Auth._uncheckedSetClientId()",t),s}return this.clientId=this.tokenParams.clientId=e,null}_tokenClientIdMismatch(e){return!(!this.clientId||"*"===this.clientId||!e||"*"===e||this.clientId===e)}static isTokenErr(e){return e.code&&e.code>=40140&&e.code<40150}revokeTokens(e,t){return this.client.rest.revokeTokens(e,t)}async _getTimestamp(e){return this.client.getTimestamp(e||!!this.authOptions.queryTime)}};function Ge(e){const t=[];if(e)for(const s in e)t.push(s+"="+e[s]);return t.join("&")}function je(e,t){return e+(t?"?":"")+Ge(t)}var Fe=class{constructor(e){this.client=e,this.platformHttp=new g.Http(e),this.checkConnectivity=this.platformHttp.checkConnectivity?()=>this.platformHttp.checkConnectivity():void 0}get logger(){var e,t;return null!=(t=null==(e=this.client)?void 0:e.logger)?t:k.defaultLogger}get supportsAuthHeaders(){return this.platformHttp.supportsAuthHeaders}get supportsLinkHeaders(){return this.platformHttp.supportsLinkHeaders}_getHosts(e){const t=e.connection,s=t&&t.connectionManager.host;return s?[s].concat(Oe.getFallbackHosts(e.options)):Oe.getHosts(e.options)}async do(e,t,s,n,a){try{const i=this.client;if(!i)return{error:new E("http.do called without client",5e4,500)};const r="function"==typeof t?t:function(e){return i.baseUri(e)+t},o=i._currentFallback;if(o){if(o.validUntil>Date.now()){const l=await this.doUri(e,r(o.host),s,n,a);return l.error&&this.platformHttp.shouldFallback(l.error)?(i._currentFallback=null,this.do(e,t,s,n,a)):l}i._currentFallback=null}const l=this._getHosts(i);if(1===l.length)return this.doUri(e,r(l[0]),s,n,a);let c=null;const u=async(t,o)=>{const l=t.shift();c=null!=c?c:new Date;const d=await this.doUri(e,r(l),s,n,a);return d.error&&this.platformHttp.shouldFallback(d.error)&&t.length?Date.now()-c.getTime()>i.options.timeouts.httpMaxRetryDuration?{error:new E(`Timeout for trying fallback hosts retries. Total elapsed time exceeded the ${i.options.timeouts.httpMaxRetryDuration}ms limit`,50003,500)}:u(t,!0):(o&&(i._currentFallback={host:l,validUntil:Date.now()+i.options.timeouts.fallbackRetryTimeout}),d)};return u(l)}catch(i){return{error:new E(`Unexpected error in Http.do: ${Q(i)}`,500,5e4)}}}async doUri(e,t,s,n,a){try{!function(e,t,s,n,a){a.shouldLog(k.LOG_MICRO)&&k.logActionNoStrip(a,k.LOG_MICRO,"Http."+e+"()","Sending; "+je(t,n)+"; Body"+(g.BufferUtils.isBuffer(s)?" (Base64): "+g.BufferUtils.base64Encode(s):": "+s))}(e,t,n,a,this.logger);const i=await this.platformHttp.doUri(e,t,s,n,a);return this.logger.shouldLog(k.LOG_MICRO)&&function(e,t,s,n,a){e.error?k.logActionNoStrip(a,k.LOG_MICRO,"Http."+t+"()","Received Error; "+je(s,n)+"; Error: "+Q(e.error)):k.logActionNoStrip(a,k.LOG_MICRO,"Http."+t+"()","Received; "+je(s,n)+"; Headers: "+Ge(e.headers)+"; StatusCode: "+e.statusCode+"; Body"+(g.BufferUtils.isBuffer(e.body)?" (Base64): "+g.BufferUtils.base64Encode(e.body):": "+e.body))}(i,e,t,a,this.logger),i}catch(i){return{error:new E(`Unexpected error in Http.doUri: ${Q(i)}`,500,5e4)}}}};function $e(e,t,s){let n,a,i;for(let r=0;r<e.length;r++)if(n=e[r],s&&(n=n[s]),Array.isArray(n)){for(;-1!==(a=n.indexOf(t));)n.splice(a,1);s&&0===n.length&&delete e[r][s]}else if(I(n))for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&Array.isArray(n[i])&&$e([n],t,i)}var We=class{constructor(e){this.logger=e,this.any=[],this.events=Object.create(null),this.anyOnce=[],this.eventsOnce=Object.create(null)}on(...e){if(1===e.length){const t=e[0];if("function"!=typeof t)throw new Error("EventListener.on(): Invalid arguments: "+g.Config.inspect(e));this.any.push(t)}if(2===e.length){const[t,s]=e;if("function"!=typeof s)throw new Error("EventListener.on(): Invalid arguments: "+g.Config.inspect(e));if(L(t))this.any.push(s);else if(Array.isArray(t))t.forEach(e=>{this.on(e,s)});else{if("string"!=typeof t)throw new Error("EventListener.on(): Invalid arguments: "+g.Config.inspect(e));(this.events[t]||(this.events[t]=[])).push(s)}}}off(...e){if(0==e.length||L(e[0])&&L(e[1]))return this.any=[],this.events=Object.create(null),this.anyOnce=[],void(this.eventsOnce=Object.create(null));const[t,s]=e;let n=null,a=null;if(1!==e.length&&s){if("function"!=typeof s)throw new Error("EventEmitter.off(): invalid arguments:"+g.Config.inspect(e));[a,n]=[t,s]}else"function"==typeof t?n=t:a=t;if(n&&L(a))$e([this.any,this.events,this.anyOnce,this.eventsOnce],n);else if(Array.isArray(a))a.forEach(e=>{this.off(e,n)});else{if("string"!=typeof a)throw new Error("EventEmitter.off(): invalid arguments:"+g.Config.inspect(e));n?$e([this.events,this.eventsOnce],n,a):(delete this.events[a],delete this.eventsOnce[a])}}listeners(e){if(e){const t=this.events[e]||[];return this.eventsOnce[e]&&Array.prototype.push.apply(t,this.eventsOnce[e]),t.length?t:null}return this.any.length?this.any:null}emit(e,...t){const s={event:e},n=[];this.anyOnce.length&&(Array.prototype.push.apply(n,this.anyOnce),this.anyOnce=[]),this.any.length&&Array.prototype.push.apply(n,this.any);const a=this.eventsOnce[e];a&&(Array.prototype.push.apply(n,a),delete this.eventsOnce[e]);const i=this.events[e];i&&Array.prototype.push.apply(n,i),n.forEach(e=>{!function(e,t,s,n){try{s.apply(t,n)}catch(a){k.logAction(e,k.LOG_ERROR,"EventEmitter.emit()","Unexpected listener exception: "+a+"; stack = "+(a&&a.stack))}}(this.logger,s,e,t)})}once(...e){const t=e.length;if(0===t||1===t&&"function"!=typeof e[0]){const t=e[0];return new Promise(e=>{this.once(t,e)})}const[s,n]=e;if(1===e.length&&"function"==typeof s)this.anyOnce.push(s);else if(L(s)){if("function"!=typeof n)throw new Error("EventEmitter.once(): Invalid arguments:"+g.Config.inspect(e));this.anyOnce.push(n)}else if(Array.isArray(s)){const t=this,a=function(){const i=Array.prototype.slice.call(arguments);if(s.forEach(function(e){t.off(e,a)}),"function"!=typeof n)throw new Error("EventEmitter.once(): Invalid arguments:"+g.Config.inspect(e));n.apply(this,i)};s.forEach(function(e){t.on(e,a)})}else{if("string"!=typeof s)throw new Error("EventEmitter.once(): Invalid arguments:"+g.Config.inspect(e));const t=this.eventsOnce[s]||(this.eventsOnce[s]=[]);if(n){if("function"!=typeof n)throw new Error("EventEmitter.once(): Invalid arguments:"+g.Config.inspect(e));t.push(n)}}}async whenState(e,t){if("string"!=typeof e||"string"!=typeof t)throw new Error("whenState requires a valid state String argument");return e===t?null:this.once(e)}},ze={HEARTBEAT:0,ACK:1,NACK:2,CONNECT:3,CONNECTED:4,DISCONNECT:5,DISCONNECTED:6,CLOSE:7,CLOSED:8,ERROR:9,ATTACH:10,ATTACHED:11,DETACH:12,DETACHED:13,PRESENCE:14,MESSAGE:15,SYNC:16,AUTH:17,ACTIVATE:18,OBJECT:19,OBJECT_SYNC:20,ANNOTATION:21},Je=[];Object.keys(ze).forEach(function(e){Je[ze[e]]=e});var Ke={HAS_PRESENCE:1,HAS_BACKLOG:2,RESUMED:4,TRANSIENT:16,ATTACH_RESUME:32,HAS_OBJECTS:128,PRESENCE:65536,PUBLISH:1<<17,SUBSCRIBE:1<<18,PRESENCE_SUBSCRIBE:1<<19,ANNOTATION_PUBLISH:1<<21,ANNOTATION_SUBSCRIBE:1<<22,OBJECT_SUBSCRIBE:1<<24,OBJECT_PUBLISH:1<<25},Ye=Object.keys(Ke);Ke.MODE_ALL=Ke.PRESENCE|Ke.PUBLISH|Ke.SUBSCRIBE|Ke.PRESENCE_SUBSCRIBE|Ke.ANNOTATION_PUBLISH|Ke.ANNOTATION_SUBSCRIBE|Ke.OBJECT_SUBSCRIBE|Ke.OBJECT_PUBLISH;var Qe=["PRESENCE","PUBLISH","SUBSCRIBE","PRESENCE_SUBSCRIBE","ANNOTATION_PUBLISH","ANNOTATION_SUBSCRIBE","OBJECT_SUBSCRIBE","OBJECT_PUBLISH"];function Xe(e,t,s){if(s&&s.cipher){e||ge("Crypto");const n=e.getCipher(s.cipher,t);return{cipher:n.cipherParams,channelCipher:n.cipher}}return null!=s?s:{}}async function Ze(e,t,s){let n=s.channelCipher,a=e,i=t?t+"/":"";g.BufferUtils.isBuffer(a)||(a=g.BufferUtils.utf8Encode(String(a)),i+="utf-8/");const r=await n.encrypt(a);return i=i+"cipher+"+n.algorithm,{data:r,encoding:i}}async function et(e,t){const s="string"==typeof e.data||g.BufferUtils.isBuffer(e.data)||null===e.data||void 0===e.data,{data:n,encoding:a}=tt(e.data,e.encoding,s);return e.data=n,e.encoding=a,null!=t&&t.cipher?async function(e,t){const{data:s,encoding:n}=await Ze(e.data,e.encoding,t);return e.data=s,e.encoding=n,e}(e,t):e}function tt(e,t,s){if(s)return{data:e,encoding:t};if(I(e)||Array.isArray(e))return{data:JSON.stringify(e),encoding:t?t+"/json":"json"};throw new E("Data type is unsupported",40013,400)}async function st(e,t){const{data:s,encoding:n,error:a}=await nt(e.data,e.encoding,t);if(e.data=s,e.encoding=n,a)throw a}async function nt(e,t,s){const n=function(e){return e&&e.channelOptions?e:{channelOptions:e,plugins:{},baseEncodedPreviousPayload:void 0}}(s);let a,i=e,r=e,o=t;if(t){const e=t.split("/");let s,c=e.length,u="";try{for(;(s=c)>0;){const t=e[--c].match(/([-\w]+)(\+([\w-]+))?/);if(!t)break;switch(u=t[1],u){case"base64":r=g.BufferUtils.base64Decode(String(r)),s==e.length&&(i=r);continue;case"utf-8":r=g.BufferUtils.utf8Decode(r);continue;case"json":r=JSON.parse(r);continue;case"cipher":if(null!=n.channelOptions&&n.channelOptions.cipher&&n.channelOptions.channelCipher){const e=t[3],s=n.channelOptions.channelCipher;if(e!=s.algorithm)throw new Error("Unable to decrypt message with given cipher; incompatible cipher params");r=await s.decrypt(r);continue}throw new Error("Unable to decrypt message; not an encrypted channel");case"vcdiff":if(!n.plugins||!n.plugins.vcdiff)throw new E("Missing Vcdiff decoder (https://github.com/ably-forks/vcdiff-decoder)",40019,400);if("undefined"==typeof Uint8Array)throw new E("Delta decoding not supported on this browser (need ArrayBuffer & Uint8Array)",40020,400);try{let e=n.baseEncodedPreviousPayload;"string"==typeof e&&(e=g.BufferUtils.utf8Encode(e));const t=g.BufferUtils.toBuffer(e);r=g.BufferUtils.toBuffer(r),r=g.BufferUtils.arrayBufferViewToBuffer(n.plugins.vcdiff.decode(r,t)),i=r}catch(l){throw new E("Vcdiff delta decode failed with "+l,40018,400)}continue;default:throw new Error("Unknown encoding")}}}catch(l){const e=l;a=new E(`Error processing the ${u} encoding, decoder returned ‘${e.message}’`,e.code||40013,400)}finally{o=s<=0?null:e.slice(0,s).join("/")}}return a?{error:a,data:r,encoding:o}:(n.baseEncodedPreviousPayload=i,{data:r,encoding:o})}function at(...e){const t=e.length>0?"json":"msgpack",{data:s,encoding:n}=it(this.data,this.encoding,t);return Object.assign({},this,{encoding:n,data:s})}function it(e,t,s){return e&&g.BufferUtils.isBuffer(e)?"msgpack"===s?{data:g.BufferUtils.toBuffer(e),encoding:t}:{data:g.BufferUtils.base64Encode(e),encoding:t?t+"/base64":"base64"}:{data:e,encoding:t}}var rt={encryptData:Ze,encodeData:tt,encodeDataForWire:it,decodeData:nt};function ot(e){const{id:t,connectionId:s,timestamp:n}=e;let a;switch(e.action){case ze.MESSAGE:a=e.messages;break;case ze.PRESENCE:case ze.SYNC:a=e.presence;break;case ze.ANNOTATION:a=e.annotations;break;case ze.OBJECT:case ze.OBJECT_SYNC:a=e.state;break;default:throw new E("Unexpected action "+e.action,4e4,400)}for(let i=0;i<a.length;i++){const e=a[i];e.connectionId||(e.connectionId=s),e.timestamp||(e.timestamp=n),t&&!e.id&&(e.id=t+":"+i)}}function lt(e,t){let s="["+t;for(const n in e)"data"===n?"string"==typeof e.data?s+="; data="+e.data:g.BufferUtils.isBuffer(e.data)?s+="; data (buffer)="+g.BufferUtils.base64Encode(e.data):void 0!==e.data&&(s+="; data (json)="+JSON.stringify(e.data)):!n||"extras"!==n&&"operation"!==n?void 0!==e[n]&&(s+="; "+n+"="+e[n]):s+="; "+n+"="+JSON.stringify(e[n]);return s+="]",s}var ct=class{},ut=class{constructor(e){var t,s,n,a,i,r,o,l,c,u;this.Platform=g,this.ErrorInfo=E,this.Logger=k,this.Defaults=Oe,this.Utils=C,this.EventEmitter=We,this.MessageEncoding=rt,this._additionalHTTPRequestImplementations=null!=(t=e.plugins)?t:null,this.logger=new k,this.logger.setLog(e.logLevel,e.logHandler),k.logAction(this.logger,k.LOG_MICRO,"BaseClient()","initialized with clientOptions "+g.Config.inspect(e)),this._MsgPack=null!=(n=null==(s=e.plugins)?void 0:s.MsgPack)?n:null;const d=this.options=Oe.normaliseOptions(e,this._MsgPack,this.logger);if(d.key){const e=d.key.match(/^([^:\s]+):([^:.\s]+)$/);if(!e){const e="invalid key parameter";throw k.logAction(this.logger,k.LOG_ERROR,"BaseClient()",e),new E(e,40400,404)}d.keyName=e[1],d.keySecret=e[2]}if("clientId"in d){if("string"!=typeof d.clientId&&null!==d.clientId)throw new E("clientId must be either a string or null",40012,400);if("*"===d.clientId)throw new E('Can’t use "*" as a clientId as that string is reserved. (To change the default token request behaviour to use a wildcard clientId, use {defaultTokenParams: {clientId: "*"}})',40012,400)}k.logAction(this.logger,k.LOG_MINOR,"BaseClient()","started; version = "+Oe.version),this._currentFallback=null,this.serverTimeOffset=null,this.http=new Fe(this),this.auth=new He(this,d),this._rest=(null==(a=e.plugins)?void 0:a.Rest)?new e.plugins.Rest(this):null,this._Crypto=null!=(r=null==(i=e.plugins)?void 0:i.Crypto)?r:null,this.__FilteredSubscriptions=null!=(l=null==(o=e.plugins)?void 0:o.MessageInteractions)?l:null,this._Annotations=null!=(u=null==(c=e.plugins)?void 0:c.Annotations)?u:null}get rest(){return this._rest||ge("Rest"),this._rest}get _FilteredSubscriptions(){return this.__FilteredSubscriptions||ge("MessageInteractions"),this.__FilteredSubscriptions}get channels(){return this.rest.channels}get push(){return this.rest.push}device(){var e;return(null==(e=this.options.plugins)?void 0:e.Push)&&this.push.LocalDevice||ge("Push"),this._device||(this._device=this.push.LocalDevice.load(this)),this._device}baseUri(e){return Oe.getHttpScheme(this.options)+e+":"+Oe.getPort(this.options,!1)}async stats(e){return this.rest.stats(e)}async time(e){return this.rest.time(e)}async request(e,t,s,n,a,i){return this.rest.request(e,t,s,n,a,i)}batchPublish(e){return this.rest.batchPublish(e)}batchPresence(e){return this.rest.batchPresence(e)}setLog(e){this.logger.setLog(e.level,e.handler)}async getTimestamp(e){return!this.isTimeOffsetSet()&&e?this.time():this.getTimestampUsingOffset()}getTimestampUsingOffset(){return Date.now()+(this.serverTimeOffset||0)}isTimeOffsetSet(){return null!==this.serverTimeOffset}};ut.Platform=g;var dt=ut,ht=class e{toJSON(){var e,t,s;return{id:this.id,deviceSecret:this.deviceSecret,platform:this.platform,formFactor:this.formFactor,clientId:this.clientId,metadata:this.metadata,deviceIdentityToken:this.deviceIdentityToken,push:{recipient:null==(e=this.push)?void 0:e.recipient,state:null==(t=this.push)?void 0:t.state,error:null==(s=this.push)?void 0:s.error}}}toString(){var e,t,s,n;let a="[DeviceDetails";return this.id&&(a+="; id="+this.id),this.platform&&(a+="; platform="+this.platform),this.formFactor&&(a+="; formFactor="+this.formFactor),this.clientId&&(a+="; clientId="+this.clientId),this.metadata&&(a+="; metadata="+this.metadata),this.deviceIdentityToken&&(a+="; deviceIdentityToken="+JSON.stringify(this.deviceIdentityToken)),(null==(e=this.push)?void 0:e.recipient)&&(a+="; push.recipient="+JSON.stringify(this.push.recipient)),(null==(t=this.push)?void 0:t.state)&&(a+="; push.state="+this.push.state),(null==(s=this.push)?void 0:s.error)&&(a+="; push.error="+JSON.stringify(this.push.error)),(null==(n=this.push)?void 0:n.metadata)&&(a+="; push.metadata="+this.push.metadata),a+="]",a}static toRequestBody(e,t,s){return ie(e,t,s)}static fromResponseBody(t,s,n){return n&&(t=ae(t,s,n)),Array.isArray(t)?e.fromValuesArray(t):e.fromValues(t)}static fromValues(t){return t.error=t.error&&E.fromValues(t.error),Object.assign(new e,t)}static fromLocalDevice(t){return Object.assign(new e,t)}static fromValuesArray(t){const s=t.length,n=new Array(s);for(let a=0;a<s;a++)n[a]=e.fromValues(t[a]);return n}};async function pt(e,t,s,n){return e.http.supportsAuthHeaders?n(T(await e.auth.getAuthHeaders(),t),s):n(t,T(await e.auth.getAuthParams(),s))}var ft=class e{static async get(t,s,n,a,i,r){return e.do(Le.Get,t,s,null,n,a,i,null!=r&&r)}static async delete(t,s,n,a,i,r){return e.do(Le.Delete,t,s,null,n,a,i,r)}static async post(t,s,n,a,i,r,o){return e.do(Le.Post,t,s,n,a,i,r,o)}static async patch(t,s,n,a,i,r,o){return e.do(Le.Patch,t,s,n,a,i,r,o)}static async put(t,s,n,a,i,r,o){return e.do(Le.Put,t,s,n,a,i,r,o)}static async do(e,t,s,n,a,i,r,o){r&&((i=i||{}).envelope=r);const l=t.logger;let c=await pt(t,a,i,async function a(i,r){var o;if(l.shouldLog(k.LOG_MICRO)){let a=n;if((null==(o=i["content-type"])?void 0:o.indexOf("msgpack"))>0)try{t._MsgPack||ge("MsgPack"),a=t._MsgPack.decode(n)}catch(u){k.logAction(l,k.LOG_MICRO,"Resource."+e+"()","Sending MsgPack Decoding Error: "+Q(u))}k.logAction(l,k.LOG_MICRO,"Resource."+e+"()","Sending; "+je(s,r)+"; Body: "+a)}const c=await t.http.do(e,s,i,n,r);return c.error&&He.isTokenErr(c.error)?(await t.auth.authorize(null,null),pt(t,i,r,a)):{err:c.error,body:c.body,headers:c.headers,unpacked:c.unpacked,statusCode:c.statusCode}});if(r&&(c=function(e,t,s){if(e.err&&!e.body)return{err:e.err};if(e.statusCode===Ne.NoContent)return f(p({},e),{body:[],unpacked:!0});let n=e.body;if(!e.unpacked)try{n=ae(n,t,s)}catch(o){return Y(o)?{err:o}:{err:new R(Q(o),null)}}if(!n)return{err:new R("unenvelope(): Response body is missing",null)};const{statusCode:a,response:i,headers:r}=n;if(void 0===a)return f(p({},e),{body:n,unpacked:!0});if(a<200||a>=300){let t=i&&i.error||e.err;return t||(t=new Error("Error in unenveloping "+n),t.statusCode=a),{err:t,body:i,headers:r,unpacked:!0,statusCode:a}}return{err:e.err,body:i,headers:r,unpacked:!0,statusCode:a}}(c,t._MsgPack,r)),l.shouldLog(k.LOG_MICRO)&&function(e,t,s,n,a){e.err?k.logAction(a,k.LOG_MICRO,"Resource."+t+"()","Received Error; "+je(s,n)+"; Error: "+Q(e.err)):k.logAction(a,k.LOG_MICRO,"Resource."+t+"()","Received; "+je(s,n)+"; Headers: "+Ge(e.headers)+"; StatusCode: "+e.statusCode+"; Body: "+(g.BufferUtils.isBuffer(e.body)?" (Base64): "+g.BufferUtils.base64Encode(e.body):": "+g.Config.inspect(e.body)))}(c,e,s,i,l),o){if(c.err)throw c.err;{const e=p({},c);return delete e.err,e}}return c}};function vt(e){const t=e.match(/^\.\/(\w+)\?(.*)$/);return t&&t[2]&&K(t[2])}var mt=class{constructor(e,t,s){this.resource=e,this.items=t,this._relParams=s}async first(){if(this.hasFirst())return this.get(this._relParams.first);throw new E("No link to the first page of results",40400,404)}async current(){if(this.hasCurrent())return this.get(this._relParams.current);throw new E("No link to the current page of results",40400,404)}async next(){return this.hasNext()?this.get(this._relParams.next):null}hasFirst(){return null!=this._relParams&&"first"in this._relParams}hasCurrent(){return null!=this._relParams&&"current"in this._relParams}hasNext(){return null!=this._relParams&&"next"in this._relParams}isLast(){return!this.hasNext()}async get(e){const t=this.resource,s=await ft.get(t.client,t.path,t.headers,e,t.envelope,!1);return t.handlePage(s)}},gt=class extends mt{constructor(e,t,s,n,a,i){super(e,t,a),this.statusCode=n,this.success=n<300&&n>=200,this.headers=s,this.errorCode=i&&i.code,this.errorMessage=i&&i.message}toJSON(){return{items:this.items,statusCode:this.statusCode,success:this.success,headers:this.headers,errorCode:this.errorCode,errorMessage:this.errorMessage}}},yt=class{constructor(e,t,s,n,a,i){this.client=e,this.path=t,this.headers=s,this.envelope=null!=n?n:null,this.bodyHandler=a,this.useHttpPaginatedResponse=i||!1}get logger(){return this.client.logger}async get(e){const t=await ft.get(this.client,this.path,this.headers,e,this.envelope,!1);return this.handlePage(t)}async delete(e){const t=await ft.delete(this.client,this.path,this.headers,e,this.envelope,!1);return this.handlePage(t)}async post(e,t){const s=await ft.post(this.client,this.path,t,this.headers,e,this.envelope,!1);return this.handlePage(s)}async put(e,t){const s=await ft.put(this.client,this.path,t,this.headers,e,this.envelope,!1);return this.handlePage(s)}async patch(e,t){const s=await ft.patch(this.client,this.path,t,this.headers,e,this.envelope,!1);return this.handlePage(s)}async handlePage(e){if(e.err&&(t=e.err,s=e.body,!this.useHttpPaginatedResponse||!s&&"number"!=typeof t.code))throw k.logAction(this.logger,k.LOG_ERROR,"PaginatedResource.handlePage()","Unexpected error getting resource: err = "+Q(e.err)),e.err;var t,s;let n,a,i;try{n=e.statusCode==Ne.NoContent?[]:await this.bodyHandler(e.body,e.headers||{},e.unpacked)}catch(r){throw e.err||r}return e.headers&&(a=e.headers.Link||e.headers.link)&&(i=function(e){"string"==typeof e&&(e=e.split(","));const t={};for(let s=0;s<e.length;s++){const n=e[s].match(/^\s*<(.+)>;\s*rel="(\w+)"$/);if(n){const e=vt(n[1]);e&&(t[n[2]]=e)}}return t}(a)),this.useHttpPaginatedResponse?new gt(this,n,e.headers||{},e.statusCode,i,e.err):new mt(this,n,i)}},bt=class e{toJSON(){return{channel:this.channel,deviceId:this.deviceId,clientId:this.clientId}}toString(){let e="[PushChannelSubscription";return this.channel&&(e+="; channel="+this.channel),this.deviceId&&(e+="; deviceId="+this.deviceId),this.clientId&&(e+="; clientId="+this.clientId),e+="]",e}static fromResponseBody(t,s,n){return n&&(t=ae(t,s,n)),Array.isArray(t)?e.fromValuesArray(t):e.fromValues(t)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){const s=t.length,n=new Array(s);for(let a=0;a<s;a++)n[a]=e.fromValues(t[a]);return n}};bt.toRequestBody=ie;var wt=bt,_t=class{constructor(e){this.client=e,this.deviceRegistrations=new kt(e),this.channelSubscriptions=new Ct(e)}async publish(e,t){const s=this.client,n=s.options.useBinaryProtocol?"msgpack":"json",a=Oe.defaultPostHeaders(s.options,{format:n}),i={},r=T({recipient:e},t);T(a,s.options.headers),s.options.pushFullWait&&T(i,{fullWait:"true"});const o=ie(r,s._MsgPack,n);await ft.post(s,"/push/publish",o,a,i,null,!0)}},kt=class{constructor(e){this.client=e}async save(e){const t=this.client,s=ht.fromValues(e),n=t.options.useBinaryProtocol?"msgpack":"json",a=Oe.defaultPostHeaders(t.options,{format:n}),i={};T(a,t.options.headers),t.options.pushFullWait&&T(i,{fullWait:"true"});const r=ie(s,t._MsgPack,n),o=await ft.put(t,"/push/deviceRegistrations/"+encodeURIComponent(e.id),r,a,i,null,!0);return ht.fromResponseBody(o.body,t._MsgPack,o.unpacked?void 0:n)}async get(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Oe.defaultGetHeaders(t.options,{format:s}),a=e.id||e;if("string"!=typeof a||!a.length)throw new E("First argument to DeviceRegistrations#get must be a deviceId string or DeviceDetails",4e4,400);T(n,t.options.headers);const i=await ft.get(t,"/push/deviceRegistrations/"+encodeURIComponent(a),n,{},null,!0);return ht.fromResponseBody(i.body,t._MsgPack,i.unpacked?void 0:s)}async list(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s,a=Oe.defaultGetHeaders(t.options,{format:s});return T(a,t.options.headers),new yt(t,"/push/deviceRegistrations",a,n,async function(e,n,a){return ht.fromResponseBody(e,t._MsgPack,a?void 0:s)}).get(e)}async remove(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Oe.defaultGetHeaders(t.options,{format:s}),a={},i=e.id||e;if("string"!=typeof i||!i.length)throw new E("First argument to DeviceRegistrations#remove must be a deviceId string or DeviceDetails",4e4,400);T(n,t.options.headers),t.options.pushFullWait&&T(a,{fullWait:"true"}),await ft.delete(t,"/push/deviceRegistrations/"+encodeURIComponent(i),n,a,null,!0)}async removeWhere(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Oe.defaultGetHeaders(t.options,{format:s});T(n,t.options.headers),t.options.pushFullWait&&T(e,{fullWait:"true"}),await ft.delete(t,"/push/deviceRegistrations",n,e,null,!0)}},Ct=class e{constructor(t){this.remove=e.prototype.removeWhere,this.client=t}async save(e){const t=this.client,s=wt.fromValues(e),n=t.options.useBinaryProtocol?"msgpack":"json",a=Oe.defaultPostHeaders(t.options,{format:n}),i={};T(a,t.options.headers),t.options.pushFullWait&&T(i,{fullWait:"true"});const r=ie(s,t._MsgPack,n),o=await ft.post(t,"/push/channelSubscriptions",r,a,i,null,!0);return wt.fromResponseBody(o.body,t._MsgPack,o.unpacked?void 0:n)}async list(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s,a=Oe.defaultGetHeaders(t.options,{format:s});return T(a,t.options.headers),new yt(t,"/push/channelSubscriptions",a,n,async function(e,n,a){return wt.fromResponseBody(e,t._MsgPack,a?void 0:s)}).get(e)}async removeWhere(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=Oe.defaultGetHeaders(t.options,{format:s});T(n,t.options.headers),t.options.pushFullWait&&T(e,{fullWait:"true"}),await ft.delete(t,"/push/channelSubscriptions",n,e,null,!0)}async listChannels(e){const t=this.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s,a=Oe.defaultGetHeaders(t.options,{format:s});return T(a,t.options.headers),t.options.pushFullWait&&T(e,{fullWait:"true"}),new yt(t,"/push/channels",a,n,async function(e,n,a){const i=!a&&s?ae(e,t._MsgPack,s):e;for(let t=0;t<i.length;t++)i[t]=String(i[t]);return i}).get(e)}},St=class{constructor(e){var t;this.client=e,this.admin=new _t(e),g.Config.push&&(null==(t=e.options.plugins)?void 0:t.Push)&&(this.stateMachine=new e.options.plugins.Push.ActivationStateMachine(e),this.LocalDevice=e.options.plugins.Push.localDeviceFactory(ht))}async activate(e,t){await new Promise((s,n)=>{var a;(null==(a=this.client.options.plugins)?void 0:a.Push)?this.stateMachine?this.stateMachine.activatedCallback?n(new E("Activation already in progress",4e4,400)):(this.stateMachine.activatedCallback=e=>{e?n(e):s()},this.stateMachine.updateFailedCallback=t,this.stateMachine.handleEvent(new this.client.options.plugins.Push.CalledActivate(this.stateMachine,e))):n(new E("This platform is not supported as a target of push notifications",4e4,400)):n(me("Push"))})}async deactivate(e){await new Promise((t,s)=>{var n;(null==(n=this.client.options.plugins)?void 0:n.Push)?this.stateMachine?this.stateMachine.deactivatedCallback?s(new E("Deactivation already in progress",4e4,400)):(this.stateMachine.deactivatedCallback=e=>{e?s(e):t()},this.stateMachine.handleEvent(new this.client.options.plugins.Push.CalledDeactivate(this.stateMachine,e))):s(new E("This platform is not supported as a target of push notifications",4e4,400)):s(me("Push"))})}},Et=["absent","present","enter","leave","update"];async function Rt(e,t,s,n){const a=Xe(t,e,null!=n?n:null);return Ot.fromValues(s).decode(a,e)}async function Tt(e,t){return Promise.all(e.map(function(e){return async function(e,t){return Ot.fromValues(e).decode(t.channelOptions,t.logger)}(e,t)}))}var At=class e extends ct{isSynthesized(){return!this.id||!this.connectionId||this.id.substring(this.connectionId.length,0)!==this.connectionId}parseId(){if(!this.id)throw new Error("parseId(): Presence message does not contain an id");const e=this.id.split(":");return{connectionId:e[0],msgSerial:parseInt(e[1],10),index:parseInt(e[2],10)}}async encode(e){return et(Object.assign(new Ot,this,{action:Et.indexOf(this.action||"present")}),e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}static fromData(t){return t instanceof e?t:e.fromValues({data:t})}toString(){return lt(this,"PresenceMessage")}},Ot=class e extends ct{toJSON(...e){return at.call(this,...e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}async decode(e,t){const s=Object.assign(new At,f(p({},this),{action:Et[this.action]}));try{await st(s,e)}catch(n){k.logAction(t,k.LOG_ERROR,"WirePresenceMessage.decode()",Q(n))}return s}toString(){return lt(this,"WirePresenceMessage")}},It=At,Mt=class{constructor(e){this.channel=e}get logger(){return this.channel.logger}async get(e){k.logAction(this.logger,k.LOG_MICRO,"RestPresence.get()","channel = "+this.channel.name);const t=this.channel.client,s=t.options.useBinaryProtocol?"msgpack":"json",n=this.channel.client.http.supportsLinkHeaders?void 0:s,a=Oe.defaultGetHeaders(t.options,{format:s});return T(a,t.options.headers),new yt(t,this.channel.client.rest.presenceMixin.basePath(this),a,n,async(e,n,a)=>Tt(a?e:ae(e,t._MsgPack,s),this.channel)).get(e)}async history(e){return k.logAction(this.logger,k.LOG_MICRO,"RestPresence.history()","channel = "+this.channel.name),this.channel.client.rest.presenceMixin.history(this,e)}},Lt=["message.create","message.update","message.delete","meta","message.summary"];function Pt(e){let t=0;return e.name&&(t+=e.name.length),e.clientId&&(t+=e.clientId.length),e.extras&&(t+=JSON.stringify(e.extras).length),e.data&&(t+=Z(e.data)),t}async function Nt(e,t,s,n){const a=Xe(t,e,null!=n?n:null);return qt.fromValues(s).decode(a,e)}async function Ut(e,t){return Promise.all(e.map(function(e){return async function(e,t){return qt.fromValues(e).decode(t.channelOptions,t.logger)}(e,t)}))}async function xt(e,t){return Promise.all(e.map(e=>e.encode(t)))}var Dt=ie;function Bt(e){let t,s=0;for(let n=0;n<e.length;n++)t=e[n],s+=t.size||(t.size=Pt(t));return s}var Vt=class e extends ct{expandFields(){"message.create"===this.action&&(this.version&&!this.serial&&(this.serial=this.version),this.timestamp&&!this.createdAt&&(this.createdAt=this.timestamp))}async encode(e){return et(Object.assign(new qt,this,{action:Lt.indexOf(this.action||"message.create")}),e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}toString(){return lt(this,"Message")}},qt=class e extends ct{toJSON(...e){return at.call(this,...e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}async decodeWithErr(e,t){const s=Object.assign(new Vt,f(p({},this),{action:(n=this.action,Lt[n||0]||"unknown")}));var n;let a;try{await st(s,e)}catch(i){k.logAction(t,k.LOG_ERROR,"WireMessage.decode()",Q(i)),a=i}return s.expandFields(),{decoded:s,err:a}}async decode(e,t){const{decoded:s}=await this.decodeWithErr(e,t);return s}toString(){return lt(this,"WireMessage")}},Ht=Vt,Gt=class{constructor(e,t,s){var n,a;this._annotations=null,k.logAction(e.logger,k.LOG_MINOR,"RestChannel()","started; name = "+t),this.name=t,this.client=e,this.presence=new Mt(this),this.channelOptions=Re(null!=(n=e._Crypto)?n:null,this.logger,s),(null==(a=e.options.plugins)?void 0:a.Push)&&(this._push=new e.options.plugins.Push.PushChannel(this)),e._Annotations&&(this._annotations=new e._Annotations.RestAnnotations(this))}get annotations(){return this._annotations||ge("Annotations"),this._annotations}get push(){return this._push||ge("Push"),this._push}get logger(){return this.client.logger}setOptions(e){var t;this.channelOptions=Re(null!=(t=this.client._Crypto)?t:null,this.logger,e)}async history(e){return k.logAction(this.logger,k.LOG_MICRO,"RestChannel.history()","channel = "+this.name),this.client.rest.channelMixin.history(this,e)}async publish(...e){const t=e[0],s=e[1];let n,a;if("string"==typeof t||null===t)n=[Ht.fromValues({name:t,data:s})],a=e[2];else if(I(t))n=[Ht.fromValues(t)],a=e[1];else{if(!Array.isArray(t))throw new E("The single-argument form of publish() expects a message object or an array of message objects",40013,400);n=Ht.fromValuesArray(t),a=e[1]}a||(a={});const i=this.client,r=i.options,o=r.useBinaryProtocol?"msgpack":"json",l=i.options.idempotentRestPublishing,c=Oe.defaultPostHeaders(i.options,{format:o});if(T(c,r.headers),l&&function(e){return e.every(function(e){return!e.id})}(n)){const e=await te(9);n.forEach(function(t,s){t.id=e+":"+s.toString()})}const u=await xt(n,this.channelOptions),d=Bt(u),h=r.maxMessageSize;if(d>h)throw new E(`Maximum size of messages that can be published at once exceeded (was ${d} bytes; limit is ${h} bytes)`,40009,400);await this._publish(Dt(u,i._MsgPack,o),c,a)}async _publish(e,t,s){await ft.post(this.client,this.client.rest.channelMixin.basePath(this)+"/messages",e,t,s,null,!0)}async status(){return this.client.rest.channelMixin.status(this)}},jt=class e{constructor(e){this.entries=e&&e.entries||void 0,this.schema=e&&e.schema||void 0,this.appId=e&&e.appId||void 0,this.inProgress=e&&e.inProgress||void 0,this.unit=e&&e.unit||void 0,this.intervalId=e&&e.intervalId||void 0}static fromValues(t){return new e(t)}},Ft=class{static basePath(e){return"/channels/"+encodeURIComponent(e.name)}static history(e,t){const s=e.client,n=s.options.useBinaryProtocol?"msgpack":"json",a=e.client.http.supportsLinkHeaders?void 0:n,i=Oe.defaultGetHeaders(s.options,{format:n});return T(i,s.options.headers),new yt(s,this.basePath(e)+"/messages",i,a,async function(t,a,i){return Ut(i?t:ae(t,s._MsgPack,n),e)}).get(t)}static async status(e){const t=e.client.options.useBinaryProtocol?"msgpack":"json",s=Oe.defaultPostHeaders(e.client.options,{format:t});return(await ft.get(e.client,this.basePath(e),s,{},t,!0)).body}},$t=class{static basePath(e){return Ft.basePath(e.channel)+"/presence"}static async history(e,t){const s=e.channel.client,n=s.options.useBinaryProtocol?"msgpack":"json",a=e.channel.client.http.supportsLinkHeaders?void 0:n,i=Oe.defaultGetHeaders(s.options,{format:n});return T(i,s.options.headers),new yt(s,this.basePath(e)+"/history",i,a,async(t,a,i)=>Tt(i?t:ae(t,s._MsgPack,n),e.channel)).get(t)}},Wt=class{constructor(e){this.channelMixin=Ft,this.presenceMixin=$t,this.Resource=ft,this.PaginatedResource=yt,this.DeviceDetails=ht,this.PushChannelSubscription=wt,this.client=e,this.channels=new zt(this.client),this.push=new St(this.client)}async stats(e){const t=Oe.defaultGetHeaders(this.client.options),s=this.client.options.useBinaryProtocol?"msgpack":"json",n=this.client.http.supportsLinkHeaders?void 0:s;return T(t,this.client.options.headers),new yt(this.client,"/stats",t,n,function(e,t,s){const n=s?e:JSON.parse(e);for(let a=0;a<n.length;a++)n[a]=jt.fromValues(n[a]);return n}).get(e)}async time(e){const t=Oe.defaultGetHeaders(this.client.options);this.client.options.headers&&T(t,this.client.options.headers);let{error:s,body:n,unpacked:a}=await this.client.http.do(Le.Get,e=>this.client.baseUri(e)+"/time",t,null,e);if(s)throw s;a||(n=JSON.parse(n));const i=n[0];if(!i)throw new E("Internal error (unexpected result type from GET /time)",5e4,500);return this.client.serverTimeOffset=i-Date.now(),i}async request(e,t,s,n,a,i){var r;const[o,l,c]=(()=>this.client.options.useBinaryProtocol?(this.client._MsgPack||ge("MsgPack"),[this.client._MsgPack.encode,this.client._MsgPack.decode,"msgpack"]):[JSON.stringify,JSON.parse,"json"])(),u=this.client.http.supportsLinkHeaders?void 0:c;n=n||{};const d=e.toLowerCase(),h="get"==d?Oe.defaultGetHeaders(this.client.options,{format:c,protocolVersion:s}):Oe.defaultPostHeaders(this.client.options,{format:c,protocolVersion:s});"string"!=typeof a&&(a=null!=(r=o(a))?r:null),T(h,this.client.options.headers),i&&T(h,i);const p=new yt(this.client,t,h,u,async function(e,t,s){return O(s?e:l(e))},!0);if(!g.Http.methods.includes(d))throw new E("Unsupported method "+d,40500,405);return g.Http.methodsWithBody.includes(d)?p[d](n,a):p[d](n)}async batchPublish(e){let t,s;Array.isArray(e)?(t=e,s=!1):(t=[e],s=!0);const n=this.client.options.useBinaryProtocol?"msgpack":"json",a=Oe.defaultPostHeaders(this.client.options,{format:n});this.client.options.headers&&T(a,this.client.options.headers);const i=ie(t,this.client._MsgPack,n),r=await ft.post(this.client,"/messages",i,a,{},null,!0),o=r.unpacked?r.body:ae(r.body,this.client._MsgPack,n);return s?o[0]:o}async batchPresence(e){const t=this.client.options.useBinaryProtocol?"msgpack":"json",s=Oe.defaultPostHeaders(this.client.options,{format:t});this.client.options.headers&&T(s,this.client.options.headers);const n=e.join(","),a=await ft.get(this.client,"/presence",s,{channels:n},null,!0);return a.unpacked?a.body:ae(a.body,this.client._MsgPack,t)}async revokeTokens(e,t){if(Ve(this.client.options))throw new E("Cannot revoke tokens when using token auth",40162,401);const s=this.client.options.keyName;let n=null!=t?t:{};const a=p({targets:e.map(e=>`${e.type}:${e.value}`)},n),i=this.client.options.useBinaryProtocol?"msgpack":"json",r=Oe.defaultPostHeaders(this.client.options,{format:i});this.client.options.headers&&T(r,this.client.options.headers);const o=ie(a,this.client._MsgPack,i),l=await ft.post(this.client,`/keys/${s}/revokeTokens`,o,r,{},null,!0);return l.unpacked?l.body:ae(l.body,this.client._MsgPack,i)}},zt=class{constructor(e){this.client=e,this.all=Object.create(null)}get(e,t){e=String(e);let s=this.all[e];return s?t&&s.setOptions(t):this.all[e]=s=new Gt(this.client,e,t),s}release(e){delete this.all[String(e)]}},Jt=class extends dt{constructor(e){super(Oe.objectifyOptions(e,!1,"BaseRest",k.defaultLogger,{Rest:Wt}))}},Kt={Rest:Wt},Yt=class extends Ht{static async fromEncoded(e,t){return Nt(k.defaultLogger,g.Crypto,e,t)}static async fromEncodedArray(e,t){return async function(e,t,s,n){return Promise.all(s.map(function(s){return Nt(e,t,s,n)}))}(k.defaultLogger,g.Crypto,e,t)}static fromValues(e){return Ht.fromValues(e)}},Qt=class extends It{static async fromEncoded(e,t){return Rt(k.defaultLogger,g.Crypto,e,t)}static async fromEncodedArray(e,t){return async function(e,t,s,n){return Promise.all(s.map(function(s){return Rt(e,t,s,n)}))}(k.defaultLogger,g.Crypto,e,t)}static fromValues(e){return It.fromValues(e)}},Xt=["annotation.create","annotation.delete"];async function Zt(e,t,s){return ss.fromValues(t).decode(s||{},e)}async function es(e,t){return Promise.all(e.map(function(e){return async function(e,t){return ss.fromValues(e).decode(t.channelOptions,t.logger)}(e,t)}))}var ts=class e extends ct{async encode(){return et(Object.assign(new ss,this,{action:Xt.indexOf(this.action||"annotation.create")}),{})}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}toString(){return lt(this,"Annotation")}},ss=class e extends ct{toJSON(...e){return at.call(this,...e)}static fromValues(t){return Object.assign(new e,t)}static fromValuesArray(t){return t.map(t=>e.fromValues(t))}async decode(e,t){const s=Object.assign(new ts,f(p({},this),{action:Xt[this.action]}));try{await st(s,e)}catch(n){k.logAction(t,k.LOG_ERROR,"WireAnnotation.decode()",Q(n))}return s}toString(){return lt(this,"WireAnnotation")}},ns=ts,as=class extends ns{static async fromEncoded(e,t){return Zt(k.defaultLogger,e,t)}static async fromEncodedArray(e,t){return async function(e,t,s){return Promise.all(t.map(function(t){return Zt(e,t,s)}))}(k.defaultLogger,e,t)}static fromValues(e){return ns.fromValues(e)}};function is(e){let t;switch(typeof e){case"string":t=e;break;case"object":t=e.serial}if(!t||"string"!=typeof t)throw new E("First argument of annotations.publish() must be either a Message (or at least an object with a string `serial` property) or a message serial (string)",40003,400);return t}function rs(e,t){const s=is(e);if(!t||"object"!=typeof t)throw new E("Second argument of annotations.publish() must be an object (the intended annotation to publish)",40003,400);const n=ns.fromValues(t);return n.messageSerial=s,n.action||(n.action="annotation.create"),n}function os(e,t){return e.client.rest.channelMixin.basePath(e)+"/messages/"+encodeURIComponent(t)+"/annotations"}var ls=class{constructor(e){this.channel=e}async publish(e,t){const s=rs(e,t),n=await s.encode(),a=this.channel.client,i=a.options.useBinaryProtocol?"msgpack":"json",r=Oe.defaultPostHeaders(a.options,{format:i});T(r,a.options.headers);const o=ie([n],a._MsgPack,i);await ft.post(a,os(this.channel,s.messageSerial),o,r,{},null,!0)}async delete(e,t){return t.action="annotation.delete",this.publish(e,t)}async get(e,t){const s=this.channel.client,n=is(e),a=s.options.useBinaryProtocol?"msgpack":"json",i=s.http.supportsLinkHeaders?void 0:a,r=Oe.defaultGetHeaders(s.options,{format:a});return T(r,s.options.headers),new yt(s,os(this.channel,n),r,i,async(e,t,n)=>es(n?e:ae(e,s._MsgPack,a),this.channel)).get(t)}},cs=ie;function us(e){const t=[];if(e)for(let s=0;s<e.length;s++)t.push(e[s].toString());return"[ "+t.join(", ")+" ]"}function ds(e,t,s,n){let a,i,r,o,l;return e.error&&(a=E.fromValues(e.error)),e.messages&&(i=qt.fromValuesArray(e.messages)),t&&e.presence&&(r=t.WirePresenceMessage.fromValuesArray(e.presence)),s&&e.annotations&&(o=s.WireAnnotation.fromValuesArray(e.annotations)),n&&e.state&&(l=n.ObjectMessage.fromValuesArray(e.state,C,rt)),Object.assign(new vs,f(p({},e),{presence:r,messages:i,annotations:o,state:l,error:a}))}function hs(e){return t=>{var s;return ds(t,{WirePresenceMessage:Ot},{WireAnnotation:ss},null!=(s=null==e?void 0:e.ObjectsPlugin)?s:null)}}function ps(e){return Object.assign(new vs,e)}function fs(e,t,s,n){let a="[ProtocolMessage";void 0!==e.action&&(a+="; action="+Je[e.action]||e.action);const i=["id","channel","channelSerial","connectionId","count","msgSerial","timestamp"];let r;for(let o=0;o<i.length;o++)r=i[o],void 0!==e[r]&&(a+="; "+r+"="+e[r]);if(e.messages&&(a+="; messages="+us(qt.fromValuesArray(e.messages))),e.presence&&t&&(a+="; presence="+us(t.WirePresenceMessage.fromValuesArray(e.presence))),e.annotations&&s&&(a+="; annotations="+us(s.WireAnnotation.fromValuesArray(e.annotations))),e.state&&n&&(a+="; state="+us(n.ObjectMessage.fromValuesArray(e.state,C,rt))),e.error&&(a+="; error="+E.fromValues(e.error).toString()),e.auth&&e.auth.accessToken&&(a+="; token="+e.auth.accessToken),e.flags&&(a+="; flags="+Ye.filter(e.hasFlag).join(",")),e.params){let t="";F(e.params,function(s){t.length>0&&(t+="; "),t+=s+"="+e.params[s]}),t.length>0&&(a+="; params=["+t+"]")}return a+="]",a}var vs=class{constructor(){this.hasFlag=e=>(this.flags&Ke[e])>0}setFlag(e){return this.flags=this.flags|Ke[e]}getMode(){return(this.flags||0)&Ke.MODE_ALL}encodeModesToFlags(e){e.forEach(e=>this.setFlag(e))}decodeModesFromFlags(){const e=[];return Qe.forEach(t=>{this.hasFlag(t)&&e.push(t)}),e.length>0?e:void 0}},ms=vs,gs=class{constructor(e,t,s,n,a){this.previous=e,this.current=t,"attached"===t&&(this.resumed=s,this.hasBacklog=n),a&&(this.reason=a)}},ys=function(){};function bs(e){const t=e||{},{agent:s}=t;return((e,t)=>{var s={};for(var n in e)u.call(e,n)&&t.indexOf(n)<0&&(s[n]=e[n]);if(null!=e&&c)for(var n of c(e))t.indexOf(n)<0&&d.call(e,n)&&(s[n]=e[n]);return s})(t,["agent"])}var ws=class e extends We{constructor(e,t,s){var n,a,i;super(e.logger),this._annotations=null,this._mode=0,this.retryCount=0,this.history=async function(e){k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.history()","channel = "+this.name);const t=this.client.rest.channelMixin;if(e&&e.untilAttach){if("attached"!==this.state)throw new E("option untilAttach requires the channel to be attached",4e4,400);if(!this.properties.attachSerial)throw new E("untilAttach was specified and channel is attached, but attachSerial is not defined",4e4,400);delete e.untilAttach,e.from_serial=this.properties.attachSerial}return t.history(this,e)},this.whenState=e=>We.prototype.whenState.call(this,e,this.state),k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel()","started; name = "+t),this.name=t,this.channelOptions=Re(null!=(n=e._Crypto)?n:null,this.logger,s),this.client=e,this._presence=e._RealtimePresence?new e._RealtimePresence.RealtimePresence(this):null,e._Annotations&&(this._annotations=new e._Annotations.RealtimeAnnotations(this)),this.connectionManager=e.connection.connectionManager,this.state="initialized",this.subscriptions=new We(this.logger),this.syncChannelSerial=void 0,this.properties={attachSerial:void 0,channelSerial:void 0},this.setOptions(s),this.errorReason=null,this._attachResume=!1,this._decodingContext={channelOptions:this.channelOptions,plugins:e.options.plugins||{},baseEncodedPreviousPayload:void 0},this._lastPayload={messageId:null,protocolMessageChannelSerial:null,decodeFailureRecoveryInProgress:null},this._allChannelChanges=new We(this.logger),(null==(a=e.options.plugins)?void 0:a.Push)&&(this._push=new e.options.plugins.Push.PushChannel(this)),(null==(i=e.options.plugins)?void 0:i.Objects)&&(this._objects=new e.options.plugins.Objects.Objects(this))}get presence(){return this._presence||ge("RealtimePresence"),this._presence}get annotations(){return this._annotations||ge("Annotations"),this._annotations}get push(){return this._push||ge("Push"),this._push}get objects(){return this._objects||ge("Objects"),this._objects}invalidStateError(){return new E("Channel operation failed as channel state is "+this.state,90001,400,this.errorReason||void 0)}static processListenerArgs(e){return"function"==typeof(e=Array.prototype.slice.call(e))[0]&&e.unshift(null),e}async setOptions(e){var t;const s=this.channelOptions,n=function(e){if(e&&"params"in e&&!I(e.params))return new E("options.params must be an object",4e4,400);if(e&&"modes"in e){if(!Array.isArray(e.modes))return new E("options.modes must be an array",4e4,400);for(let t=0;t<e.modes.length;t++){const s=e.modes[t];if(!s||"string"!=typeof s||!Qe.includes(String.prototype.toUpperCase.call(s)))return new E("Invalid channel mode: "+s,4e4,400)}}}(e);if(n)throw n;if(this.channelOptions=Re(null!=(t=this.client._Crypto)?t:null,this.logger,e),this._decodingContext&&(this._decodingContext.channelOptions=this.channelOptions),this._shouldReattachToSetOptions(e,s))return this.attachImpl(),new Promise((e,t)=>{this._allChannelChanges.once(["attached","update","detached","failed"],function(s){switch(this.event){case"update":case"attached":e();break;default:t(s.reason)}})})}_shouldReattachToSetOptions(e,t){if("attached"!==this.state&&"attaching"!==this.state)return!1;if(null==e?void 0:e.params){const s=bs(e.params),n=bs(t.params);if(Object.keys(s).length!==Object.keys(n).length)return!0;if(!he(n,s))return!0}return!(!(null==e?void 0:e.modes)||t.modes&&ve(e.modes,t.modes))}async publish(...e){let t;if(1==e.length)if(I(e[0]))t=[Ht.fromValues(e[0])];else{if(!Array.isArray(e[0]))throw new E("The single-argument form of publish() expects a message object or an array of message objects",40013,400);t=Ht.fromValuesArray(e[0])}else t=[Ht.fromValues({name:e[0],data:e[1]})];const s=this.client.options.maxMessageSize,n=await xt(t,this.channelOptions),a=Bt(n);if(a>s)throw new E(`Maximum size of messages that can be published at once exceeded (was ${a} bytes; limit is ${s} bytes)`,40009,400);this.throwIfUnpublishableState(),k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.publish()","sending message; channel state is "+this.state+", message count = "+n.length);const i=ps({action:ze.MESSAGE,channel:this.name,messages:n});return this.sendMessage(i)}throwIfUnpublishableState(){if(!this.connectionManager.activeState())throw this.connectionManager.getError();if("failed"===this.state||"suspended"===this.state)throw this.invalidStateError()}onEvent(e){k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.onEvent()","received message");const t=this.subscriptions;for(let s=0;s<e.length;s++){const n=e[s];t.emit(n.name,n)}}async attach(){return"attached"===this.state?null:new Promise((e,t)=>{this._attach(!1,null,(s,n)=>s?t(s):e(n))})}_attach(e,t,s){s||(s=e=>{e&&k.logAction(this.logger,k.LOG_ERROR,"RealtimeChannel._attach()","Channel attach failed: "+e.toString())});const n=this.connectionManager;n.activeState()?(("attaching"!==this.state||e)&&this.requestState("attaching",t),this.once(function(e){switch(this.event){case"attached":null==s||s(null,e);break;case"detached":case"suspended":case"failed":null==s||s(e.reason||n.getError()||new E("Unable to attach; reason unknown; state = "+this.event,9e4,500));break;case"detaching":null==s||s(new E("Attach request superseded by a subsequent detach request",9e4,409))}})):s(n.getError())}attachImpl(){k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.attachImpl()","sending ATTACH message");const e=ps({action:ze.ATTACH,channel:this.name,params:this.channelOptions.params,channelSerial:this.properties.channelSerial});this.channelOptions.modes&&e.encodeModesToFlags(oe(this.channelOptions.modes)),this._attachResume&&e.setFlag("ATTACH_RESUME"),this._lastPayload.decodeFailureRecoveryInProgress&&(e.channelSerial=this._lastPayload.protocolMessageChannelSerial),this.sendMessage(e).catch(ys)}async detach(){const e=this.connectionManager;if(!e.activeState())throw e.getError();switch(this.state){case"suspended":return void this.notifyState("detached");case"detached":return;case"failed":throw new E("Unable to detach; channel state = failed",90001,400);default:this.requestState("detaching");case"detaching":return new Promise((t,s)=>{this.once(function(n){switch(this.event){case"detached":t();break;case"attached":case"suspended":case"failed":s(n.reason||e.getError()||new E("Unable to detach; reason unknown; state = "+this.event,9e4,500));break;case"attaching":s(new E("Detach request superseded by a subsequent attach request",9e4,409))}})})}}detachImpl(){k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.detach()","sending DETACH message");const e=ps({action:ze.DETACH,channel:this.name});this.sendMessage(e).catch(ys)}async subscribe(...t){const[s,n]=e.processListenerArgs(t);if("failed"===this.state)throw E.fromValues(this.invalidStateError());return s&&"object"==typeof s&&!Array.isArray(s)?this.client._FilteredSubscriptions.subscribeFilter(this,s,n):this.subscriptions.on(s,n),!1!==this.channelOptions.attachOnSubscribe?this.attach():null}unsubscribe(...t){var s;const[n,a]=e.processListenerArgs(t);"object"==typeof n&&!a||(null==(s=this.filteredSubscriptions)?void 0:s.has(a))?this.client._FilteredSubscriptions.getAndDeleteFilteredSubscriptions(this,n,a).forEach(e=>this.subscriptions.off(e)):this.subscriptions.off(n,a)}sync(){switch(this.state){case"initialized":case"detaching":case"detached":throw new R("Unable to sync to channel; not attached",4e4)}const e=this.connectionManager;if(!e.activeState())throw e.getError();const t=ps({action:ze.SYNC,channel:this.name});this.syncChannelSerial&&(t.channelSerial=this.syncChannelSerial),e.send(t)}async sendMessage(e){return new Promise((t,s)=>{this.connectionManager.send(e,this.client.options.queueMessages,e=>{e?s(e):t()})})}async sendPresence(e){const t=ps({action:ze.PRESENCE,channel:this.name,presence:e});return this.sendMessage(t)}sendState(e){const t=ps({action:ze.OBJECT,channel:this.name,state:e});return this.sendMessage(t)}async processMessage(e){e.action!==ze.ATTACHED&&e.action!==ze.MESSAGE&&e.action!==ze.PRESENCE&&e.action!==ze.OBJECT&&e.action!==ze.ANNOTATION||this.setChannelSerial(e.channelSerial);let t,s=!1;switch(e.action){case ze.ATTACHED:{this.properties.attachSerial=e.channelSerial,this._mode=e.getMode(),this.params=e.params||{};const t=e.decodeModesFromFlags();this.modes=t&&re(t)||void 0;const s=e.hasFlag("RESUMED"),n=e.hasFlag("HAS_PRESENCE"),a=e.hasFlag("HAS_BACKLOG"),i=e.hasFlag("HAS_OBJECTS");if("attached"===this.state){s||(this._presence&&this._presence.onAttached(n),this._objects&&this._objects.onAttached(i));const t=new gs(this.state,this.state,s,a,e.error);this._allChannelChanges.emit("update",t),s&&!this.channelOptions.updateOnAttached||this.emit("update",t)}else"detaching"===this.state?this.checkPendingState():this.notifyState("attached",e.error,s,n,a,i);break}case ze.DETACHED:{const t=e.error?E.fromValues(e.error):new E("Channel detached",90001,404);"detaching"===this.state?this.notifyState("detached",t):"attaching"===this.state?this.notifyState("suspended",t):"attached"!==this.state&&"suspended"!==this.state||this.requestState("attaching",t);break}case ze.SYNC:if(s=!0,t=this.syncChannelSerial=e.channelSerial,!e.presence)break;case ze.PRESENCE:{if(!e.presence)break;ot(e);const n=this.channelOptions;if(this._presence){const a=await Promise.all(e.presence.map(e=>e.decode(n,this.logger)));this._presence.setPresence(a,s,t)}break}case ze.OBJECT:case ze.OBJECT_SYNC:{if(!this._objects||!e.state)return;ot(e);const t=e.state,s=this.client.connection.connectionManager.getActiveTransportFormat();await Promise.all(t.map(e=>this.client._objectsPlugin.ObjectMessage.decode(e,this.client,this.logger,k,C,s))),e.action===ze.OBJECT?this._objects.handleObjectMessages(t):this._objects.handleObjectSyncMessages(t,e.channelSerial);break}case ze.MESSAGE:{if("attached"!==this.state)return void k.logAction(this.logger,k.LOG_MAJOR,"RealtimeChannel.processMessage()",'Message "'+e.id+'" skipped as this channel "'+this.name+'" state is not "attached" (state is "'+this.state+'").');ot(e);const t=e.messages,s=t[0],n=t[t.length-1];if(s.extras&&s.extras.delta&&s.extras.delta.from!==this._lastPayload.messageId){const t='Delta message decode failure - previous message not available for message "'+e.id+'" on this channel "'+this.name+'".';k.logAction(this.logger,k.LOG_ERROR,"RealtimeChannel.processMessage()",t),this._startDecodeFailureRecovery(new E(t,40018,400));break}let a=[];for(let e=0;e<t.length;e++){const{decoded:s,err:n}=await t[e].decodeWithErr(this._decodingContext,this.logger);if(a[e]=s,n)switch(n.code){case 40018:return void this._startDecodeFailureRecovery(n);case 40019:case 40021:return void this.notifyState("failed",n)}}this._lastPayload.messageId=n.id,this._lastPayload.protocolMessageChannelSerial=e.channelSerial,this.onEvent(a);break}case ze.ANNOTATION:{ot(e);const t=this.channelOptions;if(this._annotations){const s=await Promise.all((e.annotations||[]).map(e=>e.decode(t,this.logger)));this._annotations._processIncoming(s)}break}case ze.ERROR:{const t=e.error;t&&80016==t.code?this.checkPendingState():this.notifyState("failed",E.fromValues(t));break}default:k.logAction(this.logger,k.LOG_MAJOR,"RealtimeChannel.processMessage()","Protocol error: unrecognised message action ("+e.action+")")}}_startDecodeFailureRecovery(e){this._lastPayload.decodeFailureRecoveryInProgress||(k.logAction(this.logger,k.LOG_MAJOR,"RealtimeChannel.processMessage()","Starting decode failure recovery process."),this._lastPayload.decodeFailureRecoveryInProgress=!0,this._attach(!0,e,()=>{this._lastPayload.decodeFailureRecoveryInProgress=!1}))}onAttached(){k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel.onAttached","activating channel; name = "+this.name)}notifyState(e,t,s,n,a,i){if(k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.notifyState","name = "+this.name+", current state = "+this.state+", notifying state "+e),this.clearStateTimer(),["detached","suspended","failed"].includes(e)&&(this.properties.channelSerial=null),e===this.state)return;this._presence&&this._presence.actOnChannelState(e,n,t),this._objects&&this._objects.actOnChannelState(e,i),"suspended"===e&&this.connectionManager.state.sendEvents?this.startRetryTimer():this.cancelRetryTimer(),t&&(this.errorReason=t);const r=new gs(this.state,e,s,a,t),o='Channel state for channel "'+this.name+'"',l=e+(t?"; reason: "+t:"");"failed"===e?k.logAction(this.logger,k.LOG_ERROR,o,l):k.logAction(this.logger,k.LOG_MAJOR,o,l),"attaching"!==e&&"suspended"!==e&&(this.retryCount=0),"attached"===e&&this.onAttached(),"attached"===e?this._attachResume=!0:"detaching"!==e&&"failed"!==e||(this._attachResume=!1),this.state=e,this._allChannelChanges.emit(e,r),this.emit(e,r)}requestState(e,t){k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel.requestState","name = "+this.name+", state = "+e),this.notifyState(e,t),this.checkPendingState()}checkPendingState(){if(this.connectionManager.state.sendEvents)switch(k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel.checkPendingState","name = "+this.name+", state = "+this.state),this.state){case"attaching":this.startStateTimerIfNotRunning(),this.attachImpl();break;case"detaching":this.startStateTimerIfNotRunning(),this.detachImpl();break;case"attached":this.sync()}else k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel.checkPendingState","sendEvents is false; state is "+this.connectionManager.state.state)}timeoutPendingState(){switch(this.state){case"attaching":{const e=new E("Channel attach timed out",90007,408);this.notifyState("suspended",e);break}case"detaching":{const e=new E("Channel detach timed out",90007,408);this.notifyState("attached",e);break}default:this.checkPendingState()}}startStateTimerIfNotRunning(){this.stateTimer||(this.stateTimer=setTimeout(()=>{k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel.startStateTimerIfNotRunning","timer expired"),this.stateTimer=null,this.timeoutPendingState()},this.client.options.timeouts.realtimeRequestTimeout))}clearStateTimer(){const e=this.stateTimer;e&&(clearTimeout(e),this.stateTimer=null)}startRetryTimer(){if(this.retryTimer)return;this.retryCount++;const e=ue(this.client.options.timeouts.channelRetryTimeout,this.retryCount);this.retryTimer=setTimeout(()=>{"suspended"===this.state&&this.connectionManager.state.sendEvents&&(this.retryTimer=null,k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel retry timer expired","attempting a new attach"),this.requestState("attaching"))},e)}cancelRetryTimer(){this.retryTimer&&(clearTimeout(this.retryTimer),this.retryTimer=null)}getReleaseErr(){const e=this.state;return"initialized"===e||"detached"===e||"failed"===e?null:new E("Can only release a channel in a state where there is no possibility of further updates from the server being received (initialized, detached, or failed); was "+e,90001,400)}setChannelSerial(e){k.logAction(this.logger,k.LOG_MICRO,"RealtimeChannel.setChannelSerial()","Updating channel serial; serial = "+e+"; previous = "+this.properties.channelSerial),e&&(this.properties.channelSerial=e)}async status(){return this.client.rest.channelMixin.status(this)}},_s=class{constructor(e){this.channel=e,this.logger=e.logger,this.subscriptions=new We(this.logger)}async publish(e,t){const s=this.channel.name,n=rs(e,t),a=await n.encode();this.channel.throwIfUnpublishableState(),k.logAction(this.logger,k.LOG_MICRO,"RealtimeAnnotations.publish()","channelName = "+s+", sending annotation with messageSerial = "+n.messageSerial+", type = "+n.type);const i=ps({action:ze.ANNOTATION,channel:s,annotations:[a]});return this.channel.sendMessage(i)}async delete(e,t){return t.action="annotation.delete",this.publish(e,t)}async subscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1],a=this.channel;if("failed"===a.state)throw E.fromValues(a.invalidStateError());if(this.subscriptions.on(s,n),!1!==this.channel.channelOptions.attachOnSubscribe&&await a.attach(),0===("attached"===this.channel.state&&this.channel._mode&Ke.ANNOTATION_SUBSCRIBE))throw new E("You are trying to add an annotation listener, but you haven't requested the annotation_subscribe channel mode in ChannelOptions, so this won't do anything (we only deliver annotations to clients who have explicitly requested them)",93001,400)}unsubscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1];this.subscriptions.off(s,n)}_processIncoming(e){for(const t of e)this.subscriptions.emit(t.type||"",t)}async get(e,t){return ls.prototype.get.call(this,e,t)}},ks=class e extends Jt{constructor(t){var s,n;if(!e._MsgPack)throw new Error("Expected DefaultRest._MsgPack to have been set");super(Oe.objectifyOptions(t,!0,"Rest",k.defaultLogger,f(p({},Kt),{Crypto:null!=(s=e.Crypto)?s:void 0,MsgPack:null!=(n=e._MsgPack)?n:void 0,Annotations:{Annotation:ns,WireAnnotation:ss,RealtimeAnnotations:_s,RestAnnotations:ls}})))}static get Crypto(){if(null===this._Crypto)throw new Error("Encryption not enabled; use ably.encryption.js instead");return this._Crypto}static set Crypto(e){this._Crypto=e}};ks._Crypto=null,ks.Message=Yt,ks.PresenceMessage=Qt,ks.Annotation=as,ks._MsgPack=null,ks._Http=Fe;var Cs,Ss,Es=ks,Rs=class extends We{constructor(e){super(e),this.messages=[]}count(){return this.messages.length}push(e){this.messages.push(e)}shift(){return this.messages.shift()}last(){return this.messages[this.messages.length-1]}copyAll(){return this.messages.slice()}append(e){this.messages.push.apply(this.messages,e)}prepend(e){this.messages.unshift.apply(this.messages,e)}completeMessages(e,t,s){k.logAction(this.logger,k.LOG_MICRO,"MessageQueue.completeMessages()","serial = "+e+"; count = "+t),s=s||null;const n=this.messages;if(0===n.length)throw new Error("MessageQueue.completeMessages(): completeMessages called on any empty MessageQueue");const a=n[0];if(a){const i=a.message.msgSerial,r=e+t;if(r>i){const e=n.splice(0,r-i);for(const t of e)t.callback(s)}0==n.length&&this.emit("idle")}}completeAllMessages(e){this.completeMessages(0,Number.MAX_SAFE_INTEGER||Number.MAX_VALUE,e)}resetSendAttempted(){for(let e of this.messages)e.sendAttempted=!1}clear(){k.logAction(this.logger,k.LOG_MICRO,"MessageQueue.clear()","clearing "+this.messages.length+" messages"),this.messages=[],this.emit("idle")}},Ts=class{constructor(e,t){this.message=e,this.callback=t,this.merged=!1;const s=e.action;this.sendAttempted=!1,this.ackRequired="number"==typeof s&&[ze.MESSAGE,ze.PRESENCE,ze.ANNOTATION,ze.OBJECT].includes(s)}},As=class extends We{constructor(e){super(e.logger),this.transport=e,this.messageQueue=new Rs(this.logger),e.on("ack",(e,t)=>{this.onAck(e,t)}),e.on("nack",(e,t,s)=>{this.onNack(e,t,s)})}onAck(e,t){k.logAction(this.logger,k.LOG_MICRO,"Protocol.onAck()","serial = "+e+"; count = "+t),this.messageQueue.completeMessages(e,t)}onNack(e,t,s){k.logAction(this.logger,k.LOG_ERROR,"Protocol.onNack()","serial = "+e+"; count = "+t+"; err = "+Q(s)),s||(s=new E("Unable to send message; channel not responding",50001,500)),this.messageQueue.completeMessages(e,t,s)}onceIdle(e){const t=this.messageQueue;0!==t.count()?t.once("idle",e):e()}send(e){e.ackRequired&&this.messageQueue.push(e),this.logger.shouldLog(k.LOG_MICRO)&&k.logActionNoStrip(this.logger,k.LOG_MICRO,"Protocol.send()","sending msg; "+fs(e.message,this.transport.connectionManager.realtime._RealtimePresence,this.transport.connectionManager.realtime._Annotations,this.transport.connectionManager.realtime._objectsPlugin)),e.sendAttempted=!0,this.transport.send(e.message)}getTransport(){return this.transport}getPendingMessages(){return this.messageQueue.copyAll()}clearPendingMessages(){return this.messageQueue.clear()}finish(){const e=this.transport;this.onceIdle(function(){e.disconnect()})}},Os=class{constructor(e,t,s,n){this.previous=e,this.current=t,s&&(this.retryIn=s),n&&(this.reason=n)}},Is={DISCONNECTED:80003,SUSPENDED:80002,FAILED:8e4,CLOSING:80017,CLOSED:80017,UNKNOWN_CONNECTION_ERR:50002,UNKNOWN_CHANNEL_ERR:50001},Ms={disconnected:()=>E.fromValues({statusCode:400,code:Is.DISCONNECTED,message:"Connection to server temporarily unavailable"}),suspended:()=>E.fromValues({statusCode:400,code:Is.SUSPENDED,message:"Connection to server unavailable"}),failed:()=>E.fromValues({statusCode:400,code:Is.FAILED,message:"Connection failed or disconnected by server"}),closing:()=>E.fromValues({statusCode:400,code:Is.CLOSING,message:"Connection closing"}),closed:()=>E.fromValues({statusCode:400,code:Is.CLOSED,message:"Connection closed"}),unknownConnectionErr:()=>E.fromValues({statusCode:500,code:Is.UNKNOWN_CONNECTION_ERR,message:"Internal connection error"}),unknownChannelErr:()=>E.fromValues({statusCode:500,code:Is.UNKNOWN_CONNECTION_ERR,message:"Internal channel error"})},Ls=ps({action:ze.CLOSE}),Ps=ps({action:ze.DISCONNECT}),Ns=class extends We{constructor(e,t,s,n){super(e.logger),n&&(s.format=void 0,s.heartbeats=!0),this.connectionManager=e,this.auth=t,this.params=s,this.timeouts=s.options.timeouts,this.format=s.format,this.isConnected=!1,this.isFinished=!1,this.isDisposed=!1,this.maxIdleInterval=null,this.idleTimer=null,this.lastActivity=null}connect(){}close(){this.isConnected&&this.requestClose(),this.finish("closed",Ms.closed())}disconnect(e){this.isConnected&&this.requestDisconnect(),this.finish("disconnected",e||Ms.disconnected())}fail(e){this.isConnected&&this.requestDisconnect(),this.finish("failed",e||Ms.failed())}finish(e,t){var s;this.isFinished||(this.isFinished=!0,this.isConnected=!1,this.maxIdleInterval=null,clearTimeout(null!=(s=this.idleTimer)?s:void 0),this.idleTimer=null,this.emit(e,t),this.dispose())}onProtocolMessage(e){switch(this.logger.shouldLog(k.LOG_MICRO)&&k.logActionNoStrip(this.logger,k.LOG_MICRO,"Transport.onProtocolMessage()","received on "+this.shortName+": "+fs(e,this.connectionManager.realtime._RealtimePresence,this.connectionManager.realtime._Annotations,this.connectionManager.realtime._objectsPlugin)+"; connectionId = "+this.connectionManager.connectionId),this.onActivity(),e.action){case ze.HEARTBEAT:k.logActionNoStrip(this.logger,k.LOG_MICRO,"Transport.onProtocolMessage()",this.shortName+" heartbeat; connectionId = "+this.connectionManager.connectionId),this.emit("heartbeat",e.id);break;case ze.CONNECTED:this.onConnect(e),this.emit("connected",e.error,e.connectionId,e.connectionDetails,e);break;case ze.CLOSED:this.onClose(e);break;case ze.DISCONNECTED:this.onDisconnect(e);break;case ze.ACK:this.emit("ack",e.msgSerial,e.count);break;case ze.NACK:this.emit("nack",e.msgSerial,e.count,e.error);break;case ze.SYNC:this.connectionManager.onChannelMessage(e,this);break;case ze.ACTIVATE:break;case ze.AUTH:ne(this.auth.authorize(),e=>{e&&k.logAction(this.logger,k.LOG_ERROR,"Transport.onProtocolMessage()","Ably requested re-authentication, but unable to obtain a new token: "+Q(e))});break;case ze.ERROR:if(k.logAction(this.logger,k.LOG_MINOR,"Transport.onProtocolMessage()","received error action; connectionId = "+this.connectionManager.connectionId+"; err = "+g.Config.inspect(e.error)+(e.channel?", channel: "+e.channel:"")),void 0===e.channel){this.onFatalError(e);break}this.connectionManager.onChannelMessage(e,this);break;default:this.connectionManager.onChannelMessage(e,this)}}onConnect(e){if(this.isConnected=!0,!e.connectionDetails)throw new Error("Transport.onConnect(): Connect message recieved without connectionDetails");const t=e.connectionDetails.maxIdleInterval;t&&(this.maxIdleInterval=t+this.timeouts.realtimeRequestTimeout,this.onActivity())}onDisconnect(e){const t=e&&e.error;k.logAction(this.logger,k.LOG_MINOR,"Transport.onDisconnect()","err = "+Q(t)),this.finish("disconnected",t)}onFatalError(e){const t=e&&e.error;k.logAction(this.logger,k.LOG_MINOR,"Transport.onFatalError()","err = "+Q(t)),this.finish("failed",t)}onClose(e){const t=e&&e.error;k.logAction(this.logger,k.LOG_MINOR,"Transport.onClose()","err = "+Q(t)),this.finish("closed",t)}requestClose(){k.logAction(this.logger,k.LOG_MINOR,"Transport.requestClose()",""),this.send(Ls)}requestDisconnect(){k.logAction(this.logger,k.LOG_MINOR,"Transport.requestDisconnect()",""),this.send(Ps)}ping(e){const t={action:ze.HEARTBEAT};e&&(t.id=e),this.send(ps(t))}dispose(){k.logAction(this.logger,k.LOG_MINOR,"Transport.dispose()",""),this.isDisposed=!0,this.off()}onActivity(){this.maxIdleInterval&&(this.lastActivity=this.connectionManager.lastActivity=Date.now(),this.setIdleTimer(this.maxIdleInterval+100))}setIdleTimer(e){this.idleTimer||(this.idleTimer=setTimeout(()=>{this.onIdleTimerExpire()},e))}onIdleTimerExpire(){if(!this.lastActivity||!this.maxIdleInterval)throw new Error("Transport.onIdleTimerExpire(): lastActivity/maxIdleInterval not set");this.idleTimer=null;const e=Date.now()-this.lastActivity,t=this.maxIdleInterval-e;if(t<=0){const t="No activity seen from realtime in "+e+"ms; assuming connection has dropped";k.logAction(this.logger,k.LOG_ERROR,"Transport.onIdleTimerExpire()",t),this.disconnect(new E(t,80003,408))}else this.setIdleTimer(t+100)}static tryConnect(e,t,s,n,a){const i=new e(t,s,n);let r;const o=function(e){clearTimeout(r),a({event:this.event,error:e})},l=t.options.timeouts.realtimeRequestTimeout;return r=setTimeout(()=>{i.off(["preconnect","disconnected","failed"]),i.dispose(),o.call({event:"disconnected"},new E("Timeout waiting for transport to indicate itself viable",5e4,500))},l),i.on(["failed","disconnected"],o),i.on("preconnect",function(){k.logAction(t.logger,k.LOG_MINOR,"Transport.tryConnect()","viable transport "+i),clearTimeout(r),i.off(["failed","disconnected"],o),a(null,i)}),i.connect(),i}static isAvailable(){throw new E("isAvailable not implemented for transport",5e4,500)}};(Ss=Cs||(Cs={})).WebSocket="web_socket",Ss.Comet="comet",Ss.XhrPolling="xhr_polling";var Us=void 0!==s?s:"undefined"!=typeof window?window:self,xs=()=>{var e;return void 0!==g.WebStorage&&(null==(e=g.WebStorage)?void 0:e.localSupported)},Ds=()=>{var e;return void 0!==g.WebStorage&&(null==(e=g.WebStorage)?void 0:e.sessionSupported)},Bs=function(){},Vs="ably-transport-preference";function qs(e){try{return JSON.parse(e)}catch(t){return null}}var Hs=class{constructor(e,t,s,n){this.options=e,this.host=t,this.mode=s,this.connectionKey=n,this.format=e.useBinaryProtocol?"msgpack":"json"}getConnectParams(e){const t=e?A(e):{},s=this.options;switch(this.mode){case"resume":t.resume=this.connectionKey;break;case"recover":{const e=qs(s.recover);e&&(t.recover=e.connectionKey);break}}return void 0!==s.clientId&&(t.clientId=s.clientId),!1===s.echoMessages&&(t.echo="false"),void 0!==this.format&&(t.format=this.format),void 0!==this.stream&&(t.stream=this.stream),void 0!==this.heartbeats&&(t.heartbeats=this.heartbeats),t.v=Oe.protocolVersion,t.agent=Ee(this.options),void 0!==s.transportParams&&T(t,s.transportParams),t}toString(){let e="[mode="+this.mode;return this.host&&(e+=",host="+this.host),this.connectionKey&&(e+=",connectionKey="+this.connectionKey),this.format&&(e+=",format="+this.format),e+="]",e}},Gs=class e extends We{constructor(e,t){super(e.logger),this.supportedTransports={},this.disconnectedRetryCount=0,this.pendingChannelMessagesState={isProcessing:!1,queue:[]},this.realtime=e,this.initTransports(),this.options=t;const s=t.timeouts,n=s.webSocketConnectTimeout+s.realtimeRequestTimeout;if(this.states={initialized:{state:"initialized",terminal:!1,queueEvents:!0,sendEvents:!1,failState:"disconnected"},connecting:{state:"connecting",terminal:!1,queueEvents:!0,sendEvents:!1,retryDelay:n,failState:"disconnected"},connected:{state:"connected",terminal:!1,queueEvents:!1,sendEvents:!0,failState:"disconnected"},disconnected:{state:"disconnected",terminal:!1,queueEvents:!0,sendEvents:!1,retryDelay:s.disconnectedRetryTimeout,failState:"disconnected"},suspended:{state:"suspended",terminal:!1,queueEvents:!1,sendEvents:!1,retryDelay:s.suspendedRetryTimeout,failState:"suspended"},closing:{state:"closing",terminal:!1,queueEvents:!1,sendEvents:!1,retryDelay:s.realtimeRequestTimeout,failState:"closed"},closed:{state:"closed",terminal:!0,queueEvents:!1,sendEvents:!1,failState:"closed"},failed:{state:"failed",terminal:!0,queueEvents:!1,sendEvents:!1,failState:"failed"}},this.state=this.states.initialized,this.errorReason=null,this.queuedMessages=new Rs(this.logger),this.msgSerial=0,this.connectionDetails=void 0,this.connectionId=void 0,this.connectionKey=void 0,this.connectionStateTtl=s.connectionStateTtl,this.maxIdleInterval=null,this.transports=D(t.transports||Oe.defaultTransports,this.supportedTransports),this.transportPreference=null,this.transports.includes(Cs.WebSocket)&&(this.webSocketTransportAvailable=!0),this.transports.includes(Cs.XhrPolling)?this.baseTransport=Cs.XhrPolling:this.transports.includes(Cs.Comet)&&(this.baseTransport=Cs.Comet),this.httpHosts=Oe.getHosts(t),this.wsHosts=Oe.getHosts(t,!0),this.activeProtocol=null,this.host=null,this.lastAutoReconnectAttempt=null,this.lastActivity=null,this.forceFallbackHost=!1,this.connectCounter=0,this.wsCheckResult=null,this.webSocketSlowTimer=null,this.webSocketGiveUpTimer=null,this.abandonedWebSocket=!1,k.logAction(this.logger,k.LOG_MINOR,"Realtime.ConnectionManager()","started"),k.logAction(this.logger,k.LOG_MICRO,"Realtime.ConnectionManager()","requested transports = ["+(t.transports||Oe.defaultTransports)+"]"),k.logAction(this.logger,k.LOG_MICRO,"Realtime.ConnectionManager()","available transports = ["+this.transports+"]"),k.logAction(this.logger,k.LOG_MICRO,"Realtime.ConnectionManager()","http hosts = ["+this.httpHosts+"]"),!this.transports.length){const e="no requested transports available";throw k.logAction(this.logger,k.LOG_ERROR,"realtime.ConnectionManager()",e),new Error(e)}const a=g.Config.addEventListener;a&&(Ds()&&"function"==typeof t.recover&&a("beforeunload",this.persistConnection.bind(this)),!0===t.closeOnUnload&&a("beforeunload",()=>{k.logAction(this.logger,k.LOG_MAJOR,"Realtime.ConnectionManager()","beforeunload event has triggered the connection to close as closeOnUnload is true"),this.requestState({state:"closing"})}),a("online",()=>{var e;this.state==this.states.disconnected||this.state==this.states.suspended?(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager caught browser ‘online’ event","reattempting connection"),this.requestState({state:"connecting"})):this.state==this.states.connecting&&(null==(e=this.pendingTransport)||e.off(),this.disconnectAllTransports(),this.startConnect())}),a("offline",()=>{this.state==this.states.connected&&(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager caught browser ‘offline’ event","disconnecting active transport"),this.disconnectAllTransports())}))}static supportedTransports(e){const t={supportedTransports:{}};return this.initTransports(e,t),t.supportedTransports}static initTransports(e,t){const s=p(p({},g.Transports.bundledImplementations),e);[Cs.WebSocket,...g.Transports.order].forEach(e=>{const n=s[e];n&&n.isAvailable()&&(t.supportedTransports[e]=n)})}initTransports(){e.initTransports(this.realtime._additionalTransportImplementations,this)}createTransportParams(e,t){return new Hs(this.options,e,t,this.connectionKey)}getTransportParams(e){(e=>{if(this.connectionKey)return void e("resume");if("string"==typeof this.options.recover)return void e("recover");const t=this.options.recover,s=this.getSessionRecoverData(),n=this.sessionRecoveryName();if(s&&"function"==typeof t)return k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.getTransportParams()","Calling clientOptions-provided recover function with last session data (recovery scope: "+n+")"),void t(s,t=>{t?(this.options.recover=s.recoveryKey,e("recover")):e("clean")});e("clean")})(t=>{const s=this.createTransportParams(null,t);if("recover"===t){k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.getTransportParams()","Transport recovery mode = recover; recoveryKey = "+this.options.recover);const e=qs(this.options.recover);e&&(this.msgSerial=e.msgSerial)}else k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.getTransportParams()","Transport params = "+s.toString());e(s)})}tryATransport(e,t,s){k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.tryATransport()","trying "+t),this.proposedTransport=Ns.tryConnect(this.supportedTransports[t],this,this.realtime.auth,e,(n,a)=>{const i=this.state;return i==this.states.closing||i==this.states.closed||i==this.states.failed?(a&&(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.tryATransport()","connection "+i.state+" while we were attempting the transport; closing "+a),a.close()),void s(!0)):n?(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.tryATransport()","transport "+t+" "+n.event+", err: "+n.error.toString()),void(!He.isTokenErr(n.error)||this.errorReason&&He.isTokenErr(this.errorReason)?"failed"===n.event?(this.notifyState({state:"failed",error:n.error}),s(!0)):"disconnected"===n.event&&(!(r=n.error).statusCode||!r.code||r.statusCode>=500||Object.values(Is).includes(r.code)?s(!1):(this.notifyState({state:this.states.connecting.failState,error:n.error}),s(!0))):(this.errorReason=n.error,ne(this.realtime.auth._forceNewToken(null,null),n=>{n?this.actOnErrorFromAuthorize(n):this.tryATransport(e,t,s)})))):(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.tryATransport()","viable transport "+t+"; setting pending"),this.setTransportPending(a,e),void s(null,a));var r})}setTransportPending(e,t){const s=t.mode;k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.setTransportPending()","transport = "+e+"; mode = "+s),this.pendingTransport=e,this.cancelWebSocketSlowTimer(),this.cancelWebSocketGiveUpTimer(),e.once("connected",(t,n,a)=>{this.activateTransport(t,e,n,a),"recover"===s&&this.options.recover&&(delete this.options.recover,this.unpersistConnection())});const n=this;e.on(["disconnected","closed","failed"],function(t){n.deactivateTransport(e,this.event,t)}),this.emit("transport.pending",e)}activateTransport(e,t,s,n){k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.activateTransport()","transport = "+t),e&&k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.activateTransport()","error = "+e),s&&k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.activateTransport()","connectionId =  "+s),n&&k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.activateTransport()","connectionDetails =  "+JSON.stringify(n)),this.persistTransportPreference(t);const a=this.state,i=this.states.connected.state;if(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.activateTransport()","current state = "+a.state),a.state==this.states.closing.state||a.state==this.states.closed.state||a.state==this.states.failed.state)return k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.activateTransport()","Disconnecting transport and abandoning"),t.disconnect(),!1;if(delete this.pendingTransport,!t.isConnected)return k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.activateTransport()","Declining to activate transport "+t+" since it appears to no longer be connected"),!1;const r=this.activeProtocol;this.activeProtocol=new As(t),this.host=t.params.host;const o=n.connectionKey;if(o&&this.connectionKey!=o&&this.setConnection(s,n,!!e),this.onConnectionDetailsUpdate(n,t),g.Config.nextTick(()=>{t.on("connected",(e,s,n)=>{this.onConnectionDetailsUpdate(n,t),this.emit("update",new Os(i,i,null,e))})}),a.state===this.states.connected.state?e&&(this.errorReason=this.realtime.connection.errorReason=e,this.emit("update",new Os(i,i,null,e))):(this.notifyState({state:"connected",error:e}),this.errorReason=this.realtime.connection.errorReason=e||null),this.emit("transport.active",t),r)if(r.messageQueue.count()>0&&k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.activateTransport()","Previous active protocol (for transport "+r.transport.shortName+", new one is "+t.shortName+") finishing with "+r.messageQueue.count()+" messages still pending"),r.transport===t){const e="Assumption violated: activating a transport that was also the transport for the previous active protocol; transport = "+t.shortName+"; stack = "+(new Error).stack;k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.activateTransport()",e)}else r.finish();return!0}deactivateTransport(e,t,s){const n=this.activeProtocol,a=n&&n.getTransport()===e,i=e===this.pendingTransport,r=this.noTransportsScheduledForActivation();if(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.deactivateTransport()","transport = "+e),k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.deactivateTransport()","state = "+t+(a?"; was active":i?"; was pending":"")+(r?"":"; another transport is scheduled for activation")),s&&s.message&&k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.deactivateTransport()","reason =  "+s.message),a&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.deactivateTransport()","Getting, clearing, and requeuing "+this.activeProtocol.messageQueue.count()+" pending messages"),this.queuePendingMessages(n.getPendingMessages()),n.clearPendingMessages(),this.activeProtocol=this.host=null),this.emit("transport.inactive",e),a&&r||a&&"failed"===t||"closed"===t||null===n&&i){if("disconnected"===t&&s&&s.statusCode>500&&this.httpHosts.length>1)return this.unpersistTransportPreference(),this.forceFallbackHost=!0,void this.notifyState({state:t,error:s,retryImmediately:!0});const e="failed"===t&&He.isTokenErr(s)?"disconnected":t;return void this.notifyState({state:e,error:s})}}noTransportsScheduledForActivation(){return!this.pendingTransport||!this.pendingTransport.isConnected}setConnection(e,t,s){const n=this.connectionId;(n&&n!==e||!n&&s)&&(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.setConnection()","Resetting msgSerial"),this.msgSerial=0,this.queuedMessages.resetSendAttempted()),this.connectionId!==e&&k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.setConnection()","New connectionId; reattaching any attached channels"),this.realtime.connection.id=this.connectionId=e,this.realtime.connection.key=this.connectionKey=t.connectionKey}clearConnection(){this.realtime.connection.id=this.connectionId=void 0,this.realtime.connection.key=this.connectionKey=void 0,this.msgSerial=0,this.unpersistConnection()}createRecoveryKey(){return this.connectionKey?JSON.stringify({connectionKey:this.connectionKey,msgSerial:this.msgSerial,channelSerials:this.realtime.channels.channelSerials()}):null}checkConnectionStateFreshness(){if(!this.lastActivity||!this.connectionId)return;const e=Date.now()-this.lastActivity;e>this.connectionStateTtl+this.maxIdleInterval&&(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.checkConnectionStateFreshness()","Last known activity from realtime was "+e+"ms ago; discarding connection state"),this.clearConnection(),this.states.connecting.failState="suspended")}persistConnection(){if(Ds()){const e=this.createRecoveryKey();e&&this.setSessionRecoverData({recoveryKey:e,disconnectedAt:Date.now(),location:Us.location,clientId:this.realtime.auth.clientId})}}unpersistConnection(){this.clearSessionRecoverData()}getActiveTransportFormat(){var e;return null==(e=this.activeProtocol)?void 0:e.getTransport().format}getError(){if(this.errorReason){const e=R.fromValues(this.errorReason);return e.cause=this.errorReason,e}return this.getStateError()}getStateError(){var e,t;return null==(t=(e=Ms)[this.state.state])?void 0:t.call(e)}activeState(){return this.state.queueEvents||this.state.sendEvents}enactStateChange(e){const t="Connection state",s=e.current+(e.reason?"; reason: "+e.reason:"");"failed"===e.current?k.logAction(this.logger,k.LOG_ERROR,t,s):k.logAction(this.logger,k.LOG_MAJOR,t,s),k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.enactStateChange","setting new state: "+e.current+"; reason = "+(e.reason&&e.reason.message));const n=this.state=this.states[e.current];e.reason&&(this.errorReason=e.reason,this.realtime.connection.errorReason=e.reason),(n.terminal||"suspended"===n.state)&&this.clearConnection(),this.emit("connectionstate",e)}startTransitionTimer(e){k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.startTransitionTimer()","transitionState: "+e.state),this.transitionTimer&&(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.startTransitionTimer()","clearing already-running timer"),clearTimeout(this.transitionTimer)),this.transitionTimer=setTimeout(()=>{this.transitionTimer&&(this.transitionTimer=null,k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager "+e.state+" timer expired","requesting new state: "+e.failState),this.notifyState({state:e.failState}))},e.retryDelay)}cancelTransitionTimer(){k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.cancelTransitionTimer()",""),this.transitionTimer&&(clearTimeout(this.transitionTimer),this.transitionTimer=null)}startSuspendTimer(){this.suspendTimer||(this.suspendTimer=setTimeout(()=>{this.suspendTimer&&(this.suspendTimer=null,k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager suspend timer expired","requesting new state: suspended"),this.states.connecting.failState="suspended",this.notifyState({state:"suspended"}))},this.connectionStateTtl))}checkSuspendTimer(e){"disconnected"!==e&&"suspended"!==e&&"connecting"!==e&&this.cancelSuspendTimer()}cancelSuspendTimer(){this.states.connecting.failState="disconnected",this.suspendTimer&&(clearTimeout(this.suspendTimer),this.suspendTimer=null)}startRetryTimer(e){this.retryTimer=setTimeout(()=>{k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager retry timer expired","retrying"),this.retryTimer=null,this.requestState({state:"connecting"})},e)}cancelRetryTimer(){this.retryTimer&&(clearTimeout(this.retryTimer),this.retryTimer=null)}startWebSocketSlowTimer(){this.webSocketSlowTimer=setTimeout(()=>{k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager WebSocket slow timer","checking connectivity"),this.checkWsConnectivity().then(()=>{k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager WebSocket slow timer","ws connectivity check succeeded"),this.wsCheckResult=!0}).catch(()=>{k.logAction(this.logger,k.LOG_MAJOR,"ConnectionManager WebSocket slow timer","ws connectivity check failed"),this.wsCheckResult=!1}),this.realtime.http.checkConnectivity&&ne(this.realtime.http.checkConnectivity(),(e,t)=>{e||!t?(k.logAction(this.logger,k.LOG_MAJOR,"ConnectionManager WebSocket slow timer","http connectivity check failed"),this.cancelWebSocketGiveUpTimer(),this.notifyState({state:"disconnected",error:new E("Unable to connect (network unreachable)",80003,404)})):k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager WebSocket slow timer","http connectivity check succeeded")})},this.options.timeouts.webSocketSlowTimeout)}cancelWebSocketSlowTimer(){this.webSocketSlowTimer&&(clearTimeout(this.webSocketSlowTimer),this.webSocketSlowTimer=null)}startWebSocketGiveUpTimer(e){this.webSocketGiveUpTimer=setTimeout(()=>{var t,s;this.wsCheckResult||(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager WebSocket give up timer","websocket connection took more than 10s; "+(this.baseTransport?"trying base transport":"")),this.baseTransport?(this.abandonedWebSocket=!0,null==(t=this.proposedTransport)||t.dispose(),null==(s=this.pendingTransport)||s.dispose(),this.connectBase(e,++this.connectCounter)):k.logAction(this.logger,k.LOG_MAJOR,"ConnectionManager WebSocket give up timer","websocket connectivity appears to be unavailable but no other transports to try"))},this.options.timeouts.webSocketConnectTimeout)}cancelWebSocketGiveUpTimer(){this.webSocketGiveUpTimer&&(clearTimeout(this.webSocketGiveUpTimer),this.webSocketGiveUpTimer=null)}notifyState(e){var t,s;const n=e.state,a="disconnected"===n&&(this.state===this.states.connected||e.retryImmediately||this.state===this.states.connecting&&e.error&&He.isTokenErr(e.error)&&!(this.errorReason&&He.isTokenErr(this.errorReason)));if(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.notifyState()","new state: "+n+(a?"; will retry connection immediately":"")),n==this.state.state)return;if(this.cancelTransitionTimer(),this.cancelRetryTimer(),this.cancelWebSocketSlowTimer(),this.cancelWebSocketGiveUpTimer(),this.checkSuspendTimer(e.state),"suspended"!==n&&"connected"!==n||(this.disconnectedRetryCount=0),this.state.terminal)return;const i=this.states[e.state];let r=i.retryDelay;"disconnected"===i.state&&(this.disconnectedRetryCount++,r=ue(i.retryDelay,this.disconnectedRetryCount));const o=new Os(this.state.state,i.state,r,e.error||(null==(s=(t=Ms)[i.state])?void 0:s.call(t)));if(a){const e=()=>{this.state===this.states.disconnected&&(this.lastAutoReconnectAttempt=Date.now(),this.requestState({state:"connecting"}))},t=this.lastAutoReconnectAttempt&&Date.now()-this.lastAutoReconnectAttempt+1;t&&t<1e3?(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.notifyState()","Last reconnect attempt was only "+t+"ms ago, waiting another "+(1e3-t)+"ms before trying again"),setTimeout(e,1e3-t)):g.Config.nextTick(e)}else"disconnected"!==n&&"suspended"!==n||this.startRetryTimer(r);("disconnected"===n&&!a||"suspended"===n||i.terminal)&&g.Config.nextTick(()=>{this.disconnectAllTransports()}),"connected"!=n||this.activeProtocol||k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.notifyState()","Broken invariant: attempted to go into connected state, but there is no active protocol"),this.enactStateChange(o),this.state.sendEvents?this.sendQueuedMessages():this.state.queueEvents||(this.realtime.channels.propogateConnectionInterruption(n,o.reason),this.failQueuedMessages(o.reason))}requestState(e){var t,s;const n=e.state;if(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.requestState()","requested state: "+n+"; current state: "+this.state.state),n==this.state.state)return;if(this.cancelWebSocketSlowTimer(),this.cancelWebSocketGiveUpTimer(),this.cancelTransitionTimer(),this.cancelRetryTimer(),this.checkSuspendTimer(n),"connecting"==n&&"connected"==this.state.state)return;if("closing"==n&&"closed"==this.state.state)return;const a=this.states[n],i=new Os(this.state.state,a.state,null,e.error||(null==(s=(t=Ms)[a.state])?void 0:s.call(t)));this.enactStateChange(i),"connecting"==n&&g.Config.nextTick(()=>{this.startConnect()}),"closing"==n&&this.closeImpl()}startConnect(){if(this.state!==this.states.connecting)return void k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.startConnect()","Must be in connecting state to connect, but was "+this.state.state);const e=this.realtime.auth,t=++this.connectCounter,s=()=>{this.checkConnectionStateFreshness(),this.getTransportParams(e=>{if("recover"===e.mode&&e.options.recover){const t=qs(e.options.recover);t&&this.realtime.channels.recoverChannels(t.channelSerials)}t===this.connectCounter&&this.connectImpl(e,t)})};if(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.startConnect()","starting connection"),this.startSuspendTimer(),this.startTransitionTimer(this.states.connecting),"basic"===e.method)s();else{const n=e=>{t===this.connectCounter&&(e?this.actOnErrorFromAuthorize(e):s())};this.errorReason&&He.isTokenErr(this.errorReason)?ne(e._forceNewToken(null,null),n):ne(e._ensureValidAuthCredentials(!1),n)}}connectImpl(e,t){const s=this.state.state;if(s!==this.states.connecting.state)return void k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.connectImpl()","Must be in connecting state to connect, but was "+s);const n=this.getTransportPreference();n&&n===this.baseTransport&&this.webSocketTransportAvailable&&this.checkWsConnectivity().then(()=>{this.unpersistTransportPreference(),this.state===this.states.connecting&&(k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.connectImpl():","web socket connectivity available, cancelling connection attempt with "+this.baseTransport),this.disconnectAllTransports(),this.connectWs(e,++this.connectCounter))}).catch(Bs),n&&n===this.baseTransport||this.baseTransport&&!this.webSocketTransportAvailable?this.connectBase(e,t):this.connectWs(e,t)}connectWs(e,t){k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.connectWs()"),this.wsCheckResult=null,this.abandonedWebSocket=!1,this.startWebSocketSlowTimer(),this.startWebSocketGiveUpTimer(e),this.tryTransportWithFallbacks("web_socket",e,!0,t,()=>!1!==this.wsCheckResult&&!this.abandonedWebSocket)}connectBase(e,t){k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.connectBase()"),this.baseTransport?this.tryTransportWithFallbacks(this.baseTransport,e,!1,t,()=>!0):this.notifyState({state:"disconnected",error:new E("No transports left to try",8e4,404)})}tryTransportWithFallbacks(e,t,s,n,a){k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.tryTransportWithFallbacks()",e);const i=e=>{this.notifyState({state:this.states.connecting.failState,error:e})},r=s?this.wsHosts.slice():this.httpHosts.slice(),o=(e,t)=>{n===this.connectCounter&&(a()?t||e||c():t&&t.dispose())},l=r.shift();if(!l)return void i(new E("Unable to connect (no available host)",80003,404));t.host=l;const c=()=>{r.length?this.realtime.http.checkConnectivity?ne(this.realtime.http.checkConnectivity(),(s,l)=>{n===this.connectCounter&&a()&&(s?i(s):l?(t.host=z(r),this.tryATransport(t,e,o)):i(new E("Unable to connect (network unreachable)",80003,404)))}):i(new R("Internal error: Http.checkConnectivity not set",null,500)):i(new E("Unable to connect (and no more fallback hosts to try)",80003,404))};if(this.forceFallbackHost&&r.length)return this.forceFallbackHost=!1,void c();this.tryATransport(t,e,o)}closeImpl(){k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.closeImpl()","closing connection"),this.cancelSuspendTimer(),this.startTransitionTimer(this.states.closing),this.pendingTransport&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.closeImpl()","Closing pending transport: "+this.pendingTransport),this.pendingTransport.close()),this.activeProtocol&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.closeImpl()","Closing active transport: "+this.activeProtocol.getTransport()),this.activeProtocol.getTransport().close()),this.notifyState({state:"closed"})}onAuthUpdated(e,t){var s;switch(this.state.state){case"connected":{k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.onAuthUpdated()","Sending AUTH message on active transport");const n=null==(s=this.activeProtocol)?void 0:s.getTransport();n&&n.onAuthUpdated&&n.onAuthUpdated(e);const a=ps({action:ze.AUTH,auth:{accessToken:e.token}});this.send(a);const i=()=>{this.off(r),t(null,e)},r=e=>{"failed"===e.current&&(this.off(i),this.off(r),t(e.reason||this.getStateError()))};this.once("connectiondetails",i),this.on("connectionstate",r);break}case"connecting":k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.onAuthUpdated()","Aborting current connection attempts in order to start again with the new auth details"),this.disconnectAllTransports();default:{k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.onAuthUpdated()","Connection state is "+this.state.state+"; waiting until either connected or failed");const s=n=>{switch(n.current){case"connected":this.off(s),t(null,e);break;case"failed":case"closed":case"suspended":this.off(s),t(n.reason||this.getStateError())}};this.on("connectionstate",s),"connecting"===this.state.state?this.startConnect():this.requestState({state:"connecting"})}}}disconnectAllTransports(){k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.disconnectAllTransports()","Disconnecting all transports"),this.connectCounter++,this.pendingTransport&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.disconnectAllTransports()","Disconnecting pending transport: "+this.pendingTransport),this.pendingTransport.disconnect()),delete this.pendingTransport,this.proposedTransport&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.disconnectAllTransports()","Disconnecting proposed transport: "+this.pendingTransport),this.proposedTransport.disconnect()),delete this.pendingTransport,this.activeProtocol&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.disconnectAllTransports()","Disconnecting active transport: "+this.activeProtocol.getTransport()),this.activeProtocol.getTransport().disconnect())}send(e,t,s){s=s||Bs;const n=this.state;if(n.sendEvents)return k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.send()","sending event"),void this.sendImpl(new Ts(e,s));if(!t||!n.queueEvents){const e="rejecting event, queueEvent was "+t+", state was "+n.state;return k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.send()",e),void s(this.errorReason||new E(e,9e4,400))}this.logger.shouldLog(k.LOG_MICRO)&&k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.send()","queueing msg; "+fs(e,this.realtime._RealtimePresence,this.realtime._Annotations,this.realtime._objectsPlugin)),this.queue(e,s)}sendImpl(e){const t=e.message;e.ackRequired&&!e.sendAttempted&&(t.msgSerial=this.msgSerial++);try{this.activeProtocol.send(e)}catch(s){k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.sendImpl()","Unexpected exception in transport.send(): "+s.stack)}}queue(e,t){k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.queue()","queueing event");const s=this.queuedMessages.last(),n=this.options.maxMessageSize;s&&!s.sendAttempted&&function(e,t,s){let n;if(e.channel!==t.channel)return!1;if((n=e.action)!==ze.PRESENCE&&n!==ze.MESSAGE)return!1;if(n!==t.action)return!1;const a=n===ze.PRESENCE?"presence":"messages",i=e[a].concat(t[a]);return!(Bt(i)>s||!$(i,"clientId")||!i.every(function(e){return!e.id})||(e[a]=i,0))}(s.message,e,n)?(s.merged||(s.callback=Ie.create(this.logger,[s.callback]),s.merged=!0),s.callback.push(t)):this.queuedMessages.push(new Ts(e,t))}sendQueuedMessages(){let e;for(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.sendQueuedMessages()","sending "+this.queuedMessages.count()+" queued messages");e=this.queuedMessages.shift();)this.sendImpl(e)}queuePendingMessages(e){e&&e.length&&(k.logAction(this.logger,k.LOG_MICRO,"ConnectionManager.queuePendingMessages()","queueing "+e.length+" pending messages"),this.queuedMessages.prepend(e))}failQueuedMessages(e){const t=this.queuedMessages.count();t>0&&(k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.failQueuedMessages()","failing "+t+" queued messages, err = "+Q(e)),this.queuedMessages.completeAllMessages(e))}onChannelMessage(e,t){this.pendingChannelMessagesState.queue.push({message:e,transport:t}),this.pendingChannelMessagesState.isProcessing||this.processNextPendingChannelMessage()}processNextPendingChannelMessage(){if(this.pendingChannelMessagesState.queue.length>0){this.pendingChannelMessagesState.isProcessing=!0;const e=this.pendingChannelMessagesState.queue.shift();this.processChannelMessage(e.message).catch(e=>{k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.processNextPendingChannelMessage() received error ",e)}).finally(()=>{this.pendingChannelMessagesState.isProcessing=!1,this.processNextPendingChannelMessage()})}}async processChannelMessage(e){await this.realtime.channels.processChannelMessage(e)}async ping(){var e;if("connected"!==this.state.state)throw new E("Unable to ping service; not connected",4e4,400);const t=null==(e=this.activeProtocol)?void 0:e.getTransport();if(!t)throw this.getStateError();k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.ping()","transport = "+t);const s=Date.now(),n=ee();return ye(new Promise(e=>{const a=i=>{i===n&&(t.off("heartbeat",a),e(Date.now()-s))};t.on("heartbeat",a),t.ping(n)}),this.options.timeouts.realtimeRequestTimeout,"Timeout waiting for heartbeat response")}abort(e){this.activeProtocol.getTransport().fail(e)}getTransportPreference(){var e,t;return this.transportPreference||xs()&&(null==(t=null==(e=g.WebStorage)?void 0:e.get)?void 0:t.call(e,Vs))}persistTransportPreference(e){var t,s;this.transportPreference=e.shortName,xs()&&(null==(s=null==(t=g.WebStorage)?void 0:t.set)||s.call(t,Vs,e.shortName))}unpersistTransportPreference(){var e,t;this.transportPreference=null,xs()&&(null==(t=null==(e=g.WebStorage)?void 0:e.remove)||t.call(e,Vs))}actOnErrorFromAuthorize(e){if(40171===e.code)this.notifyState({state:"failed",error:e});else if(40102===e.code)this.notifyState({state:"failed",error:e});else if(e.statusCode===Ne.Forbidden){const t="Client configured authentication provider returned 403; failing the connection";k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.actOnErrorFromAuthorize()",t),this.notifyState({state:"failed",error:new E(t,80019,403,e)})}else{const t="Client configured authentication provider request failed";k.logAction(this.logger,k.LOG_MINOR,"ConnectionManager.actOnErrorFromAuthorize",t),this.notifyState({state:this.state.failState,error:new E(t,80019,401,e)})}}onConnectionDetailsUpdate(e,t){if(!e)return;this.connectionDetails=e,e.maxMessageSize&&(this.options.maxMessageSize=e.maxMessageSize);const s=e.clientId;if(s){const e=this.realtime.auth._uncheckedSetClientId(s);if(e)return k.logAction(this.logger,k.LOG_ERROR,"ConnectionManager.onConnectionDetailsUpdate()",e.message),void t.fail(e)}const n=e.connectionStateTtl;n&&(this.connectionStateTtl=n),this.maxIdleInterval=e.maxIdleInterval,this.emit("connectiondetails",e)}checkWsConnectivity(){const e=this.options.wsConnectivityCheckUrl||Oe.wsConnectivityCheckUrl,t=new g.Config.WebSocket(e);return new Promise((e,s)=>{let n=!1;t.onopen=()=>{n||(n=!0,e(),t.close())},t.onclose=t.onerror=()=>{n||(n=!0,s())}})}sessionRecoveryName(){return this.options.recoveryKeyStorageName||"ably-connection-recovery"}getSessionRecoverData(){var e,t;return Ds()&&(null==(t=null==(e=g.WebStorage)?void 0:e.getSession)?void 0:t.call(e,this.sessionRecoveryName()))}setSessionRecoverData(e){var t,s;return Ds()&&(null==(s=null==(t=g.WebStorage)?void 0:t.setSession)?void 0:s.call(t,this.sessionRecoveryName(),e))}clearSessionRecoverData(){var e,t;return Ds()&&(null==(t=null==(e=g.WebStorage)?void 0:e.removeSession)?void 0:t.call(e,this.sessionRecoveryName()))}},js=class extends We{constructor(e,t){super(e.logger),this.whenState=e=>We.prototype.whenState.call(this,e,this.state),this.ably=e,this.connectionManager=new Gs(e,t),this.state=this.connectionManager.state.state,this.key=void 0,this.id=void 0,this.errorReason=null,this.connectionManager.on("connectionstate",e=>{const t=this.state=e.current;g.Config.nextTick(()=>{this.emit(t,e)})}),this.connectionManager.on("update",e=>{g.Config.nextTick(()=>{this.emit("update",e)})})}connect(){k.logAction(this.logger,k.LOG_MINOR,"Connection.connect()",""),this.connectionManager.requestState({state:"connecting"})}async ping(){return k.logAction(this.logger,k.LOG_MINOR,"Connection.ping()",""),this.connectionManager.ping()}close(){k.logAction(this.logger,k.LOG_MINOR,"Connection.close()","connectionKey = "+this.key),this.connectionManager.requestState({state:"closing"})}get recoveryKey(){return this.logger.deprecationWarning("The `Connection.recoveryKey` attribute has been replaced by the `Connection.createRecoveryKey()` method. Replace your usage of `recoveryKey` with the return value of `createRecoveryKey()`. `recoveryKey` will be removed in a future version."),this.createRecoveryKey()}createRecoveryKey(){return this.connectionManager.createRecoveryKey()}},Fs=class e extends dt{constructor(t){var s,n,a,i;if(super(Oe.objectifyOptions(t,!1,"BaseRealtime",k.defaultLogger)),k.logAction(this.logger,k.LOG_MINOR,"Realtime()",""),"string"==typeof EdgeRuntime)throw new E('Ably.Realtime instance cannot be used in Vercel Edge runtime. If you are running Vercel Edge functions, please replace your "new Ably.Realtime()" with "new Ably.Rest()" and use Ably Rest API instead of the Realtime API. If you are server-rendering your application in the Vercel Edge runtime, please use the condition "if (typeof EdgeRuntime === \'string\')" to prevent instantiating Ably.Realtime instance during SSR in the Vercel Edge runtime.',4e4,400);this._additionalTransportImplementations=e.transportImplementationsFromPlugins(this.options.plugins),this._RealtimePresence=null!=(n=null==(s=this.options.plugins)?void 0:s.RealtimePresence)?n:null,this._objectsPlugin=null!=(i=null==(a=this.options.plugins)?void 0:a.Objects)?i:null,this.connection=new js(this,this.options),this._channels=new Ws(this),!1!==this.options.autoConnect&&this.connect()}static transportImplementationsFromPlugins(e){const t={};return(null==e?void 0:e.WebSocketTransport)&&(t[Cs.WebSocket]=e.WebSocketTransport),(null==e?void 0:e.XHRPolling)&&(t[Cs.XhrPolling]=e.XHRPolling),t}get channels(){return this._channels}connect(){k.logAction(this.logger,k.LOG_MINOR,"Realtime.connect()",""),this.connection.connect()}close(){k.logAction(this.logger,k.LOG_MINOR,"Realtime.close()",""),this.connection.close()}};Fs.EventEmitter=We;var $s=Fs,Ws=class extends We{constructor(e){super(e.logger),this.realtime=e,this.all=Object.create(null),e.connection.connectionManager.on("transport.active",()=>{this.onTransportActive()})}channelSerials(){let e={};for(const t of G(this.all,!0)){const s=this.all[t];s.properties.channelSerial&&(e[t]=s.properties.channelSerial)}return e}recoverChannels(e){for(const t of G(e,!0))this.get(t).properties.channelSerial=e[t]}async processChannelMessage(e){const t=e.channel;if(void 0===t)return void k.logAction(this.logger,k.LOG_ERROR,"Channels.processChannelMessage()","received event unspecified channel, action = "+e.action);const s=this.all[t];s?await s.processMessage(e):k.logAction(this.logger,k.LOG_ERROR,"Channels.processChannelMessage()","received event for non-existent channel: "+t)}onTransportActive(){for(const e in this.all){const t=this.all[e];"attaching"===t.state||"detaching"===t.state?t.checkPendingState():"suspended"===t.state?t._attach(!1,null):"attached"===t.state&&t.requestState("attaching")}}propogateConnectionInterruption(e,t){const s=["attaching","attached","detaching","suspended"],n={closing:"detached",closed:"detached",failed:"failed",suspended:"suspended"}[e];for(const a in this.all){const e=this.all[a];s.includes(e.state)&&e.notifyState(n,t)}}get(e,t){e=String(e);let s=this.all[e];if(s){if(t){if(s._shouldReattachToSetOptions(t,s.channelOptions))throw new E("Channels.get() cannot be used to set channel options that would cause the channel to reattach. Please, use RealtimeChannel.setOptions() instead.",4e4,400);s.setOptions(t)}}else s=this.all[e]=new ws(this.realtime,e,t);return s}getDerived(e,t,s){if(t.filter){const s=fe(t.filter),n=pe(e);e=`[filter=${s}${n.qualifierParam}]${n.channelName}`}return this.get(e,s)}release(e){e=String(e);const t=this.all[e];if(!t)return;const s=t.getReleaseErr();if(s)throw s;delete this.all[e]}},zs=$s;function Js(e,t){if(e.isSynthesized()||t.isSynthesized())return e.timestamp>=t.timestamp;const s=e.parseId(),n=t.parseId();return s.msgSerial===n.msgSerial?s.index>n.index:s.msgSerial>n.msgSerial}var Ks=class extends We{constructor(e,t,s=Js){super(e.logger),this.presence=e,this.map=Object.create(null),this.syncInProgress=!1,this.residualMembers=null,this.memberKey=t,this.newerThan=s}get(e){return this.map[e]}getClient(e){const t=this.map,s=[];for(const n in t){const a=t[n];a.clientId==e&&"absent"!=a.action&&s.push(a)}return s}list(e){const t=this.map,s=e&&e.clientId,n=e&&e.connectionId,a=[];for(const i in t){const e=t[i];"absent"!==e.action&&(s&&s!=e.clientId||n&&n!=e.connectionId||a.push(e))}return a}put(e){"enter"!==e.action&&"update"!==e.action||((e=It.fromValues(e)).action="present");const t=this.map,s=this.memberKey(e);this.residualMembers&&delete this.residualMembers[s];const n=t[s];return!(n&&!this.newerThan(e,n)||(t[s]=e,0))}values(){const e=this.map,t=[];for(const s in e){const n=e[s];"absent"!=n.action&&t.push(n)}return t}remove(e){const t=this.map,s=this.memberKey(e),n=t[s];return!(n&&!this.newerThan(e,n)||(this.syncInProgress?((e=It.fromValues(e)).action="absent",t[s]=e):delete t[s],!n))}startSync(){const e=this.map,t=this.syncInProgress;k.logAction(this.logger,k.LOG_MINOR,"PresenceMap.startSync()","channel = "+this.presence.channel.name+"; syncInProgress = "+t),this.syncInProgress||(this.residualMembers=A(e),this.setInProgress(!0))}endSync(){const e=this.map,t=this.syncInProgress;if(k.logAction(this.logger,k.LOG_MINOR,"PresenceMap.endSync()","channel = "+this.presence.channel.name+"; syncInProgress = "+t),t){for(const t in e)"absent"===e[t].action&&delete e[t];this.presence._synthesizeLeaves(j(this.residualMembers));for(const t in this.residualMembers)delete e[t];this.residualMembers=null,this.setInProgress(!1)}this.emit("sync")}waitSync(e){const t=this.syncInProgress;k.logAction(this.logger,k.LOG_MINOR,"PresenceMap.waitSync()","channel = "+this.presence.channel.name+"; syncInProgress = "+t),t?this.once("sync",e):e()}clear(){this.map={},this.setInProgress(!1),this.residualMembers=null}setInProgress(e){k.logAction(this.logger,k.LOG_MICRO,"PresenceMap.setInProgress()","inProgress = "+e),this.syncInProgress=e,this.presence.syncComplete=!e}};function Ys(e){const t=e.channel.client,s=t.auth.clientId;return(!s||"*"===s)&&"connected"===t.connection.state}var Qs=class extends We{constructor(e){super(e.logger),this.channel=e,this.syncComplete=!1,this.members=new Ks(this,e=>e.clientId+":"+e.connectionId),this._myMembers=new Ks(this,e=>e.clientId),this.subscriptions=new We(this.logger),this.pendingPresence=[]}async enter(e){if(Ys(this))throw new E("clientId must be specified to enter a presence channel",40012,400);return this._enterOrUpdateClient(void 0,void 0,e,"enter")}async update(e){if(Ys(this))throw new E("clientId must be specified to update presence data",40012,400);return this._enterOrUpdateClient(void 0,void 0,e,"update")}async enterClient(e,t){return this._enterOrUpdateClient(void 0,e,t,"enter")}async updateClient(e,t){return this._enterOrUpdateClient(void 0,e,t,"update")}async _enterOrUpdateClient(e,t,s,n){const a=this.channel;if(!a.connectionManager.activeState())throw a.connectionManager.getError();k.logAction(this.logger,k.LOG_MICRO,"RealtimePresence."+n+"Client()","channel = "+a.name+", id = "+e+", client = "+(t||"(implicit) "+this.channel.client.auth.clientId));const i=It.fromData(s);i.action=n,e&&(i.id=e),t&&(i.clientId=t);const r=await i.encode(a.channelOptions);switch(a.state){case"attached":return a.sendPresence([r]);case"initialized":case"detached":a.attach();case"attaching":return new Promise((e,t)=>{this.pendingPresence.push({presence:r,callback:s=>s?t(s):e()})});default:{const e=new R("Unable to "+n+" presence channel while in "+a.state+" state",90001);throw e.code=90001,e}}}async leave(e){if(Ys(this))throw new E("clientId must have been specified to enter or leave a presence channel",40012,400);return this.leaveClient(void 0,e)}async leaveClient(e,t){const s=this.channel;if(!s.connectionManager.activeState())throw s.connectionManager.getError();k.logAction(this.logger,k.LOG_MICRO,"RealtimePresence.leaveClient()","leaving; channel = "+this.channel.name+", client = "+e);const n=It.fromData(t);n.action="leave",e&&(n.clientId=e);const a=await n.encode(s.channelOptions);switch(s.state){case"attached":return s.sendPresence([a]);case"attaching":return new Promise((e,t)=>{this.pendingPresence.push({presence:a,callback:s=>s?t(s):e()})});case"initialized":case"failed":throw new R("Unable to leave presence channel (incompatible state)",90001);default:throw s.invalidStateError()}}async get(e){const t=!e||!("waitForSync"in e)||e.waitForSync;return new Promise((s,n)=>{function a(t){s(e?t.list(e):t.values())}"suspended"!==this.channel.state?function(e,t,s){switch(e.state){case"attached":case"suspended":s();break;case"initialized":case"detached":case"detaching":case"attaching":ne(e.attach(),function(e){e?t(e):s()});break;default:t(E.fromValues(e.invalidStateError()))}}(this.channel,e=>n(e),()=>{const e=this.members;t?e.waitSync(function(){a(e)}):a(e)}):t?n(E.fromValues({statusCode:400,code:91005,message:"Presence state is out of sync due to channel being in the SUSPENDED state"})):a(this.members)})}async history(e){k.logAction(this.logger,k.LOG_MICRO,"RealtimePresence.history()","channel = "+this.name);const t=this.channel.client.rest.presenceMixin;if(e&&e.untilAttach){if("attached"!==this.channel.state)throw new E("option untilAttach requires the channel to be attached, was: "+this.channel.state,4e4,400);delete e.untilAttach,e.from_serial=this.channel.properties.attachSerial}return t.history(this,e)}setPresence(e,t,s){let n,a;k.logAction(this.logger,k.LOG_MICRO,"RealtimePresence.setPresence()","received presence for "+e.length+" participants; syncChannelSerial = "+s);const i=this.members,r=this._myMembers,o=[],l=this.channel.connectionManager.connectionId;t&&(this.members.startSync(),s&&(a=s.match(/^[\w-]+:(.*)$/))&&(n=a[1]));for(let c of e)switch(c.action){case"leave":i.remove(c)&&o.push(c),c.connectionId!==l||c.isSynthesized()||r.remove(c);break;case"enter":case"present":case"update":i.put(c)&&o.push(c),c.connectionId===l&&r.put(c)}t&&!n&&(i.endSync(),this.channel.syncChannelSerial=null);for(let c=0;c<o.length;c++){const e=o[c];this.subscriptions.emit(e.action,e)}}onAttached(e){k.logAction(this.logger,k.LOG_MINOR,"RealtimePresence.onAttached()","channel = "+this.channel.name+", hasPresence = "+e),e?this.members.startSync():(this._synthesizeLeaves(this.members.values()),this.members.clear()),this._ensureMyMembersPresent();const t=this.pendingPresence,s=t.length;if(s){this.pendingPresence=[];const e=[],n=Ie.create(this.logger);k.logAction(this.logger,k.LOG_MICRO,"RealtimePresence.onAttached","sending "+s+" queued presence messages");for(let a=0;a<s;a++){const s=t[a];e.push(s.presence),n.push(s.callback)}this.channel.sendPresence(e).then(()=>n()).catch(e=>n(e))}}actOnChannelState(e,t,s){switch(e){case"attached":this.onAttached(t);break;case"detached":case"failed":this._clearMyMembers(),this.members.clear();case"suspended":this.failPendingPresence(s)}}failPendingPresence(e){if(this.pendingPresence.length){k.logAction(this.logger,k.LOG_MINOR,"RealtimeChannel.failPendingPresence","channel; name = "+this.channel.name+", err = "+Q(e));for(let s=0;s<this.pendingPresence.length;s++)try{this.pendingPresence[s].callback(e)}catch(t){}this.pendingPresence=[]}}_clearMyMembers(){this._myMembers.clear()}_ensureMyMembersPresent(){const e=this._myMembers,t=this.channel.connectionManager.connectionId;for(const s in e.map){const n=e.map[s];k.logAction(this.logger,k.LOG_MICRO,"RealtimePresence._ensureMyMembersPresent()",'Auto-reentering clientId "'+n.clientId+'" into the presence set');const a=n.connectionId===t?n.id:void 0;this._enterOrUpdateClient(a,n.clientId,n.data,"enter").catch(e=>{const t=new E("Presence auto re-enter failed",91004,400,e);k.logAction(this.logger,k.LOG_ERROR,"RealtimePresence._ensureMyMembersPresent()","Presence auto re-enter failed; reason = "+Q(e));const s=new gs(this.channel.state,this.channel.state,!0,!1,t);this.channel.emit("update",s)})}}_synthesizeLeaves(e){const t=this.subscriptions;e.forEach(function(e){const s=It.fromValues({action:"leave",connectionId:e.connectionId,clientId:e.clientId,data:e.data,encoding:e.encoding,timestamp:Date.now()});t.emit("leave",s)})}async subscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1],a=this.channel;if("failed"===a.state)throw E.fromValues(a.invalidStateError());this.subscriptions.on(s,n),!1!==a.channelOptions.attachOnSubscribe&&await a.attach()}unsubscribe(...e){const t=ws.processListenerArgs(e),s=t[0],n=t[1];this.subscriptions.off(s,n)}},Xs=Cs.WebSocket,Zs=class extends Ns{constructor(e,t,s){super(e,t,s),this.shortName=Xs,s.heartbeats=g.Config.useProtocolHeartbeats,this.wsHost=s.host}static isAvailable(){return!!g.Config.WebSocket}createWebSocket(e,t){return this.uri=e+J(t),new g.Config.WebSocket(this.uri)}toString(){return"WebSocketTransport; uri="+this.uri}connect(){k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.connect()","starting"),Ns.prototype.connect.call(this);const e=this,t=this.params,s=t.options,n=(s.tls?"wss://":"ws://")+this.wsHost+":"+Oe.getPort(s)+"/";k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.connect()","uri: "+n),ne(this.auth.getAuthParams(),function(s,a){if(e.isDisposed)return;let i="";for(const e in a)i+=" "+e+": "+a[e]+";";if(k.logAction(e.logger,k.LOG_MINOR,"WebSocketTransport.connect()","authParams:"+i+" err: "+s),s)return void e.disconnect(s);const r=t.getConnectParams(a);try{const t=e.wsConnection=e.createWebSocket(n,r);t.binaryType=g.Config.binaryType,t.onopen=function(){e.onWsOpen()},t.onclose=function(t){e.onWsClose(t)},t.onmessage=function(t){e.onWsData(t.data)},t.onerror=function(t){e.onWsError(t)},t.on&&t.on("ping",function(){e.onActivity()})}catch(o){k.logAction(e.logger,k.LOG_ERROR,"WebSocketTransport.connect()","Unexpected exception creating websocket: err = "+(o.stack||o.message)),e.disconnect(o)}})}send(e){const t=this.wsConnection;if(t)try{t.send(cs(e,this.connectionManager.realtime._MsgPack,this.params.format))}catch(s){const e="Exception from ws connection when trying to send: "+Q(s);k.logAction(this.logger,k.LOG_ERROR,"WebSocketTransport.send()",e),this.finish("disconnected",new E(e,5e4,500))}else k.logAction(this.logger,k.LOG_ERROR,"WebSocketTransport.send()","No socket connection")}onWsData(e){k.logAction(this.logger,k.LOG_MICRO,"WebSocketTransport.onWsData()","data received; length = "+e.length+"; type = "+typeof e);try{this.onProtocolMessage((t=e,s=this.connectionManager.realtime._MsgPack,n=this.connectionManager.realtime._RealtimePresence,a=this.connectionManager.realtime._Annotations,i=this.connectionManager.realtime._objectsPlugin,r=this.format,ds(ae(t,s,r),n,a,i)))}catch(o){k.logAction(this.logger,k.LOG_ERROR,"WebSocketTransport.onWsData()","Unexpected exception handing channel message: "+o.stack)}var t,s,n,a,i,r}onWsOpen(){k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.onWsOpen()","opened WebSocket"),this.emit("preconnect")}onWsClose(e){let t,s;if("object"==typeof e?(s=e.code,t=e.wasClean||1e3===s):(s=e,t=1e3==s),delete this.wsConnection,t){k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.onWsClose()","Cleanly closed WebSocket");const e=new E("Websocket closed",80003,400);this.finish("disconnected",e)}else{const e="Unclean disconnection of WebSocket ; code = "+s,t=new E(e,80003,400);k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.onWsClose()",e),this.finish("disconnected",t)}this.emit("disposed")}onWsError(e){k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.onError()","Error from WebSocket: "+e.message),g.Config.nextTick(()=>{this.disconnect(Error(e.message))})}dispose(){k.logAction(this.logger,k.LOG_MINOR,"WebSocketTransport.dispose()",""),this.isDisposed=!0;const e=this.wsConnection;e&&(e.onmessage=function(){},delete this.wsConnection,g.Config.nextTick(()=>{if(k.logAction(this.logger,k.LOG_MICRO,"WebSocketTransport.dispose()","closing websocket"),!e)throw new Error("WebSocketTransport.dispose(): wsConnection is not defined");e.close()}))}},en=class{static subscribeFilter(e,t,s){const n=e=>{var n,a,i,r,o,l;const c={name:e.name,refTimeserial:null==(a=null==(n=e.extras)?void 0:n.ref)?void 0:a.timeserial,refType:null==(r=null==(i=e.extras)?void 0:i.ref)?void 0:r.type,isRef:!!(null==(l=null==(o=e.extras)?void 0:o.ref)?void 0:l.timeserial),clientId:e.clientId};Object.entries(t).find(([e,t])=>void 0!==t&&c[e]!==t)||s(e)};this.addFilteredSubscription(e,t,s,n),e.subscriptions.on(n)}static addFilteredSubscription(e,t,s,n){var a;if(e.filteredSubscriptions||(e.filteredSubscriptions=new Map),e.filteredSubscriptions.has(s)){const i=e.filteredSubscriptions.get(s);i.set(t,(null==(a=null==i?void 0:i.get(t))?void 0:a.concat(n))||[n])}else e.filteredSubscriptions.set(s,new Map([[t,[n]]]))}static getAndDeleteFilteredSubscriptions(e,t,s){if(!e.filteredSubscriptions)return[];if(!s&&t)return Array.from(e.filteredSubscriptions.entries()).map(([s,n])=>{var a;let i=n.get(t);return n.delete(t),0===n.size&&(null==(a=e.filteredSubscriptions)||a.delete(s)),i}).reduce((e,t)=>t?e.concat(...t):e,[]);if(!s||!e.filteredSubscriptions.has(s))return[];const n=e.filteredSubscriptions.get(s);if(!t){const t=Array.from(n.values()).reduce((e,t)=>e.concat(...t),[]);return e.filteredSubscriptions.delete(s),t}let a=n.get(t);return n.delete(t),a||[]}},tn=class e extends zs{constructor(t){var s;const n=e._MsgPack;if(!n)throw new Error("Expected DefaultRealtime._MsgPack to have been set");super(Oe.objectifyOptions(t,!0,"Realtime",k.defaultLogger,f(p({},Kt),{Crypto:null!=(s=e.Crypto)?s:void 0,MsgPack:n,RealtimePresence:{RealtimePresence:Qs,PresenceMessage:It,WirePresenceMessage:Ot},Annotations:{Annotation:ns,WireAnnotation:ss,RealtimeAnnotations:_s,RestAnnotations:ls},WebSocketTransport:Zs,MessageInteractions:en})))}static get Crypto(){if(null===this._Crypto)throw new Error("Encryption not enabled; use ably.encryption.js instead");return this._Crypto}static set Crypto(e){this._Crypto=e}};tn.Utils=C,tn.ConnectionManager=Gs,tn.ProtocolMessage=ms,tn._Crypto=null,tn.Message=Yt,tn.PresenceMessage=Qt,tn.Annotation=as,tn._MsgPack=null,tn._Http=Fe,tn._PresenceMap=Ks,tn._MessageEncoding=rt;var sn=tn,nn=Uint8Array,an=Uint32Array,rn=Math.pow,on=new an(8),ln=[],cn=new an(64);function un(e){return(e-(0|e))*rn(2,32)|0}for(var dn,hn,pn=2,fn=0;fn<64;){for(dn=!0,hn=2;hn<=pn/2;hn++)pn%hn===0&&(dn=!1);dn&&(fn<8&&(on[fn]=un(rn(pn,.5))),ln[fn]=un(rn(pn,1/3)),fn++),pn++}var vn=!!new nn(new an([1]).buffer)[0];function mn(e){return vn?e>>>24|(e>>>16&255)<<8|(65280&e)<<8|e<<24:e}function gn(e,t){return e>>>t|e<<32-t}function yn(e){var t,s=on.slice(),n=e.length,a=8*n,i=512-(a+64)%512-1+a+65,r=new nn(i/8),o=new an(r.buffer);r.set(e,0),r[n]=128,o[o.length-1]=mn(a);for(var l=0;l<i/32;l+=16){var c=s.slice();for(t=0;t<64;t++){var u;if(t<16)u=mn(o[l+t]);else{var d=cn[t-15],h=cn[t-2];u=cn[t-7]+cn[t-16]+(gn(d,7)^gn(d,18)^d>>>3)+(gn(h,17)^gn(h,19)^h>>>10)}cn[t]=u|=0;for(var p=(gn(c[4],6)^gn(c[4],11)^gn(c[4],25))+(c[4]&c[5]^~c[4]&c[6])+c[7]+u+ln[t],f=(gn(c[0],2)^gn(c[0],13)^gn(c[0],22))+(c[0]&c[1]^c[2]&(c[0]^c[1])),v=7;v>0;v--)c[v]=c[v-1];c[0]=p+f|0,c[4]=c[4]+p|0}for(t=0;t<8;t++)s[t]=s[t]+c[t]|0}return new nn(new an(s.map(function(e){return mn(e)})).buffer)}var bn,wn=new class{constructor(){this.base64CharSet="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",this.hexCharSet="0123456789abcdef"}uint8ViewToBase64(e){let t="";const s=this.base64CharSet,n=e.byteLength,a=n%3,i=n-a;let r,o,l,c,u;for(let d=0;d<i;d+=3)u=e[d]<<16|e[d+1]<<8|e[d+2],r=(16515072&u)>>18,o=(258048&u)>>12,l=(4032&u)>>6,c=63&u,t+=s[r]+s[o]+s[l]+s[c];return 1==a?(u=e[i],r=(252&u)>>2,o=(3&u)<<4,t+=s[r]+s[o]+"=="):2==a&&(u=e[i]<<8|e[i+1],r=(64512&u)>>10,o=(1008&u)>>4,l=(15&u)<<2,t+=s[r]+s[o]+s[l]+"="),t}base64ToArrayBuffer(e){const t=null==atob?void 0:atob(e),s=t.length,n=new Uint8Array(s);for(let a=0;a<s;a++){const e=t.charCodeAt(a);n[a]=e}return this.toArrayBuffer(n)}isBuffer(e){return e instanceof ArrayBuffer||ArrayBuffer.isView(e)}toBuffer(e){if(!ArrayBuffer)throw new Error("Can't convert to Buffer: browser does not support the necessary types");if(e instanceof ArrayBuffer)return new Uint8Array(e);if(ArrayBuffer.isView(e))return new Uint8Array(this.toArrayBuffer(e));throw new Error("BufferUtils.toBuffer expected an ArrayBuffer or a view onto one")}toArrayBuffer(e){if(!ArrayBuffer)throw new Error("Can't convert to ArrayBuffer: browser does not support the necessary types");if(e instanceof ArrayBuffer)return e;if(ArrayBuffer.isView(e))return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength);throw new Error("BufferUtils.toArrayBuffer expected an ArrayBuffer or a view onto one")}base64Encode(e){return this.uint8ViewToBase64(this.toBuffer(e))}base64UrlEncode(e){return this.base64Encode(e).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}base64Decode(e){if(ArrayBuffer&&g.Config.atob)return this.base64ToArrayBuffer(e);throw new Error("Expected ArrayBuffer to exist and Platform.Config.atob to be configured")}hexEncode(e){return this.toBuffer(e).reduce((e,t)=>e+t.toString(16).padStart(2,"0"),"")}hexDecode(e){if(e.length%2!=0)throw new Error("Can't create a byte array from a hex string of odd length");const t=new Uint8Array(e.length/2);for(let s=0;s<t.length;s++)t[s]=parseInt(e.slice(2*s,2*(s+1)),16);return this.toArrayBuffer(t)}utf8Encode(e){if(g.Config.TextEncoder){const t=(new g.Config.TextEncoder).encode(e);return this.toArrayBuffer(t)}throw new Error("Expected TextEncoder to be configured")}utf8Decode(e){if(!this.isBuffer(e))throw new Error("Expected input of utf8decode to be an arraybuffer or typed array");if(TextDecoder)return(new TextDecoder).decode(e);throw new Error("Expected TextDecoder to be configured")}areBuffersEqual(e,t){if(!e||!t)return!1;const s=this.toArrayBuffer(e),n=this.toArrayBuffer(t);if(s.byteLength!=n.byteLength)return!1;const a=new Uint8Array(s),i=new Uint8Array(n);for(var r=0;r<a.length;r++)if(a[r]!=i[r])return!1;return!0}byteLength(e){return e instanceof ArrayBuffer||ArrayBuffer.isView(e)?e.byteLength:-1}arrayBufferViewToBuffer(e){return this.toArrayBuffer(e)}concat(e){const t=e.reduce((e,t)=>e+t.byteLength,0),s=new Uint8Array(t);let n=0;for(const a of e){const e=this.toBuffer(a);s.set(e,n),n+=e.byteLength}return s.buffer}sha256(e){const t=yn(this.toBuffer(e));return this.toArrayBuffer(t)}hmacSha256(e,t){const s=function(e,t){if(e.length>64&&(e=yn(e)),e.length<64){const t=new Uint8Array(64);t.set(e,0),e=t}for(var s=new Uint8Array(64),n=new Uint8Array(64),a=0;a<64;a++)s[a]=54^e[a],n[a]=92^e[a];var i=new Uint8Array(t.length+64);i.set(s,0),i.set(t,64);var r=new Uint8Array(96);return r.set(n,0),r.set(yn(i),64),yn(r)}(this.toBuffer(t),this.toBuffer(e));return this.toArrayBuffer(s)}},_n=(e=>(e[e.REQ_SEND=0]="REQ_SEND",e[e.REQ_RECV=1]="REQ_RECV",e[e.REQ_RECV_POLL=2]="REQ_RECV_POLL",e[e.REQ_RECV_STREAM=3]="REQ_RECV_STREAM",e))(_n||{}),kn=_n;function Cn(){return new E("No HTTP request plugin provided. Provide at least one of the FetchRequest or XHRRequest plugins.",400,4e4)}var Sn=((bn=class{constructor(e){var t;this.checksInProgress=null,this.checkConnectivity=void 0,this.supportsAuthHeaders=!1,this.supportsLinkHeaders=!1,this.client=null!=e?e:null;const s=(null==e?void 0:e.options.connectivityCheckUrl)||Oe.connectivityCheckUrl,n=null!=(t=null==e?void 0:e.options.connectivityCheckParams)?t:null,a=!(null==e?void 0:e.options.connectivityCheckUrl),i=p(p({},Sn.bundledRequestImplementations),null==e?void 0:e._additionalHTTPRequestImplementations),r=i.XHRRequest,o=i.FetchRequest,l=!(!r&&!o);if(!l)throw Cn();g.Config.xhrSupported&&r?(this.supportsAuthHeaders=!0,this.Request=async function(t,s,n,a,i){return new Promise(o=>{var l;const c=r.createRequest(s,n,a,i,kn.REQ_SEND,null!=(l=e&&e.options.timeouts)?l:null,this.logger,t);c.once("complete",(e,t,s,n,a)=>o({error:e,body:t,headers:s,unpacked:n,statusCode:a})),c.exec()})},(null==e?void 0:e.options.disableConnectivityCheck)?this.checkConnectivity=async function(){return!0}:this.checkConnectivity=async function(){var e;k.logAction(this.logger,k.LOG_MICRO,"(XHRRequest)Http.checkConnectivity()","Sending; "+s);const t=await this.doUri(Le.Get,s,null,null,n);let i=!1;var r;return i=a?!t.error&&"yes"==(null==(e=t.body)?void 0:e.replace(/\n/,"")):!t.error&&(r=t.statusCode)>=200&&r<400,k.logAction(this.logger,k.LOG_MICRO,"(XHRRequest)Http.checkConnectivity()","Result: "+i),i}):g.Config.fetchSupported&&o?(this.supportsAuthHeaders=!0,this.Request=async(t,s,n,a,i)=>o(t,null!=e?e:null,s,n,a,i),(null==e?void 0:e.options.disableConnectivityCheck)?this.checkConnectivity=async function(){return!0}:this.checkConnectivity=async function(){var e;k.logAction(this.logger,k.LOG_MICRO,"(Fetch)Http.checkConnectivity()","Sending; "+s);const t=await this.doUri(Le.Get,s,null,null,null),n=!t.error&&"yes"==(null==(e=t.body)?void 0:e.replace(/\n/,""));return k.logAction(this.logger,k.LOG_MICRO,"(Fetch)Http.checkConnectivity()","Result: "+n),n}):this.Request=async()=>({error:l?new R("no supported HTTP transports available",null,400):Cn()})}get logger(){var e,t;return null!=(t=null==(e=this.client)?void 0:e.logger)?t:k.defaultLogger}async doUri(e,t,s,n,a){return this.Request?this.Request(e,t,s,a,n):{error:new R("Request invoked before assigned to",null,500)}}shouldFallback(e){const t=e.statusCode;return 408===t&&!e.code||400===t&&!e.code||t>=500&&t<=504}}).methods=[Le.Get,Le.Delete,Le.Post,Le.Put,Le.Patch],bn.methodsWithoutBody=[Le.Get,Le.Delete],bn.methodsWithBody=[Le.Post,Le.Put,Le.Patch],bn),En=Sn,Rn="ablyjs-storage-test",Tn=void 0!==s?s:"undefined"!=typeof window?window:self,An=new class{constructor(){try{Tn.sessionStorage.setItem(Rn,Rn),Tn.sessionStorage.removeItem(Rn),this.sessionSupported=!0}catch(e){this.sessionSupported=!1}try{Tn.localStorage.setItem(Rn,Rn),Tn.localStorage.removeItem(Rn),this.localSupported=!0}catch(e){this.localSupported=!1}}get(e){return this._get(e,!1)}getSession(e){return this._get(e,!0)}remove(e){return this._remove(e,!1)}removeSession(e){return this._remove(e,!0)}set(e,t,s){return this._set(e,t,s,!1)}setSession(e,t,s){return this._set(e,t,s,!0)}_set(e,t,s,n){const a={value:t};return s&&(a.expires=Date.now()+s),this.storageInterface(n).setItem(e,JSON.stringify(a))}_get(e,t){if(t&&!this.sessionSupported)throw new Error("Session Storage not supported");if(!t&&!this.localSupported)throw new Error("Local Storage not supported");const s=this.storageInterface(t).getItem(e);if(!s)return null;const n=JSON.parse(s);return n.expires&&n.expires<Date.now()?(this.storageInterface(t).removeItem(e),null):n.value}_remove(e,t){return this.storageInterface(t).removeItem(e)}storageInterface(e){return e?Tn.sessionStorage:Tn.localStorage}},On=de(),In={agent:"browser",logTimestamps:!0,userAgent:On.navigator&&On.navigator.userAgent.toString(),currentUrl:On.location&&On.location.href,binaryType:"arraybuffer",WebSocket:On.WebSocket,fetchSupported:!!On.fetch,xhrSupported:On.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest,allowComet:function(){const e=On.location;return!On.WebSocket||!e||!e.origin||e.origin.indexOf("http")>-1}(),useProtocolHeartbeats:!0,supportsBinary:!!On.TextDecoder,preferBinary:!1,ArrayBuffer:On.ArrayBuffer,atob:On.atob,nextTick:void 0!==On.setImmediate?On.setImmediate.bind(On):function(e){setTimeout(e,0)},addEventListener:On.addEventListener,inspect:JSON.stringify,stringByteSize:function(e){return On.TextDecoder&&(new On.TextEncoder).encode(e).length||e.length},TextEncoder:On.TextEncoder,TextDecoder:On.TextDecoder,getRandomArrayBuffer:async function(e){const t=new Uint8Array(e);return On.crypto.getRandomValues(t),t.buffer},isWebworker:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope,push:{platform:"browser",formFactor:"desktop",storage:An}};function Mn(e){return function(e){const t=[80015,80017,80030];return!!e.code&&!He.isTokenErr(e)&&(!!t.includes(e.code)||e.code>=4e4&&e.code<5e4)}(e)?[ps({action:ze.ERROR,error:e})]:[ps({action:ze.DISCONNECTED,error:e})]}var Ln=class extends Ns{constructor(e,t,s){super(e,t,s,!0),this.onAuthUpdated=e=>{this.authParams={access_token:e.token}},this.stream=!("stream"in s)||s.stream,this.sendRequest=null,this.recvRequest=null,this.pendingCallback=null,this.pendingItems=null}connect(){k.logAction(this.logger,k.LOG_MINOR,"CometTransport.connect()","starting"),Ns.prototype.connect.call(this);const e=this.params,t=e.options,s=Oe.getHost(t,e.host),n=Oe.getPort(t),a=t.tls?"https://":"http://";this.baseUri=a+s+":"+n+"/comet/";const i=this.baseUri+"connect";k.logAction(this.logger,k.LOG_MINOR,"CometTransport.connect()","uri: "+i),ne(this.auth.getAuthParams(),(e,t)=>{if(e)return void this.disconnect(e);if(this.isDisposed)return;this.authParams=t;const s=this.params.getConnectParams(t);"stream"in s&&(this.stream=s.stream),k.logAction(this.logger,k.LOG_MINOR,"CometTransport.connect()","connectParams:"+J(s));let n=!1;const a=this.recvRequest=this.createRequest(i,null,s,null,this.stream?kn.REQ_RECV_STREAM:kn.REQ_RECV);a.on("data",e=>{this.recvRequest&&(n||(n=!0,this.emit("preconnect")),this.onData(e))}),a.on("complete",e=>{this.recvRequest||(e=e||new E("Request cancelled",80003,400)),this.recvRequest=null,n||e||(n=!0,this.emit("preconnect")),this.onActivity(),e?e.code?this.onData(Mn(e)):this.disconnect(e):g.Config.nextTick(()=>{this.recv()})}),a.exec()})}requestClose(){k.logAction(this.logger,k.LOG_MINOR,"CometTransport.requestClose()"),this._requestCloseOrDisconnect(!0)}requestDisconnect(){k.logAction(this.logger,k.LOG_MINOR,"CometTransport.requestDisconnect()"),this._requestCloseOrDisconnect(!1)}_requestCloseOrDisconnect(e){const t=e?this.closeUri:this.disconnectUri;if(t){const s=this.createRequest(t,null,this.authParams,null,kn.REQ_SEND);s.on("complete",t=>{t&&(k.logAction(this.logger,k.LOG_ERROR,"CometTransport.request"+(e?"Close()":"Disconnect()"),"request returned err = "+Q(t)),this.finish("disconnected",t))}),s.exec()}}dispose(){k.logAction(this.logger,k.LOG_MINOR,"CometTransport.dispose()",""),this.isDisposed||(this.isDisposed=!0,this.recvRequest&&(k.logAction(this.logger,k.LOG_MINOR,"CometTransport.dispose()","aborting recv request"),this.recvRequest.abort(),this.recvRequest=null),this.finish("disconnected",Ms.disconnected()),g.Config.nextTick(()=>{this.emit("disposed")}))}onConnect(e){var t;if(this.isDisposed)return;const s=null==(t=e.connectionDetails)?void 0:t.connectionKey;Ns.prototype.onConnect.call(this,e);const n=this.baseUri+s;k.logAction(this.logger,k.LOG_MICRO,"CometTransport.onConnect()","baseUri = "+n),this.sendUri=n+"/send",this.recvUri=n+"/recv",this.closeUri=n+"/close",this.disconnectUri=n+"/disconnect"}send(e){if(this.sendRequest)return this.pendingItems=this.pendingItems||[],void this.pendingItems.push(e);const t=this.pendingItems||[];t.push(e),this.pendingItems=null,this.sendItems(t)}sendAnyPending(){const e=this.pendingItems;e&&(this.pendingItems=null,this.sendItems(e))}sendItems(e){const t=this.sendRequest=this.createRequest(this.sendUri,null,this.authParams,this.encodeRequest(e),kn.REQ_SEND);t.on("complete",(e,t)=>{e&&k.logAction(this.logger,k.LOG_ERROR,"CometTransport.sendItems()","on complete: err = "+Q(e)),this.sendRequest=null,e?e.code?this.onData(Mn(e)):this.disconnect(e):(t&&this.onData(t),this.pendingItems&&g.Config.nextTick(()=>{this.sendRequest||this.sendAnyPending()}))}),t.exec()}recv(){if(this.recvRequest)return;if(!this.isConnected)return;const e=this.recvRequest=this.createRequest(this.recvUri,null,this.authParams,null,this.stream?kn.REQ_RECV_STREAM:kn.REQ_RECV_POLL);e.on("data",e=>{this.onData(e)}),e.on("complete",e=>{this.recvRequest=null,this.onActivity(),e?e.code?this.onData(Mn(e)):this.disconnect(e):g.Config.nextTick(()=>{this.recv()})}),e.exec()}onData(e){try{const t=this.decodeResponse(e);if(t&&t.length)for(let e=0;e<t.length;e++)this.onProtocolMessage(ds(t[e],this.connectionManager.realtime._RealtimePresence,this.connectionManager.realtime._Annotations,this.connectionManager.realtime._objectsPlugin))}catch(t){k.logAction(this.logger,k.LOG_ERROR,"CometTransport.onData()","Unexpected exception handing channel event: "+t.stack)}}encodeRequest(e){return JSON.stringify(e)}decodeResponse(e){return"string"==typeof e?JSON.parse(e):e}};function Pn(e,t){if(function(e,t){return re(G(t)).includes("x-ably-errorcode")}(0,t))return e.error&&E.fromValues(e.error)}var Nn=function(){},Un=0,xn={},Dn=class e extends We{constructor(e,t,s,n,a,i,r,o){super(r),(s=s||{}).rnd=ee(),this.uri=e+J(s),this.headers=t||{},this.body=n,this.method=o?o.toUpperCase():L(n)?"GET":"POST",this.requestMode=a,this.timeouts=i,this.timedOut=!1,this.requestComplete=!1,this.id=String(++Un),xn[this.id]=this}static createRequest(t,s,n,a,i,r,o,l){const c=r||Oe.TIMEOUTS;return new e(t,s,A(n),a,i,c,o,l)}complete(e,t,s,n,a){this.requestComplete||(this.requestComplete=!0,!e&&t&&this.emit("data",t),this.emit("complete",e,t,s,n,a),this.dispose())}abort(){this.dispose()}exec(){let e=this.headers;const t=this.requestMode==kn.REQ_SEND?this.timeouts.httpRequestTimeout:this.timeouts.recvTimeout,s=this.timer=setTimeout(()=>{this.timedOut=!0,a.abort()},t),n=this.method,a=this.xhr=new XMLHttpRequest,i=e.accept;let r=this.body,o="text";i?0===i.indexOf("application/x-msgpack")&&(o="arraybuffer"):e.accept="application/json",r&&(e["content-type"]||(e["content-type"]="application/json")).indexOf("application/json")>-1&&"string"!=typeof r&&(r=JSON.stringify(r)),a.open(n,this.uri,!0),a.responseType=o,"authorization"in e&&(a.withCredentials=!0);for(const g in e)a.setRequestHeader(g,e[g]);const l=(e,t,s,n)=>{var a;let i=t+" (event type: "+e.type+")";(null==(a=null==this?void 0:this.xhr)?void 0:a.statusText)&&(i+=", current statusText is "+this.xhr.statusText),k.logAction(this.logger,k.LOG_ERROR,"Request.on"+e.type+"()",i),this.complete(new R(i,s,n))};let c,u,d;a.onerror=function(e){l(e,"XHR error occurred",null,400)},a.onabort=e=>{this.timedOut?l(e,"Request aborted due to request timeout expiring",null,408):l(e,"Request cancelled",null,400)},a.ontimeout=function(e){l(e,"Request timed out",null,408)};let h=0,p=!1;const f=()=>{clearTimeout(s),d=u<400,204!=u?c=this.requestMode==kn.REQ_RECV_STREAM&&d&&function(e){return e.getResponseHeader&&(e.getResponseHeader("transfer-encoding")||!e.getResponseHeader("content-length"))}(a):this.complete(null,null,null,null,u)},v=()=>{let t;try{const s=function(e,t){return e.getResponseHeader&&e.getResponseHeader(t)}(a,"content-type");if(s?s.indexOf("application/json")>=0:"text"==a.responseType){const e="arraybuffer"===a.responseType?g.BufferUtils.utf8Decode(a.response):String(a.responseText);t=e.length?JSON.parse(e):e,p=!0}else t=a.response;void 0!==t.response?(u=t.statusCode,d=u<400,e=t.headers,t=t.response):e=function(e){const t=e.getAllResponseHeaders().trim().split("\r\n"),s={};for(let n=0;n<t.length;n++){const e=t[n].split(":").map(e=>e.trim());s[e[0].toLowerCase()]=e[1]}return s}(a)}catch(n){return void this.complete(new R("Malformed response body from server: "+n.message,null,400))}if(d||Array.isArray(t))return void this.complete(null,t,e,p,u);let s=Pn(t,e);s||(s=new R("Error response received from server: "+u+" body was: "+g.Config.inspect(t),null,u)),this.complete(s,t,e,p,u)};function m(){const e=a.responseText,t=e.length-1;let s,n;for(;h<t&&(s=e.indexOf("\n",h))>-1;)n=e.slice(h,s),h=s+1,y(n)}const y=e=>{try{e=JSON.parse(e)}catch(t){return void this.complete(new R("Malformed response body from server: "+t.message,null,400))}this.emit("data",e)},b=()=>{m(),this.streamComplete=!0,g.Config.nextTick(()=>{this.complete()})};a.onreadystatechange=function(){const e=a.readyState;e<3||0!==a.status&&(void 0===u&&(u=a.status,f()),3==e&&c?m():4==e&&(c?b():v()))},a.send(r)}dispose(){const e=this.xhr;if(e){e.onreadystatechange=e.onerror=e.onabort=e.ontimeout=Nn,this.xhr=null;const t=this.timer;t&&(clearTimeout(t),this.timer=null),this.requestComplete||e.abort()}delete xn[this.id]}},Bn=Cs.XhrPolling,Vn={order:["xhr_polling"],bundledImplementations:{web_socket:Zs,xhr_polling:class extends Ln{constructor(e,t,s){super(e,t,s),this.shortName=Bn,s.stream=!1,this.shortName=Bn}static isAvailable(){return!(!g.Config.xhrSupported||!g.Config.allowComet)}toString(){return"XHRPollingTransport; uri="+this.baseUri+"; isConnected="+this.isConnected}createRequest(e,t,s,n,a){return Dn.createRequest(e,t,s,n,a,this.timeouts,this.logger)}}}},qn={connectivityCheckUrl:"https://internet-up.ably-realtime.com/is-the-internet-up.txt",wsConnectivityCheckUrl:"wss://ws-up.ably-realtime.com",defaultTransports:[Cs.XhrPolling,Cs.WebSocket]};function Hn(e,t,s){for(let n=0,a=s.length;n<a;n++){const a=s.charCodeAt(n);if(a<128)e.setUint8(t++,a>>>0&127);else if(a<2048)e.setUint8(t++,a>>>6&31|192),e.setUint8(t++,a>>>0&63|128);else if(a<65536)e.setUint8(t++,a>>>12&15|224),e.setUint8(t++,a>>>6&63|128),e.setUint8(t++,a>>>0&63|128);else{if(!(a<1114112))throw new Error("bad codepoint "+a);e.setUint8(t++,a>>>18&7|240),e.setUint8(t++,a>>>12&63|128),e.setUint8(t++,a>>>6&63|128),e.setUint8(t++,a>>>0&63|128)}}}function Gn(e,t,s){let n="";for(let a=t,i=t+s;a<i;a++){const t=e.getUint8(a);if(128&t)if(192!=(224&t))if(224!=(240&t)){if(240!=(248&t))throw new Error("Invalid byte "+t.toString(16));n+=String.fromCharCode((7&t)<<18|(63&e.getUint8(++a))<<12|(63&e.getUint8(++a))<<6|63&e.getUint8(++a))}else n+=String.fromCharCode((15&t)<<12|(63&e.getUint8(++a))<<6|63&e.getUint8(++a));else n+=String.fromCharCode((15&t)<<6|63&e.getUint8(++a));else n+=String.fromCharCode(t)}return n}function jn(e){let t=0;for(let s=0,n=e.length;s<n;s++){const n=e.charCodeAt(s);if(n<128)t+=1;else if(n<2048)t+=2;else if(n<65536)t+=3;else{if(!(n<1114112))throw new Error("bad codepoint "+n);t+=4}}return t}var Fn=4294967296,$n=1/Fn,Wn=class{constructor(e,t){this.map=e=>{const t={};for(let s=0;s<e;s++)t[this.parse()]=this.parse();return t},this.bin=e=>{const t=new ArrayBuffer(e);return new Uint8Array(t).set(new Uint8Array(this.view.buffer,this.offset,e),0),this.offset+=e,t},this.buf=this.bin,this.str=e=>{const t=Gn(this.view,this.offset,e);return this.offset+=e,t},this.array=e=>{const t=new Array(e);for(let s=0;s<e;s++)t[s]=this.parse();return t},this.ext=e=>(this.offset+=e,{type:this.view.getInt8(this.offset),data:this.buf(e)}),this.parse=()=>{const e=this.view.getUint8(this.offset);let t,s;if(!(128&e))return this.offset++,e;if(128==(240&e))return s=15&e,this.offset++,this.map(s);if(144==(240&e))return s=15&e,this.offset++,this.array(s);if(160==(224&e))return s=31&e,this.offset++,this.str(s);if(!(224&~e))return t=this.view.getInt8(this.offset),this.offset++,t;switch(e){case 192:return this.offset++,null;case 193:return void this.offset++;case 194:return this.offset++,!1;case 195:return this.offset++,!0;case 196:return s=this.view.getUint8(this.offset+1),this.offset+=2,this.bin(s);case 197:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.bin(s);case 198:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.bin(s);case 199:return s=this.view.getUint8(this.offset+1),this.offset+=2,this.ext(s);case 200:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.ext(s);case 201:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.ext(s);case 202:return t=this.view.getFloat32(this.offset+1),this.offset+=5,t;case 203:return t=this.view.getFloat64(this.offset+1),this.offset+=9,t;case 204:return t=this.view.getUint8(this.offset+1),this.offset+=2,t;case 205:return t=this.view.getUint16(this.offset+1),this.offset+=3,t;case 206:return t=this.view.getUint32(this.offset+1),this.offset+=5,t;case 207:return t=function(e,t){return t=t||0,e.getUint32(t)*Fn+e.getUint32(t+4)}(this.view,this.offset+1),this.offset+=9,t;case 208:return t=this.view.getInt8(this.offset+1),this.offset+=2,t;case 209:return t=this.view.getInt16(this.offset+1),this.offset+=3,t;case 210:return t=this.view.getInt32(this.offset+1),this.offset+=5,t;case 211:return t=function(e,t){return t=t||0,e.getInt32(t)*Fn+e.getUint32(t+4)}(this.view,this.offset+1),this.offset+=9,t;case 212:return s=1,this.offset++,this.ext(s);case 213:return s=2,this.offset++,this.ext(s);case 214:return s=4,this.offset++,this.ext(s);case 215:return s=8,this.offset++,this.ext(s);case 216:return s=16,this.offset++,this.ext(s);case 217:return s=this.view.getUint8(this.offset+1),this.offset+=2,this.str(s);case 218:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.str(s);case 219:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.str(s);case 220:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.array(s);case 221:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.array(s);case 222:return s=this.view.getUint16(this.offset+1),this.offset+=3,this.map(s);case 223:return s=this.view.getUint32(this.offset+1),this.offset+=5,this.map(s)}throw new Error("Unknown type 0x"+e.toString(16))},this.offset=t||0,this.view=e}};function zn(e,t){return Object.keys(e).filter(function(s){const n=e[s];return!(t&&null==n||"function"==typeof n&&!n.toJSON)})}function Jn(e,t,s,n){const a=typeof e;if("string"==typeof e){const n=jn(e);if(n<32)return t.setUint8(s,160|n),Hn(t,s+1,e),1+n;if(n<256)return t.setUint8(s,217),t.setUint8(s+1,n),Hn(t,s+2,e),2+n;if(n<65536)return t.setUint8(s,218),t.setUint16(s+1,n),Hn(t,s+3,e),3+n;if(n<4294967296)return t.setUint8(s,219),t.setUint32(s+1,n),Hn(t,s+5,e),5+n}if(ArrayBuffer.isView&&ArrayBuffer.isView(e)&&(e=e.buffer),e instanceof ArrayBuffer){const n=e.byteLength;if(n<256)return t.setUint8(s,196),t.setUint8(s+1,n),new Uint8Array(t.buffer).set(new Uint8Array(e),s+2),2+n;if(n<65536)return t.setUint8(s,197),t.setUint16(s+1,n),new Uint8Array(t.buffer).set(new Uint8Array(e),s+3),3+n;if(n<4294967296)return t.setUint8(s,198),t.setUint32(s+1,n),new Uint8Array(t.buffer).set(new Uint8Array(e),s+5),5+n}if("number"==typeof e){if(Math.floor(e)!==e)return t.setUint8(s,203),t.setFloat64(s+1,e),9;if(e>=0){if(e<128)return t.setUint8(s,e),1;if(e<256)return t.setUint8(s,204),t.setUint8(s+1,e),2;if(e<65536)return t.setUint8(s,205),t.setUint16(s+1,e),3;if(e<4294967296)return t.setUint8(s,206),t.setUint32(s+1,e),5;if(e<0x10000000000000000)return t.setUint8(s,207),function(e,t,s){s<0x10000000000000000?(e.setUint32(t,Math.floor(s*$n)),e.setInt32(t+4,-1&s)):(e.setUint32(t,4294967295),e.setUint32(t+4,4294967295))}(t,s+1,e),9;throw new Error("Number too big 0x"+e.toString(16))}if(e>=-32)return t.setInt8(s,e),1;if(e>=-128)return t.setUint8(s,208),t.setInt8(s+1,e),2;if(e>=-32768)return t.setUint8(s,209),t.setInt16(s+1,e),3;if(e>=-2147483648)return t.setUint8(s,210),t.setInt32(s+1,e),5;if(e>=-0x8000000000000000)return t.setUint8(s,211),function(e,t,s){s<0x8000000000000000?(e.setInt32(t,Math.floor(s*$n)),e.setInt32(t+4,-1&s)):(e.setUint32(t,2147483647),e.setUint32(t+4,2147483647))}(t,s+1,e),9;throw new Error("Number too small -0x"+(-e).toString(16).substr(1))}if("undefined"===a)return n?0:(t.setUint8(s,212),t.setUint8(s+1,0),t.setUint8(s+2,0),3);if(null===e)return n?0:(t.setUint8(s,192),1);if("boolean"===a)return t.setUint8(s,e?195:194),1;if("function"==typeof e.toJSON)return Jn(e.toJSON(),t,s,n);if("object"===a){let a,i,r=0;const o=Array.isArray(e);if(o?a=e.length:(i=zn(e,n),a=i.length),a<16?(t.setUint8(s,a|(o?144:128)),r=1):a<65536?(t.setUint8(s,o?220:222),t.setUint16(s+1,a),r=3):a<4294967296&&(t.setUint8(s,o?221:223),t.setUint32(s+1,a),r=5),o)for(let l=0;l<a;l++)r+=Jn(e[l],t,s+r,n);else if(i)for(let l=0;l<a;l++){const a=i[l];r+=Jn(a,t,s+r),r+=Jn(e[a],t,s+r,n)}return r}if("function"===a)return 0;throw new Error("Unknown type "+a)}function Kn(e,t){const s=typeof e;if("string"===s){const t=jn(e);if(t<32)return 1+t;if(t<256)return 2+t;if(t<65536)return 3+t;if(t<4294967296)return 5+t}if(ArrayBuffer.isView&&ArrayBuffer.isView(e)&&(e=e.buffer),e instanceof ArrayBuffer){const t=e.byteLength;if(t<256)return 2+t;if(t<65536)return 3+t;if(t<4294967296)return 5+t}if("number"==typeof e){if(Math.floor(e)!==e)return 9;if(e>=0){if(e<128)return 1;if(e<256)return 2;if(e<65536)return 3;if(e<4294967296)return 5;if(e<0x10000000000000000)return 9;throw new Error("Number too big 0x"+e.toString(16))}if(e>=-32)return 1;if(e>=-128)return 2;if(e>=-32768)return 3;if(e>=-2147483648)return 5;if(e>=-0x8000000000000000)return 9;throw new Error("Number too small -0x"+e.toString(16).substr(1))}if("boolean"===s)return 1;if(null===e)return t?0:1;if(void 0===e)return t?0:3;if("function"==typeof e.toJSON)return Kn(e.toJSON(),t);if("object"===s){let s,n=0;if(Array.isArray(e)){s=e.length;for(let a=0;a<s;a++)n+=Kn(e[a],t)}else{const a=zn(e,t);s=a.length;for(let i=0;i<s;i++){const s=a[i];n+=Kn(s)+Kn(e[s],t)}}if(s<16)return 1+n;if(s<65536)return 3+n;if(s<4294967296)return 5+n;throw new Error("Array or object too long 0x"+s.toString(16))}if("function"===s)return 0;throw new Error("Unknown type "+s)}var Yn,Qn={encode:function(e,t){const s=Kn(e,t);if(0===s)return;const n=new ArrayBuffer(s);return Jn(e,new DataView(n),0,t),n},decode:function(e){const t=new DataView(e),s=new Wn(t),n=s.parse();if(s.offset!==e.byteLength)throw new Error(e.byteLength-s.offset+" trailing bytes");return n},inspect:function(e){if(void 0===e)return"undefined";let t,s;if(e instanceof ArrayBuffer?(s="ArrayBuffer",t=new DataView(e)):e instanceof DataView&&(s="DataView",t=e),!t)return JSON.stringify(e);const n=[];for(let a=0;a<e.byteLength;a++){if(a>20){n.push("...");break}let e=t.getUint8(a).toString(16);1===e.length&&(e="0"+e),n.push(e)}return"<"+s+" "+n.join(" ")+">"},utf8Write:Hn,utf8Read:Gn,utf8ByteCount:jn},Xn={XHRRequest:Dn,FetchRequest:async function(e,t,s,n,a,i){const r=new Headers(n||{}),o=e?e.toUpperCase():L(i)?"GET":"POST",l=new AbortController;let c;const u=new Promise(e=>{c=setTimeout(()=>{l.abort(),e({error:new R("Request timed out",null,408)})},t?t.options.timeouts.httpRequestTimeout:Oe.TIMEOUTS.httpRequestTimeout)}),d={method:o,headers:r,body:i,signal:l.signal};g.Config.isWebworker||(d.credentials=r.has("authorization")?"include":"same-origin");const h=(async()=>{try{const e=new URLSearchParams(a||{});e.set("rnd",ee());const t=s+"?"+e,n=await de().fetch(t,d);if(clearTimeout(c),204==n.status)return{error:null,statusCode:n.status};const i=n.headers.get("Content-Type");let r;r=i&&i.indexOf("application/x-msgpack")>-1?await n.arrayBuffer():i&&i.indexOf("application/json")>-1?await n.json():await n.text();const o=!!i&&-1===i.indexOf("application/x-msgpack"),l=function(e){const t={};return e.forEach((e,s)=>{t[s]=e}),t}(n.headers);if(n.ok)return{error:null,body:r,headers:l,unpacked:o,statusCode:n.status};{const e=function(e,t){if(function(e,t){return!!t.get("x-ably-errorcode")}(0,t))return e.error&&E.fromValues(e.error)}(r,n.headers)||new R("Error response received from server: "+n.status+" body was: "+g.Config.inspect(r),null,n.status);return{error:e,body:r,headers:l,unpacked:o,statusCode:n.status}}}catch(e){return clearTimeout(c),{error:e}}})();return Promise.race([u,h])}},Zn=function(e,t){class s{constructor(e,t,s,n){this.algorithm=e,this.keyLength=t,this.mode=s,this.key=n}}class n{static getDefaultParams(e){var n;if(!e.key)throw new Error("Crypto.getDefaultParams: a key is required");n="string"==typeof e.key?t.toArrayBuffer(t.base64Decode(e.key.replace("_","/").replace("-","+"))):e.key instanceof ArrayBuffer?e.key:t.toArrayBuffer(e.key);var a=e.algorithm||"aes",i=8*n.byteLength,r=e.mode||"cbc",o=new s(a,i,r,n);if(e.keyLength&&e.keyLength!==o.keyLength)throw new Error("Crypto.getDefaultParams: a keyLength of "+e.keyLength+" was specified, but the key actually has length "+o.keyLength);return function(e){if("aes"===e.algorithm&&"cbc"===e.mode){if(128===e.keyLength||256===e.keyLength)return;throw new Error("Unsupported key length "+e.keyLength+" for aes-cbc encryption. Encryption key must be 128 or 256 bits (16 or 32 ASCII characters)")}}(o),o}static async generateRandomKey(t){try{return e.getRandomArrayBuffer((t||256)/8)}catch(s){throw new E("Failed to generate random key: "+s.message,400,5e4,s)}}static getCipher(e,t){var n,i=function(e){return e instanceof s}(e)?e:this.getDefaultParams(e);return{cipherParams:i,cipher:new a(i,null!=(n=e.iv)?n:null,t)}}}n.CipherParams=s;class a{constructor(e,s,n){if(this.logger=n,!crypto.subtle)throw isSecureContext?new Error("Crypto operations are not possible since the browser’s SubtleCrypto class is unavailable (reason unknown)."):new Error("Crypto operations are is not possible since the current environment is a non-secure context and hence the browser’s SubtleCrypto class is not available.");this.algorithm=e.algorithm+"-"+String(e.keyLength)+"-"+e.mode,this.webCryptoAlgorithm=e.algorithm+"-"+e.mode,this.key=t.toArrayBuffer(e.key),this.iv=s?t.toArrayBuffer(s):null}concat(e,s){const n=new ArrayBuffer(e.byteLength+s.byteLength),a=new DataView(n),i=new DataView(t.toArrayBuffer(e));for(let t=0;t<i.byteLength;t++)a.setInt8(t,i.getInt8(t));const r=new DataView(t.toArrayBuffer(s));for(let t=0;t<r.byteLength;t++)a.setInt8(i.byteLength+t,r.getInt8(t));return n}async encrypt(e){k.logAction(this.logger,k.LOG_MICRO,"CBCCipher.encrypt()","");const t=await this.getIv(),s=await crypto.subtle.importKey("raw",this.key,this.webCryptoAlgorithm,!1,["encrypt"]),n=await crypto.subtle.encrypt({name:this.webCryptoAlgorithm,iv:t},s,e);return this.concat(t,n)}async decrypt(e){k.logAction(this.logger,k.LOG_MICRO,"CBCCipher.decrypt()","");const s=t.toArrayBuffer(e),n=s.slice(0,16),a=s.slice(16),i=await crypto.subtle.importKey("raw",this.key,this.webCryptoAlgorithm,!1,["decrypt"]);return crypto.subtle.decrypt({name:this.webCryptoAlgorithm,iv:n},i,a)}async getIv(){if(this.iv){var s=this.iv;return this.iv=null,s}const n=await e.getRandomArrayBuffer(16);return t.toArrayBuffer(n)}}return n}(In,wn);g.Crypto=Zn,g.BufferUtils=wn,g.Http=En,g.Config=In,g.Transports=Vn,g.WebStorage=An;for(const s of[Es,sn])s.Crypto=Zn,s._MsgPack=Qn;En.bundledRequestImplementations=Xn,k.initLogHandlers(),g.Defaults=(Yn=qn,Object.assign(we,Yn)),g.Config.agent&&(g.Defaults.agent+=" "+g.Config.agent);var ea={ErrorInfo:E,Rest:Es,Realtime:sn,msgpack:Qn,makeProtocolMessageFromDeserialized:hs};return"object"==typeof n.exports&&(n.exports=((e,t,s,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of Object.getOwnPropertyNames(t))Object.prototype.hasOwnProperty.call(e,a)||a===s||Object.defineProperty(e,a,{get:()=>t[a],enumerable:!(n=Object.getOwnPropertyDescriptor(t,a))||n.enumerable});return e})(n.exports,t)),n.exports},ce.exports=ue());let he=null;const pe=a(!1),fe=a([]),ve=()=>"undefined"!=typeof window?window.location.hostname:"default",me=async e=>{J.info("[ABLY] Connexion au service de temps réel");try{J.debug("[ABLY] Initialisation sans clientId spécifique");const t={authCallback:async(e,t)=>{J.debug("[ABLY] authCallback appelé pour renouveler le token");try{t(null,await(async()=>{var e;try{const t=await $.routes.client.getRealtimeToken();if(!(null==(e=t.data)?void 0:e.token))throw new Error("Token Ably client non trouvé dans la réponse");return J.debug("[ABLY] Nouveau token client obtenu avec succès",{client_id:t.data.client_id}),t.data.token}catch(t){throw J.error("[ABLY] Erreur lors de la récupération du token client",{error:t}),t}})())}catch(s){J.error("[ABLY] Erreur dans authCallback",{error:s});t({code:40170,statusCode:401,message:s instanceof Error?s.message:"Erreur de récupération du token",name:"TokenError"},null)}},token:e,echoMessages:!1,closeOnUnload:!1,recover:""};he=new de.Realtime(t),he.connection.on("connected",()=>{J.info("[ABLY] Connecté au service de temps réel"),pe.value=!0}),he.connection.on("disconnected",()=>{J.info("[ABLY] Déconnecté du service de temps réel"),pe.value=!1}),he.connection.on("failed",e=>{J.error("[ABLY] Échec de connexion",{reason:e.reason})}),he.connection.on("suspended",()=>{}),setTimeout(()=>{pe.value||(J.debug("[ABLY] Tentative de connexion forcée après délai d'initialisation"),null==he||he.connect())},1e3)}catch(t){throw J.error("[ABLY] Erreur de connexion",{error:t}),t}},ge=(e,t,s)=>{if(!he)return J.error("[ABLY] Client non initialisé"),()=>{};try{he.channels.get(e).subscribe(t,n=>{J.debug(`[ABLY] Message reçu sur ${e}:${t}`,{message:n}),s(n.data)});return-1===fe.value.findIndex(s=>s.channel===e&&s.event===t)?(fe.value.push({channel:e,event:t,callback:s}),J.debug(`[ABLY] Abonnement ajouté: ${e}:${t}`)):J.debug(`[ABLY] Abonnement déjà existant: ${e}:${t}`),()=>ye(e,t)}catch(n){return J.error("[ABLY] Erreur lors de l'abonnement au canal",{channel:e,error:n}),()=>{}}},ye=(e,t)=>{if(he)try{const s=he.channels.get(e);t?(s.unsubscribe(t),fe.value=fe.value.filter(s=>!(s.channel===e&&s.event===t))):(s.unsubscribe(),fe.value=fe.value.filter(t=>t.channel!==e)),be()}catch(s){J.error("[ABLY] Erreur lors du désabonnement du canal",{channel:e,error:s})}else J.error("[ABLY] Client non initialisé")},be=()=>{var e;const t=(()=>{if(!he)return J.error("[ABLY] Client non initialisé"),[];const e=new Map;return fe.value.forEach(t=>{var s,n;e.has(t.channel)||e.set(t.channel,[]),(null==(s=e.get(t.channel))?void 0:s.includes(t.event))||null==(n=e.get(t.channel))||n.push(t.event)}),Array.from(e.entries()).map(([e,t])=>{let s="unknown";try{he&&he.channels&&(s=he.channels.get(e).state||"attached")}catch(n){}return{name:e,state:s,events:t}}).sort((e,t)=>e.name.localeCompare(t.name))})();return J.debug(`[ABLY] Nombre total d'abonnements enregistrés: ${fe.value.length}`),0===t.length?fe.value.length>0&&J.debug("[ABLY] Abonnements enregistrés mais aucun canal trouvé:",fe.value.map(e=>`${e.channel}:${e.event}`)):t.forEach(e=>{e.name.includes("private-admin");e.events.length>0&&J.debug("Événements:",{events:e.events.join(", ")})}),"undefined"!=typeof window&&(null==(e=window.AblyDebug)?void 0:e.isAutoDisplayActive)&&window.AblyDebug.isAutoDisplayActive(),t},we=(e,t,s)=>{const n=`${ve()}:private-client-${e}`;J.info(`[ABLY] 🔔 ABONNEMENT CANAL PRIVÉ CLIENT: ${n}, événement: ${t}`);return ge(n,t,e=>{J.info(`[ABLY] 🎯 MESSAGE REÇU sur ${n}:${t}`,{message:e,timestamp:(new Date).toISOString(),channelName:n,event:t}),s(e)})},_e=(e,t,s)=>{const n=ve();return ge(`${n}:ticket-${e}`,t,s)},ke=n("realtime",()=>{const e=a(!1),t=a(!1),s=a(null),n=a(0),r=a(5),o=a(null),l=a(null),c=a(null),u=a(new Map),d=a(null),h=a([]),p=a(0),f=i(()=>null!==s.value),v=i(()=>n.value<r.value),m=i(()=>e.value&&t.value&&!f.value),g=i(()=>p.value>0),y=i(()=>!!d.value),b=async()=>{var a;try{J.info("[REALTIME STORE CLIENT] Initialisation du service temps réel");if(!oe().isAuthenticated)throw new Error("Utilisateur non authentifié");J.debug("[REALTIME STORE CLIENT] Récupération du token Ably");const i=null==(a=(await $.routes.client.getRealtimeToken()).data)?void 0:a.token;if(!i)throw new Error("Token Ably non trouvé dans la réponse");J.debug("[REALTIME STORE CLIENT] Initialisation d'Ably avec le token"),await me(i),w(),e.value=!0,t.value=!0,n.value=0,s.value=null,J.info("[REALTIME STORE CLIENT] Service de temps réel initialisé avec succès")}catch(i){J.error("[REALTIME STORE CLIENT] Erreur lors de l'initialisation",{error:i}),s.value=i,e.value=!1,t.value=!1}},w=()=>{const e=he;e&&(e.connection.on("connected",()=>{J.info("[REALTIME STORE CLIENT] Connexion établie"),t.value=!0,s.value=null,n.value=0}),e.connection.on("disconnected",()=>{J.warn("[REALTIME STORE CLIENT] Connexion perdue"),t.value=!1}),e.connection.on("failed",e=>{var n;J.error("[REALTIME STORE CLIENT] Échec de connexion",{reason:e.reason}),t.value=!1,s.value=new Error((null==(n=e.reason)?void 0:n.message)||"Connexion échouée")}),e.connection.on("suspended",()=>{J.warn("[REALTIME STORE CLIENT] Connexion suspendue"),t.value=!1}))},_=e=>{if(J.info("[REALTIME STORE CLIENT] Nouvelle réponse reçue",{data:e}),S(e.timestamp))return void J.debug("[REALTIME STORE CLIENT] Événement dupliqué ignoré",{timestamp:e.timestamp});const t={action:"reply",ticket:e.ticket,timestamp:e.timestamp,author:e.author,data:e.reply};o.value=t,l.value=e.timestamp},k=e=>{if(J.info("[REALTIME STORE CLIENT] Changement de statut reçu",{data:e}),S(e.timestamp))return void J.debug("[REALTIME STORE CLIENT] Événement dupliqué ignoré",{timestamp:e.timestamp});const t={action:"status_change",ticket:e.ticket,timestamp:e.timestamp,author:e.author,data:{oldStatus:e.oldStatus,newStatus:e.newStatus}};o.value=t,l.value=e.timestamp},C=e=>{if(J.info("[REALTIME STORE CLIENT] Mise à jour ticket reçue",{data:e}),S(e.timestamp))return void J.debug("[REALTIME STORE CLIENT] Événement dupliqué ignoré",{timestamp:e.timestamp});const t={action:"update",ticket:e.ticket,timestamp:e.timestamp,author:e.author,data:e.changes};o.value=t,l.value=e.timestamp},S=e=>l.value===e,E=e=>{J.info("[REALTIME STORE] Nouvelle notification",{notification:e}),h.value.unshift(e),e.read||p.value++,h.value.length>50&&(h.value=h.value.slice(0,50))};return{initialized:e,connected:t,error:s,reconnectAttempts:n,maxReconnectAttempts:r,lastRealtimeEvent:o,lastEventTimestamp:l,lastDashboardEvent:c,dashboardEventHandlers:u,dashboardChannel:d,notifications:h,unreadNotificationsCount:p,hasError:f,canReconnect:v,isReady:m,hasUnreadNotifications:g,isDashboardConnected:y,init:b,disconnect:()=>{J.info("[REALTIME STORE CLIENT] Déconnexion du service temps réel"),J.info("[ABLY] Déconnexion du service de temps réel"),he&&(he.close(),he=null,pe.value=!1),e.value=!1,t.value=!1,s.value=null,o.value=null,l.value=null},retry:async()=>{if(v.value){n.value++,J.info("[REALTIME STORE CLIENT] Tentative de reconnexion",{attempt:n.value});try{await b()}catch(e){J.error("[REALTIME STORE CLIENT] Échec de la reconnexion",{error:e})}}else J.warn("[REALTIME STORE CLIENT] Nombre maximum de tentatives de reconnexion atteint")},subscribeToTicket:e=>{if(!m.value)return J.warn("[REALTIME STORE CLIENT] Service non prêt pour abonnement ticket",{ticketId:e}),()=>{};J.info("[REALTIME STORE CLIENT] Abonnement aux événements du ticket",{ticketId:e});const t=_e(e,"ticket-reply",e=>{_(e)}),s=_e(e,"ticket-status-change",e=>{k(e)}),n=_e(e,"ticket-update",e=>{C(e)});return()=>{J.info("[REALTIME STORE CLIENT] Désabonnement des événements du ticket",{ticketId:e}),t(),s(),n()}},subscribeToDashboardEvents:async e=>{try{if(!m.value)throw new Error("Service temps réel non initialisé");if(d.value)return void J.warn("[REALTIME STORE] Abonnement dashboard déjà actif",{clientId:e});J.info("[REALTIME STORE] Abonnement aux événements dashboard sur canal privé client",{clientId:e});const t=we(e,"dashboard-update",t=>{J.info("[REALTIME STORE] Événement dashboard-update reçu",{event:t,clientId:e});const s=t;c.value=s;const n=u.value.get(s.entity_type);n&&n(s),"notification"===s.action&&s.data.notification&&E(s.data.notification)});J.info("[REALTIME STORE] Abonnement à service-update sur canal private-client-"+e);const s=we(e,"service-update",t=>{var s,n,a,i;J.info("[REALTIME STORE] 🎯 ÉVÉNEMENT SERVICE-UPDATE REÇU !",{data:t,clientId:e,timestamp:(new Date).toISOString()});const r={entity_type:"service",action:t.action||t.type,data:{service:(null==(s=t.data)?void 0:s.service)||t.service||t},timestamp:t.timestamp||(new Date).toISOString()};J.info("[REALTIME STORE] Événement service normalisé",{normalizedEvent:r});const o=u.value.get("service");o&&o(r);const l=u.value.get("service-page");l&&l(r);const c=(null==(a=null==(n=t.data)?void 0:n.service)?void 0:a.id)||(null==(i=t.service)?void 0:i.id);if(c){const e=`service-detail-${c}`,t=u.value.get(e);t?(J.info("[REALTIME STORE] 📤 ENVOI VERS SERVICE DETAIL HANDLER:",{handler_key:e,handler_exists:!0,data_sent:r,service_id:c}),t(r)):J.info("[REALTIME STORE] ℹ️ SERVICE DETAIL HANDLER NON TROUVÉ (normal si pas sur page détail):",{handler_key:e,service_id:c})}});J.info("[REALTIME STORE] Abonnement à invoice-update sur canal private-client-"+e);const n=we(e,"invoice-update",t=>{var s,n,a,i,r,o,l;J.info("[REALTIME STORE] 🎯 ÉVÉNEMENT INVOICE-UPDATE REÇU !",{data:t,clientId:e,timestamp:(new Date).toISOString()}),J.info("[REALTIME STORE] 📊 ANALYSE STRUCTURE DONNÉES INVOICE:",{data_keys:Object.keys(t),"data.action":t.action,"data.entity_type":t.entity_type,"data.data_exists":!!t.data,"data.data_keys":t.data?Object.keys(t.data):null,"data.invoice_exists":!!t.invoice,"data.data.invoice_exists":t.data?!!t.data.invoice:null,structure_complete:JSON.stringify(t,null,2)});const c=u.value.get("invoice");c?(J.info("[REALTIME STORE] 📤 ENVOI VERS DASHBOARD HANDLER:",{handler_exists:!0,data_sent:t}),c(t)):J.warn("[REALTIME STORE] ❌ DASHBOARD HANDLER INTROUVABLE pour invoice");const d=u.value.get("invoice-page");d?(J.info("[REALTIME STORE] 📤 ENVOI VERS PAGE HANDLER:",{handler_exists:!0,data_sent:t}),d(t)):J.warn("[REALTIME STORE] ❌ PAGE HANDLER INTROUVABLE pour invoice-page");const h=(null==(n=null==(s=t.data)?void 0:s.invoice)?void 0:n.id)||(null==(a=t.invoice)?void 0:a.id);if(h){const e=`invoice-detail-${h}`,s=u.value.get(e);if(s){const n={entity_type:"invoice",action:t.action||t.type,data:{invoice:(null==(i=t.data)?void 0:i.invoice)||t.invoice||t},timestamp:t.timestamp||(new Date).toISOString()};J.info("[REALTIME STORE] 📤 ENVOI VERS DETAIL HANDLER:",{handler_key:e,handler_exists:!0,data_sent:n,invoice_id:h}),s(n)}else J.info("[REALTIME STORE] ℹ️ DETAIL HANDLER NON TROUVÉ (normal si pas sur page détail):",{handler_key:e,invoice_id:h})}const p=(null==(o=null==(r=t.data)?void 0:r.invoice)?void 0:o.id)||(null==(l=t.invoice)?void 0:l.id);if(p){const e=`invoice-detail-${p}`,s=u.value.get(e);s?(J.info("[REALTIME STORE] 📤 ENVOI VERS DETAIL HANDLER:",{handler_key:e,handler_exists:!0,data_sent:t,invoice_id:p}),s(t)):J.info("[REALTIME STORE] ℹ️ DETAIL HANDLER NON TROUVÉ (normal si pas sur page détail):",{handler_key:e,invoice_id:p})}}),a=we(e,"ticket-update",t=>{J.info("[REALTIME STORE] Événement ticket-update reçu",{data:t,clientId:e});const s=u.value.get("ticket");s&&s(t);const n=u.value.get("ticket-page");n?(J.info("[REALTIME STORE] 📤 ENVOI VERS TICKET PAGE HANDLER:",{handler_exists:!0,data_sent:t}),n(t)):J.info("[REALTIME STORE] ℹ️ TICKET PAGE HANDLER NON TROUVÉ (normal si pas sur page support)")}),i=we(e,"stats-update",t=>{J.info("[REALTIME STORE] Événement stats-update reçu",{data:t,clientId:e});const s=u.value.get("stats");s&&s(t)});d.value={unsubscribe:()=>{t(),s(),n(),a(),i()}},J.info("[REALTIME STORE] Abonnement dashboard établi avec succès sur canal privé client",{clientId:e})}catch(t){throw J.error("[REALTIME STORE] Erreur lors de l'abonnement dashboard",{error:t.message,clientId:e}),t}},unsubscribeFromDashboardEvents:()=>{d.value&&(J.info("[REALTIME STORE] Désabonnement des événements dashboard"),d.value.unsubscribe(),d.value=null,u.value.clear())},registerDashboardHandler:(e,t)=>{u.value.set(e,t)},unregisterDashboardHandler:e=>{u.value.delete(e)},addNotification:E,markNotificationAsRead:e=>{const t=h.value.find(t=>t.id===e);t&&!t.read&&(t.read=!0,p.value=Math.max(0,p.value-1))},markAllNotificationsAsRead:()=>{h.value.forEach(e=>{e.read=!0}),p.value=0,J.info("[REALTIME STORE] Toutes les notifications marquées comme lues")},clearNotifications:()=>{h.value=[],p.value=0,J.info("[REALTIME STORE] Notifications effacées")}}}),Ce={class:"notification-center"},Se={key:0,class:"notification-dropdown"},Ee={class:"notification-header"},Re={class:"notification-actions"},Te=["title"],Ae=["title"],Oe={class:"notification-list"},Ie={key:0,class:"no-notifications"},Me=["onClick"],Le={class:"notification-icon"},Pe={class:"notification-content"},Ne={class:"notification-title"},Ue={class:"notification-message"},xe={class:"notification-time"},De={class:"notification-actions-item"},Be=["onClick","title"],Ve=["onClick","title"],qe={key:0,class:"notification-footer"},He=(e,t)=>{const s=e.__vccOpts||e;for(const[n,a]of t)s[n]=a;return s},Ge=He(r({__name:"NotificationCenter",props:{notifications:{default:()=>[]},maxVisible:{default:10}},emits:["notificationClick","markAsRead","markAllAsRead","clearAll","remove"],setup(e,{emit:t}){const s=e,n=t,r=o(),C=ke(),S=a(!1),E=a(!1),R=i(()=>s.notifications.slice(0,s.maxVisible)),T=i(()=>s.notifications.filter(e=>!e.read).length),A=()=>{S.value=!S.value,S.value&&(E.value=!1)},O=()=>{S.value=!1},I=e=>{C.markNotificationAsRead(e),n("markAsRead",e)},M=()=>{J.info("[NOTIFICATION CENTER] Marquer toutes comme lues"),C.markAllNotificationsAsRead(),n("markAllAsRead")},L=()=>{J.info("[NOTIFICATION CENTER] Effacer toutes les notifications"),C.clearNotifications(),n("clearAll")},P=()=>{J.info("[NOTIFICATION CENTER] Voir toutes les notifications"),r.push("/notifications"),O()},N=e=>{const t=new Date(e),s=(new Date).getTime()-t.getTime(),n=Math.floor(s/6e4),a=Math.floor(n/60),i=Math.floor(a/24);return n<1?"À l'instant":n<60?`Il y a ${n}min`:a<24?`Il y a ${a}h`:i<7?`Il y a ${i}j`:new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"short",hour:"2-digit",minute:"2-digit"}).format(t)};l(()=>s.notifications.length,(e,t)=>{e>t&&!S.value&&(E.value=!0,setTimeout(()=>{E.value=!1},3e3))});const U=e=>{e.target.closest(".notification-center")||O()};return c(()=>{document.addEventListener("click",U)}),u(()=>{document.removeEventListener("click",U)}),(e,t)=>(h(),d("div",Ce,[p("div",{class:m(["notification-trigger",{active:S.value}]),onClick:A},[t[0]||(t[0]=p("i",{class:"fas fa-bell"},null,-1)),T.value>0?(h(),d("span",{key:0,class:m(["notification-badge",{pulse:E.value}])},g(T.value>99?"99+":T.value),3)):v("",!0)],2),f(y,{name:"notification-dropdown"},{default:b(()=>[S.value?(h(),d("div",Se,[p("div",Ee,[p("h4",null,g(e.$t("notifications.title")),1),p("div",Re,[T.value>0?(h(),d("button",{key:0,onClick:M,class:"btn-mark-all-read",title:e.$t("notifications.mark_all_read")},t[1]||(t[1]=[p("i",{class:"fas fa-check-double"},null,-1)]),8,Te)):v("",!0),p("button",{onClick:L,class:"btn-clear-all",title:e.$t("notifications.clear_all")},t[2]||(t[2]=[p("i",{class:"fas fa-trash"},null,-1)]),8,Ae)])]),p("div",Oe,[0===R.value.length?(h(),d("div",Ie,[t[3]||(t[3]=p("i",{class:"fas fa-bell-slash"},null,-1)),p("p",null,g(e.$t("notifications.no_notifications")),1)])):v("",!0),(h(!0),d(w,null,_(R.value,s=>{return h(),d("div",{key:s.id,class:m(["notification-item",{unread:!s.read,clickable:!!s.action_url}]),onClick:e=>(e=>{J.info("[NOTIFICATION CENTER] Clic sur notification",{notification:e}),e.read||I(e.id),e.action_url&&(r.push(e.action_url),O()),n("notificationClick",e)})(s)},[p("div",Le,[p("i",{class:m((a=s.type,{service:"fas fa-server text-blue",invoice:"fas fa-file-invoice text-orange",ticket:"fas fa-headset text-green",system:"fas fa-cog text-gray"}[a]||"fas fa-info-circle text-blue"))},null,2)]),p("div",Pe,[p("div",Ne,g(s.title),1),p("div",Ue,g(s.message),1),p("div",xe,g(N(s.created_at)),1)]),p("div",De,[s.read?v("",!0):(h(),d("button",{key:0,onClick:k(e=>I(s.id),["stop"]),class:"btn-mark-read",title:e.$t("notifications.mark_read")},t[4]||(t[4]=[p("i",{class:"fas fa-check"},null,-1)]),8,Be)),p("button",{onClick:k(e=>{return t=s.id,void n("remove",t);var t},["stop"]),class:"btn-remove",title:e.$t("notifications.remove")},t[5]||(t[5]=[p("i",{class:"fas fa-times"},null,-1)]),8,Ve)])],10,Me);var a}),128))]),R.value.length>0?(h(),d("div",qe,[p("button",{onClick:P,class:"btn-view-all"},g(e.$t("notifications.view_all")),1)])):v("",!0)])):v("",!0)]),_:1}),S.value?(h(),d("div",{key:0,class:"notification-overlay",onClick:O})):v("",!0)]))}}),[["__scopeId","data-v-8937cb6a"]]),je={class:"client-header"},Fe={class:"header-left"},$e={class:"page-title"},We={class:"header-right"},ze={class:"search-bar"},Je={class:"current-lang"},Ke={class:"menu-items"},Ye={key:0,class:"fas fa-check"},Qe={key:0,class:"fas fa-check"},Xe={class:"user-name"},Ze={key:0,class:"user-info"},et={class:"user-details"},tt={class:"name"},st={class:"email"},nt={key:1,class:"user-info"},at={key:2,class:"user-info"},it={class:"menu-items"},rt=He(r({__name:"AppHeader",setup(e){const t=C(),s=o(),{locale:n}=S(),r=oe(),l=ke(),y=a(""),w=a(n.value||"fr"),_=a(!1),I=a(!1),M=a(),L=a(),P=i(()=>l.notifications),N=i(()=>({dashboard:"Tableau de bord",services:"Mes Services",invoices:"Factures",tickets:"Support",account:"Mon Compte"}[t.name]||"TechCMS Client")),U=()=>{},x=()=>{_.value=!_.value,I.value=!1},D=e=>{w.value=e,n.value=e,_.value=!1,localStorage.setItem("preferred-language",e)},B=()=>{I.value=!I.value,_.value=!1},V=e=>{J.info("[HEADER] Clic sur notification",{notification:e}),e.action_url&&s.push(e.action_url)},q=e=>{l.markNotificationAsRead(e)},H=()=>{J.info("[HEADER] Marquer toutes les notifications comme lues"),l.markAllNotificationsAsRead()},G=()=>{J.info("[HEADER] Effacer toutes les notifications"),l.clearNotifications()},j=async()=>{try{await r.logout(),s.push("/client/login")}catch(e){}},F=e=>{M.value&&!M.value.contains(e.target)&&(_.value=!1),L.value&&!L.value.contains(e.target)&&(I.value=!1)};return c(async()=>{document.addEventListener("click",F);const e=localStorage.getItem("preferred-language");!e||"fr"!==e&&"en"!==e||(w.value=e,n.value=e),r.initialized||await r.initialize()}),u(()=>{document.removeEventListener("click",F)}),(e,t)=>{var s;const n=E("router-link");return h(),d("div",je,[p("div",Fe,[p("h1",$e,g(N.value),1)]),p("div",We,[p("div",ze,[t[3]||(t[3]=p("i",{class:"fas fa-search"},null,-1)),R(p("input",{"onUpdate:modelValue":t[0]||(t[0]=e=>y.value=e),type:"text",placeholder:"Rechercher...",onInput:U},null,544),[[T,y.value]])]),p("div",{ref_key:"languageDropdown",ref:M,class:"dropdown"},[p("button",{class:"btn-icon dropdown-toggle",onClick:x},[p("span",Je,g(w.value.toUpperCase()),1)]),p("div",{class:m(["dropdown-menu",{show:_.value}])},[t[8]||(t[8]=p("div",{class:"menu-header"},[p("h3",null,"Langue")],-1)),p("div",Ke,[p("a",{href:"#",class:m(["menu-item",{active:"fr"===w.value}]),onClick:t[1]||(t[1]=k(e=>D("fr"),["prevent"]))},[t[4]||(t[4]=p("span",{class:"flag"},"🇫🇷",-1)),t[5]||(t[5]=p("span",{class:"label"},"Français",-1)),"fr"===w.value?(h(),d("i",Ye)):v("",!0)],2),p("a",{href:"#",class:m(["menu-item",{active:"en"===w.value}]),onClick:t[2]||(t[2]=k(e=>D("en"),["prevent"]))},[t[6]||(t[6]=p("span",{class:"flag"},"🇬🇧",-1)),t[7]||(t[7]=p("span",{class:"label"},"English",-1)),"en"===w.value?(h(),d("i",Qe)):v("",!0)],2)])],2)],512),f(Ge,{notifications:P.value,onNotificationClick:V,onMarkAsRead:q,onMarkAllAsRead:H,onClearAll:G},null,8,["notifications"]),p("div",{ref_key:"userDropdown",ref:L,class:"user-menu"},[p("button",{class:"user-btn",onClick:B},[t[9]||(t[9]=p("div",{class:"user-avatar"},[p("i",{class:"fas fa-user-circle"})],-1)),p("span",Xe,g(A(r).isAuthenticated?A(r).userFullName:A(r).loading?"Chargement...":"Non connecté"),1),t[10]||(t[10]=p("i",{class:"fas fa-chevron-down"},null,-1))]),p("div",{class:m(["user-dropdown",{show:I.value}])},[A(r).isAuthenticated?(h(),d("div",Ze,[t[11]||(t[11]=p("div",{class:"user-avatar-large"},[p("i",{class:"fas fa-user-circle"})],-1)),p("div",et,[p("div",tt,g(A(r).userFullName),1),p("div",st,g(null==(s=A(r).user)?void 0:s.email),1)])])):A(r).loading?(h(),d("div",nt,t[12]||(t[12]=[O('<div class="user-avatar-large" data-v-3abcfcae><i class="fas fa-user-circle" data-v-3abcfcae></i></div><div class="user-details" data-v-3abcfcae><div class="name" data-v-3abcfcae>Chargement...</div><div class="email" data-v-3abcfcae>---</div></div>',2)]))):(h(),d("div",at,t[13]||(t[13]=[O('<div class="user-avatar-large" data-v-3abcfcae><i class="fas fa-user-circle" data-v-3abcfcae></i></div><div class="user-details" data-v-3abcfcae><div class="name" data-v-3abcfcae>Non connecté</div><div class="email" data-v-3abcfcae>---</div></div>',2)]))),t[17]||(t[17]=p("div",{class:"menu-divider"},null,-1)),p("div",it,[f(n,{to:"/account",class:"menu-item"},{default:b(()=>t[14]||(t[14]=[p("i",{class:"fas fa-user-cog"},null,-1),p("span",null,"Mon Compte",-1)])),_:1,__:[14]}),f(n,{to:"/settings",class:"menu-item"},{default:b(()=>t[15]||(t[15]=[p("i",{class:"fas fa-cog"},null,-1),p("span",null,"Paramètres",-1)])),_:1,__:[15]}),p("a",{href:"#",class:m(["menu-item",{disabled:A(r).loading}]),onClick:k(j,["prevent"])},t[16]||(t[16]=[p("i",{class:"fas fa-sign-out-alt"},null,-1),p("span",null,"Déconnexion",-1)]),2)])],2)],512)])])}}}),[["__scopeId","data-v-3abcfcae"]]),ot="data:image/png;base64,Cg==",lt={class:"sidebar-header"},ct={key:0,class:"logo-text"},ut={class:"sidebar-nav"},dt={key:0},ht={key:0},pt={key:0},ft={key:0},vt={key:0},mt={key:0},gt={key:0},yt={key:0,class:"sidebar-footer"},bt={class:"user-info"},wt={key:0,class:"user-details"},_t={class:"user-name"},kt={class:"user-email"},Ct={key:1,class:"user-details"},St={class:"user-name"},Et={key:2,class:"user-details"},Rt={class:"user-name"},Tt=["disabled"],At=He(r({__name:"AppSidebar",props:{collapsed:{type:Boolean}},emits:["sidebar-toggle"],setup(e,{emit:t}){const s=t,n=oe(),a=o(),i=()=>{s("sidebar-toggle")},r=async()=>{try{await n.logout(),a.push("/client/login")}catch(e){}};return c(async()=>{n.initialized||await n.initialize()}),(e,t)=>{var s;const a=E("router-link");return h(),d("aside",{class:m(["client-sidebar",{"sidebar-collapsed":e.collapsed}])},[p("div",lt,[f(a,{to:"/dashboard",class:"logo"},{default:b(()=>[t[0]||(t[0]=p("img",{src:ot,alt:"TechCMS",class:"logo-img"},null,-1)),e.collapsed?v("",!0):(h(),d("span",ct,"TechCMS"))]),_:1,__:[0]}),p("button",{class:"sidebar-toggle",onClick:i},t[1]||(t[1]=[p("i",{class:"fas fa-bars"},null,-1)]))]),p("nav",ut,[p("ul",null,[p("li",{class:m({active:"dashboard"===e.$route.name})},[f(a,{to:"/dashboard",class:"nav-link"},{default:b(()=>[t[2]||(t[2]=p("i",{class:"fas fa-home"},null,-1)),e.collapsed?v("",!0):(h(),d("span",dt,g(e.$t("dashboard.title")),1))]),_:1,__:[2]})],2),p("li",{class:m({active:"licenses"===e.$route.name||"license-detail"===e.$route.name})},[f(a,{to:"/licenses",class:"nav-link"},{default:b(()=>[t[3]||(t[3]=p("i",{class:"fas fa-key"},null,-1)),e.collapsed?v("",!0):(h(),d("span",ht,g(e.$t("licenses.title")),1))]),_:1,__:[3]})],2),p("li",{class:m({active:"store-private"===e.$route.name})},[f(a,{to:"/store",class:"nav-link"},{default:b(()=>[t[4]||(t[4]=p("i",{class:"fas fa-shopping-bag"},null,-1)),e.collapsed?v("",!0):(h(),d("span",pt,"Boutique"))]),_:1,__:[4]})],2),p("li",{class:m({active:"updates"===e.$route.name})},[f(a,{to:"/updates",class:"nav-link"},{default:b(()=>[t[5]||(t[5]=p("i",{class:"fas fa-download"},null,-1)),e.collapsed?v("",!0):(h(),d("span",ft,"Mises à jour"))]),_:1,__:[5]})],2),p("li",{class:m({active:"billing"===e.$route.name})},[f(a,{to:"/billing",class:"nav-link"},{default:b(()=>[t[6]||(t[6]=p("i",{class:"fas fa-file-invoice"},null,-1)),e.collapsed?v("",!0):(h(),d("span",vt,g(e.$t("billing.title")),1))]),_:1,__:[6]})],2),p("li",{class:m({active:"support"===e.$route.name})},[f(a,{to:"/support",class:"nav-link"},{default:b(()=>[t[7]||(t[7]=p("i",{class:"fas fa-headset"},null,-1)),e.collapsed?v("",!0):(h(),d("span",mt,g(e.$t("support.title")),1))]),_:1,__:[7]})],2),p("li",{class:m({active:"account"===e.$route.name})},[f(a,{to:"/account",class:"nav-link"},{default:b(()=>[t[8]||(t[8]=p("i",{class:"fas fa-user-cog"},null,-1)),e.collapsed?v("",!0):(h(),d("span",gt,g(e.$t("account.title")),1))]),_:1,__:[8]})],2)])]),e.collapsed?v("",!0):(h(),d("div",yt,[p("div",bt,[t[11]||(t[11]=p("div",{class:"user-avatar"},[p("i",{class:"fas fa-user-circle"})],-1)),A(n).isAuthenticated?(h(),d("div",wt,[p("div",_t,g(A(n).userFullName),1),p("div",kt,g(null==(s=A(n).user)?void 0:s.email),1)])):A(n).loading?(h(),d("div",Ct,[p("div",St,g(e.$t("common.loading")),1),t[9]||(t[9]=p("div",{class:"user-email"},"---",-1))])):(h(),d("div",Et,[p("div",Rt,g(e.$t("auth.not_connected")),1),t[10]||(t[10]=p("div",{class:"user-email"},"---",-1))]))]),p("button",{class:"logout-btn",onClick:r,disabled:A(n).loading},[t[12]||(t[12]=p("i",{class:"fas fa-sign-out-alt"},null,-1)),p("span",null,g(e.$t("auth.logout")),1)],8,Tt)]))],2)}}}),[["__scopeId","data-v-c683936d"]]),Ot={class:"client-app-layout"},It={class:"app-container"},Mt=He(r({__name:"AppLayout",setup(e){const t=a(!1),s=()=>{t.value=!t.value};return(e,n)=>(h(),d("div",Ot,[p("header",null,[f(rt)]),p("div",It,[f(At,{collapsed:t.value,onSidebarToggle:s},null,8,["collapsed"]),p("main",{class:m(["app-content",{"sidebar-collapsed":t.value}])},[I(e.$slots,"default",{},void 0)],2)])]))}}),[["__scopeId","data-v-aa1a35f8"]]),Lt=n("cart",()=>{const e=a([]),t=a(!1),s=a(null),n=i(()=>e.value.reduce((e,t)=>e+t.quantity,0)),r=i(()=>e.value.reduce((e,t)=>e+t.subtotal,0)),o=i(()=>.2*r.value),l=i(()=>e.value.reduce((e,t)=>e+(t.template.setup_fee||0)*t.quantity,0)),c=i(()=>r.value+l.value+o.value),u=i(()=>({items:e.value,total:r.value,tax_amount:o.value,grand_total:c.value,currency:"EUR"})),d=i(()=>0===e.value.length),h=i(()=>t=>e.value.some(e=>e.template.id===t)),p=i(()=>t=>e.value.find(e=>e.template.id===t)),f=t=>{try{const s=e.value.findIndex(e=>e.id===t);if(-1!==s){const n=e.value[s];e.value.splice(s,1),v(),J.info("[CartStore] Item supprimé du panier",{item_id:t,template_name:n.template.name}),y("remove_from_cart",n.template)}}catch(n){s.value="Erreur lors de la suppression",J.error("[CartStore] Erreur removeItem",{error:n.message})}},v=()=>{try{const t={items:e.value,updated_at:(new Date).toISOString()};localStorage.setItem("techcms_cart",JSON.stringify(t))}catch(t){J.error("[CartStore] Erreur lors de la sauvegarde localStorage",{error:t.message})}},m=()=>`cart_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,g=(e,t,s={})=>{let n=e.price;return e.setup_fee&&(n+=e.setup_fee),s.additional_domains&&(n+=10*s.additional_domains),s.additional_installations&&(n+=5*s.additional_installations),n*t},y=(e,t)=>{try{J.info("[CartStore] Tracking event",{event:e,template_id:t.id,template_name:t.name,cart_total:c.value})}catch(s){J.error("[CartStore] Erreur tracking",{error:s.message})}};return{items:e,loading:t,error:s,itemCount:n,subtotal:r,setupFees:l,taxAmount:o,total:c,cart:u,isEmpty:d,hasItem:h,getItem:p,addItem:(t,a=1,i={})=>{try{const s=e.value.findIndex(e=>e.template.id===t.id);if(-1!==s)e.value[s].quantity+=a,e.value[s].subtotal=g(t,e.value[s].quantity,i);else{const s={id:m(),template:t,quantity:a,customizations:i,subtotal:g(t,a,i)};e.value.push(s)}v(),J.info("[CartStore] Item ajouté au panier",{template_id:t.id,template_name:t.name,quantity:a,total_items:n.value}),y("add_to_cart",t)}catch(r){s.value="Erreur lors de l'ajout au panier",J.error("[CartStore] Erreur addItem",{error:r.message})}},updateQuantity:(t,n)=>{try{if(n<=0)return void f(t);const s=e.value.findIndex(e=>e.id===t);-1!==s&&(e.value[s].quantity=n,e.value[s].subtotal=g(e.value[s].template,n,e.value[s].customizations),v(),J.info("[CartStore] Quantité mise à jour",{item_id:t,new_quantity:n}))}catch(a){s.value="Erreur lors de la mise à jour",J.error("[CartStore] Erreur updateQuantity",{error:a.message})}},removeItem:f,clearCart:()=>{try{e.value=[],v(),J.info("[CartStore] Panier vidé")}catch(t){s.value="Erreur lors du vidage du panier",J.error("[CartStore] Erreur clearCart",{error:t.message})}},clearError:()=>{s.value=null},initialize:()=>{(()=>{try{const t=localStorage.getItem("techcms_cart");if(t){const s=JSON.parse(t);e.value=s.items||[],J.info("[CartStore] Panier chargé depuis localStorage",{items_count:e.value.length})}}catch(t){J.error("[CartStore] Erreur lors du chargement du localStorage",{error:t.message}),e.value=[]}})()},formatPrice:e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e)}}),Pt={class:"public-layout"},Nt={class:"public-header"},Ut={class:"header-container"},xt={class:"header-left"},Dt={class:"main-nav"},Bt={class:"header-right"},Vt={class:"cart-count"},qt={class:"cart-total"},Ht={class:"auth-actions"},Gt={class:"public-content"},jt={class:"public-footer"},Ft={class:"footer-container"},$t={class:"footer-content"},Wt={class:"footer-section"},zt={class:"footer-section"},Jt={class:"footer-section"},Kt=He(r({__name:"PublicLayout",setup(e){const t=Lt();return(e,s)=>{const n=E("router-link");return h(),d("div",Pt,[p("header",Nt,[p("div",Ut,[p("div",xt,[f(n,{to:"/store",class:"logo"},{default:b(()=>s[0]||(s[0]=[p("img",{src:ot,alt:"TechCMS",class:"logo-img"},null,-1),p("span",{class:"logo-text"},"TechCMS",-1)])),_:1,__:[0]}),p("nav",Dt,[f(n,{to:"/store",class:"nav-link"},{default:b(()=>s[1]||(s[1]=[p("i",{class:"fas fa-shopping-bag"},null,-1),p("span",null,"Boutique",-1)])),_:1,__:[1]}),s[2]||(s[2]=O('<a href="/website/features" class="nav-link" data-v-1e382b64><i class="fas fa-list" data-v-1e382b64></i><span data-v-1e382b64>Fonctionnalités</span></a><a href="/website/pricing" class="nav-link" data-v-1e382b64><i class="fas fa-tags" data-v-1e382b64></i><span data-v-1e382b64>Tarifs</span></a><a href="/website/contact" class="nav-link" data-v-1e382b64><i class="fas fa-envelope" data-v-1e382b64></i><span data-v-1e382b64>Contact</span></a>',3))])]),p("div",Bt,[A(t).itemCount>0?(h(),M(n,{key:0,to:"/cart",class:"cart-btn"},{default:b(()=>{return[s[3]||(s[3]=p("i",{class:"fas fa-shopping-cart"},null,-1)),p("span",Vt,g(A(t).itemCount),1),p("span",qt,g((e=A(t).total,new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e))),1)];var e}),_:1,__:[3]})):v("",!0),p("div",Ht,[f(n,{to:"/login",class:"btn btn-outline btn-sm"},{default:b(()=>s[4]||(s[4]=[p("i",{class:"fas fa-sign-in-alt"},null,-1),p("span",null,"Connexion",-1)])),_:1,__:[4]}),f(n,{to:"/register",class:"btn btn-primary btn-sm"},{default:b(()=>s[5]||(s[5]=[p("i",{class:"fas fa-user-plus"},null,-1),p("span",null,"Inscription",-1)])),_:1,__:[5]})])])])]),p("main",Gt,[I(e.$slots,"default",{},void 0)]),p("footer",jt,[p("div",Ft,[p("div",$t,[s[18]||(s[18]=O('<div class="footer-section" data-v-1e382b64><h3 data-v-1e382b64>TechCMS</h3><p data-v-1e382b64>Solution CMS professionnelle avec licences flexibles</p><div class="social-links" data-v-1e382b64><a href="#" class="social-link" data-v-1e382b64><i class="fab fa-twitter" data-v-1e382b64></i></a><a href="#" class="social-link" data-v-1e382b64><i class="fab fa-linkedin" data-v-1e382b64></i></a><a href="#" class="social-link" data-v-1e382b64><i class="fab fa-github" data-v-1e382b64></i></a></div></div>',1)),p("div",Wt,[s[9]||(s[9]=p("h4",null,"Produits",-1)),p("ul",null,[p("li",null,[f(n,{to:"/store"},{default:b(()=>s[6]||(s[6]=[L("Licences TechCMS")])),_:1,__:[6]})]),s[7]||(s[7]=p("li",null,[p("a",{href:"/website/features"},"Fonctionnalités")],-1)),s[8]||(s[8]=p("li",null,[p("a",{href:"/website/pricing"},"Tarifs")],-1))])]),p("div",zt,[s[13]||(s[13]=p("h4",null,"Support",-1)),p("ul",null,[s[11]||(s[11]=p("li",null,[p("a",{href:"/website/contact"},"Contact")],-1)),s[12]||(s[12]=p("li",null,[p("a",{href:"/website/about"},"À propos")],-1)),p("li",null,[f(n,{to:"/login"},{default:b(()=>s[10]||(s[10]=[L("Espace client")])),_:1,__:[10]})])])]),p("div",Jt,[s[17]||(s[17]=p("h4",null,"Liens utiles",-1)),p("ul",null,[s[15]||(s[15]=p("li",null,[p("a",{href:"/website"},"Site vitrine")],-1)),s[16]||(s[16]=p("li",null,[p("a",{href:"/admin"},"Administration")],-1)),p("li",null,[f(n,{to:"/register"},{default:b(()=>s[14]||(s[14]=[L("Créer un compte")])),_:1,__:[14]})])])])]),s[19]||(s[19]=p("div",{class:"footer-bottom"},[p("p",null,"© 2024 TechCMS. Tous droits réservés.")],-1))])])])}}}),[["__scopeId","data-v-1e382b64"]]),Yt={id:"app",class:"tech-cms-client-app"},Qt={key:0,class:"auth-container"},Xt={key:0,class:"realtime-error-banner"},Zt=He(r({__name:"App",setup(e){const t=oe(),s=ke(),n=i(()=>s.hasError&&t.isAuthenticated);c(async()=>{J.info("TechCMS Client - Initialisation..."),await t.initialize(),t.isAuthenticated&&(J.info("TechCMS Client - Initialisation du temps réel..."),a())}),l(()=>t.isAuthenticated,e=>{e?(J.info("TechCMS Client - Utilisateur authentifié, initialisation du temps réel..."),a()):(J.info("TechCMS Client - Utilisateur déconnecté, déconnexion du temps réel..."),s.disconnect())});const a=async()=>{try{await s.init()}catch(e){J.error("TechCMS Client - Erreur d'initialisation du temps réel:",{error:e})}},r=async()=>{J.info("TechCMS Client - Tentative de reconnexion au service temps réel..."),await s.retry()};return(e,t)=>{const s=E("router-view");return h(),d("div",Yt,[f(s,null,{default:b(({Component:e,route:t})=>[f(y,{name:"fade",mode:"out-in"},{default:b(()=>[t.meta.hideLayout?(h(),d("div",Qt,[(h(),M(P(e)))])):t.meta.public?(h(),M(Kt,{key:1},{default:b(()=>[(h(),M(P(e)))]),_:2},1024)):(h(),M(Mt,{key:2},{default:b(()=>[(h(),M(P(e)))]),_:2},1024))]),_:2},1024)]),_:1}),n.value?(h(),d("div",Xt,[p("div",{class:"realtime-error-content"},[t[0]||(t[0]=p("span",null,"Connexion temps réel interrompue",-1)),p("button",{class:"retry-button",onClick:r}," Réessayer ")])])):v("",!0)])}}}),[["__scopeId","data-v-9c9c1482"]]),es={},ts=function(e,t,s){let n=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:"fulfilled",value:e}),e=>({status:"rejected",reason:e}))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));n=e(t.map(e=>{if((e=function(e){return"/client/dist/"+e}(e))in es)return;es[e]=!0;const t=e.endsWith(".css"),s=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${s}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script"),n.crossOrigin="",n.href=e,a&&n.setAttribute("nonce",a),document.head.appendChild(n),t?new Promise((t,s)=>{n.addEventListener("load",t),n.addEventListener("error",()=>s(new Error(`Unable to preload CSS for ${e}`)))}):void 0}))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then(t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)})};class ss{static async getStats(){var e,t;try{return(await $.routes.client.dashboard.getStats()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des statistiques")}}static async getOverview(){var e,t;try{return(await $.routes.client.dashboard.getOverview()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération de la vue d'ensemble")}}static async getRecentServices(){var e,t;try{return(await $.routes.client.service.getRecent()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des services récents")}}static async getRecentInvoices(){var e,t;try{return(await $.routes.client.invoice.getRecent()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des factures récentes")}}static async getUnpaidInvoices(){var e,t;try{return(await $.routes.client.invoice.getUnpaid()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des factures impayées")}}static async getRecentTickets(){var e,t;try{return(await $.routes.client.ticket.getRecent()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des tickets récents")}}static async getOpenTickets(){var e,t;try{return(await $.routes.client.ticket.getOpen()).data}catch(s){throw new Error((null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la récupération des tickets ouverts")}}}const ns=n("clientDashboard",()=>{const e=a(!1),t=a(null),s=a(null),n=a(null),r=a([]),o=a([]),l=a([]),c=a([]),u=a([]),d=a(!1),h=a(null),p=a(!1),f=i(()=>!!s.value),v=i(()=>{var e,t;return(null==(t=null==(e=s.value)?void 0:e.licenses)?void 0:t.total)||0}),m=i(()=>{var e,t;return(null==(t=null==(e=s.value)?void 0:e.licenses)?void 0:t.active)||0}),g=i(()=>{var e;return(null==(e=s.value)?void 0:e.invoices.total_due)||0}),y=i(()=>{var e,t;return((null==(e=s.value)?void 0:e.tickets.open)||0)+((null==(t=s.value)?void 0:t.tickets.in_progress)||0)}),b=async()=>{var e;try{const t=oe(),s=ke();if(!t.isAuthenticated||!(null==(e=t.user)?void 0:e.id))return void J.warn("[DASHBOARD STORE] Utilisateur non authentifié, impossible d'initialiser le temps réel");if(d.value)return void J.warn("[DASHBOARD STORE] Temps réel déjà initialisé");const n=t.user.id;if(J.info("[DASHBOARD STORE] Initialisation des mises à jour temps réel",{clientId:n}),!s.isReady)return J.warn("[DASHBOARD STORE] Service temps réel non prêt, tentative d'initialisation différée"),void setTimeout(()=>{d.value||b()},2e3);await s.subscribeToDashboardEvents(n),s.registerDashboardHandler("service",w),s.registerDashboardHandler("invoice",_),s.registerDashboardHandler("ticket",k),s.registerDashboardHandler("stats",C),d.value=!0,J.info("[DASHBOARD STORE] Temps réel initialisé avec succès")}catch(t){J.error("[DASHBOARD STORE] Erreur lors de l'initialisation du temps réel",{error:t.message}),d.value=!1}},w=async e=>{J.info("[DASHBOARD STORE] Événement service ignoré (services supprimés)",{event:e})},_=async e=>{if(J.info("[DASHBOARD STORE] Mise à jour facture reçue",{event:e}),J.info("[DASHBOARD STORE] 📊 ANALYSE STRUCTURE EVENT REÇU:",{event_keys:Object.keys(e),"event.action":e.action,"event.entity_type":e.entity_type,"event.data_exists":!!e.data,"event.data_keys":e.data?Object.keys(e.data):null,"event.data.invoice_exists":e.data?!!e.data.invoice:null,"event.invoice_exists":!!e.invoice,structure_complete:JSON.stringify(e,null,2)}),!e.data.invoice)return void J.error("[DASHBOARD STORE] ❌ AUCUNE DONNÉE FACTURE TROUVÉE dans event.data.invoice");p.value=!0;const t=e.data.invoice,n=e.action||e.data.action;switch(J.info("[DASHBOARD STORE] Traitement événement facture",{action:n,invoiceId:t.id,invoiceNumber:t.number}),n){case"invoice_create":case"create":case"created":J.info("[DASHBOARD STORE] Facture créée - récupération liste complète prévue",{invoiceId:t.id,invoiceNumber:t.number});break;case"invoice_update":case"update":case"updated":const e=o.value.findIndex(e=>e.id===t.id);-1!==e&&(o.value[e]={...o.value[e],...t},J.info("[DASHBOARD STORE] Facture mise à jour",{invoiceId:t.id,newStatus:t.status,number:o.value[e].number}));break;default:const s=o.value.findIndex(e=>e.id===t.id);-1!==s&&(o.value[s]={...o.value[s],...t},J.info("[DASHBOARD STORE] Facture mise à jour (action inconnue)",{invoiceId:t.id,action:n,number:o.value[s].number}))}if(s.value)if("invoice_create"===n||"create"===n||"created"===n){J.info("[DASHBOARD STORE] Récupération des données après modification de facture",{action:n});try{const e=await ss.getOverview();r.value=e.recent_licenses||[],o.value=e.recent_invoices,l.value=e.recent_tickets,c.value=e.unpaid_invoices,u.value=e.open_tickets;const t=await ss.getStats();s.value=t,J.info("[DASHBOARD STORE] Toutes les données mises à jour après modification facture",{services:t.services,recentInvoicesCount:e.recent_invoices.length,action:n})}catch(a){J.error("[DASHBOARD STORE] Erreur lors de la récupération des données",{error:a});const e=o.value.filter(e=>"unpaid"===e.status).length,t=o.value.filter(e=>"unpaid"===e.status).reduce((e,t)=>e+t.amount,0);s.value.invoices.unpaid=e,s.value.invoices.total_due=t}}else{const e=o.value.filter(e=>"unpaid"===e.status).length,t=o.value.filter(e=>"unpaid"===e.status).reduce((e,t)=>e+t.amount,0);s.value.invoices.unpaid=e,s.value.invoices.total_due=t,J.info("[DASHBOARD STORE] Statistiques factures recalculées localement",{unpaid:e,totalDue:t,action:n})}h.value=(new Date).toISOString(),setTimeout(()=>{p.value=!1},1e3)},k=async e=>{if(J.info("[DASHBOARD STORE] Mise à jour ticket reçue",{event:e}),!e.data.ticket)return;p.value=!0;const t=e.data.ticket,n=e.action||e.data.action;switch(J.info("[DASHBOARD STORE] Traitement événement ticket",{action:n,ticketId:t.id,ticketTitle:t.title}),n){case"ticket_create":case"create":case"created":J.info("[DASHBOARD STORE] Ticket créé - récupération liste complète prévue",{ticketId:t.id,ticketTitle:t.title});break;case"ticket_update":case"update":case"updated":const e=l.value.findIndex(e=>e.id===t.id);-1!==e&&(l.value[e]={...l.value[e],...t},J.info("[DASHBOARD STORE] Ticket mis à jour",{ticketId:t.id,newStatus:t.status,title:l.value[e].title}));break;default:const s=l.value.findIndex(e=>e.id===t.id);-1!==s&&(l.value[s]={...l.value[s],...t},J.info("[DASHBOARD STORE] Ticket mis à jour (action inconnue)",{ticketId:t.id,action:n,title:l.value[s].title}))}if(s.value)if("ticket_create"===n||"create"===n||"created"===n){J.info("[DASHBOARD STORE] Récupération des données après modification de ticket",{action:n});try{const e=await ss.getOverview();recentServices.value=e.recent_services,o.value=e.recent_invoices,l.value=e.recent_tickets,c.value=e.unpaid_invoices,u.value=e.open_tickets;const t=await ss.getStats();s.value=t,J.info("[DASHBOARD STORE] Toutes les données mises à jour après modification ticket",{services:t.services,recentTicketsCount:e.recent_tickets.length,action:n})}catch(a){J.error("[DASHBOARD STORE] Erreur lors de la récupération des données",{error:a});const e=l.value.filter(e=>"open"===e.status).length,t=l.value.filter(e=>"answered"===e.status).length;s.value.tickets.open=e,s.value.tickets.in_progress=t}}else{const e=l.value.filter(e=>"open"===e.status).length,t=l.value.filter(e=>"answered"===e.status).length;s.value.tickets.open=e,s.value.tickets.in_progress=t,J.info("[DASHBOARD STORE] Statistiques tickets recalculées localement",{open:e,inProgress:t,action:n})}h.value=(new Date).toISOString(),setTimeout(()=>{p.value=!1},1e3)},C=e=>{J.info("[DASHBOARD STORE] Mise à jour statistiques reçue",{event:e}),e.data.stats&&(p.value=!0,s.value=e.data.stats,h.value=(new Date).toISOString(),setTimeout(()=>{p.value=!1},1e3))};return{loading:e,error:t,stats:s,overview:n,recentLicenses:r,recentInvoices:o,recentTickets:l,unpaidInvoices:c,openTickets:u,realtimeInitialized:d,lastUpdate:h,isUpdating:p,hasData:f,totalLicenses:v,activeLicenses:m,totalUnpaidAmount:g,openTicketsCount:y,fetchStats:async()=>{e.value=!0,t.value=null;try{s.value=await ss.getStats()}catch(n){throw t.value=n.message||"Erreur lors de la récupération des statistiques",n}finally{e.value=!1}},fetchOverview:async()=>{e.value=!0,t.value=null;try{n.value=await ss.getOverview(),r.value=n.value.recent_licenses||[],o.value=n.value.recent_invoices,l.value=n.value.recent_tickets,c.value=n.value.unpaid_invoices,u.value=n.value.open_tickets}catch(s){throw t.value=s.message||"Erreur lors de la récupération de la vue d'ensemble",s}finally{e.value=!1}},fetchDashboardData:async()=>{e.value=!0,t.value=null;try{const[e,t]=await Promise.all([ss.getStats(),ss.getOverview()]);s.value=e,n.value=t,r.value=t.recent_licenses||[],o.value=t.recent_invoices,l.value=t.recent_tickets,c.value=t.unpaid_invoices,u.value=t.open_tickets}catch(a){throw t.value=a.message||"Erreur lors de la récupération des données du dashboard",a}finally{e.value=!1}},fetchRecentLicenses:async()=>{try{const e=await ss.getOverview();r.value=e.recent_licenses||[]}catch(e){throw t.value=e.message||"Erreur lors de la récupération des licences récentes",e}},fetchRecentInvoices:async()=>{try{o.value=await ss.getRecentInvoices()}catch(e){throw t.value=e.message||"Erreur lors de la récupération des factures récentes",e}},fetchRecentTickets:async()=>{try{l.value=await ss.getRecentTickets()}catch(e){throw t.value=e.message||"Erreur lors de la récupération des tickets récents",e}},clearError:()=>{t.value=null},resetData:()=>{s.value=null,n.value=null,r.value=[],o.value=[],l.value=[],c.value=[],u.value=[],t.value=null,e.value=!1,d.value=!1,h.value=null,p.value=!1},initRealtimeUpdates:b,stopRealtimeUpdates:()=>{const e=ke();J.info("[DASHBOARD STORE] Arrêt des mises à jour temps réel"),e.unsubscribeFromDashboardEvents(),e.unregisterDashboardHandler("service"),e.unregisterDashboardHandler("invoice"),e.unregisterDashboardHandler("ticket"),e.unregisterDashboardHandler("stats"),d.value=!1},handleServiceUpdate:w,handleInvoiceUpdate:_,handleTicketUpdate:k,handleStatsUpdate:C}}),as={id:"client-dashboard"},is={class:"header-box"},rs={class:"stats-grid box-grid"},os={class:"stat-card card-box"},ls={class:"stat-number"},cs={class:"stat-label"},us={class:"stat-card card-box"},ds={class:"stat-number"},hs={class:"stat-label"},ps={class:"stat-card card-box"},fs={class:"stat-number"},vs={class:"stat-label"},ms={class:"stat-card card-box"},gs={class:"stat-number"},ys={class:"stat-label"},bs={class:"dashboard-grid box-grid"},ws={class:"card card-box"},_s={class:"card-header"},ks={class:"card-title"},Cs={class:"card-body"},Ss={key:0,class:"loading-state"},Es={key:1,class:"license-list"},Rs={class:"license-info"},Ts={class:"license-details"},As={class:"license-key"},Os=["onClick"],Is={class:"license-domains"},Ms={class:"license-meta"},Ls={class:"license-date"},Ps={class:"license-status"},Ns={key:0,class:"no-data"},Us={class:"card card-box"},xs={class:"card-header"},Ds={class:"card-title"},Bs={class:"card-body"},Vs={key:0,class:"loading-state"},qs={key:1,class:"invoice-list"},Hs={class:"invoice-info"},Gs={class:"invoice-details"},js={class:"invoice-number"},Fs=["onClick"],$s={class:"invoice-date"},Ws={class:"invoice-meta"},zs={class:"invoice-amount"},Js={class:"invoice-status"},Ks={key:0,class:"no-data"},Ys={class:"card card-box"},Qs={class:"card-header"},Xs={class:"card-title"},Zs={class:"card-body"},en={key:0,class:"loading-state"},tn={key:1,class:"ticket-list"},sn={class:"ticket-info"},nn={class:"ticket-details"},an={class:"ticket-title"},rn=["onClick"],on={class:"ticket-id"},ln={class:"ticket-meta"},cn={class:"ticket-date"},un={class:"ticket-status"},dn={key:0,class:"no-data"},hn=r({__name:"DashboardView",setup(e){const t=o(),s=ns(),n=ke(),a=oe();function i(e){return new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e)}function r(e){const t=new Date(e);return new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"long",hour:"2-digit",minute:"2-digit"}).format(t)}function l(e){return{active:"status-badge status-success",suspended:"status-badge status-danger",paid:"status-badge status-success",unpaid:"status-badge status-warning",open:"status-badge status-info",in_progress:"status-badge status-warning",resolved:"status-badge status-success",closed:"status-badge status-secondary"}[e]||"status-badge status-default"}function f(){t.push("/licenses")}function y(){t.push("/billing")}function b(){t.push("/support")}return c(async()=>{try{J.info("[DASHBOARD] Chargement des données du dashboard"),await s.fetchDashboardData(),J.info("[DASHBOARD] Données du dashboard chargées avec succès"),a.isAuthenticated&&!n.initialized&&(await n.init(),J.info("[DASHBOARD] Service temps réel initialisé")),a.isAuthenticated&&!s.realtimeInitialized&&(await s.initRealtimeUpdates(),J.info("[DASHBOARD] Temps réel dashboard initialisé avec succès"))}catch(e){J.error("[DASHBOARD] Erreur lors du chargement des données",{error:e})}}),u(()=>{s.realtimeInitialized&&(s.stopRealtimeUpdates(),J.info("[DASHBOARD] Nettoyage du composant dashboard"))}),(e,n)=>{var a;return h(),d("div",as,[p("div",is,[p("h1",null,g(e.$t("dashboard.title")),1)]),p("div",rs,[p("div",os,[n[0]||(n[0]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-key"})],-1)),p("div",ls,g(A(s).totalLicenses),1),p("div",cs,g(e.$t("dashboard.my_licenses")),1)]),p("div",us,[n[1]||(n[1]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-file-invoice"})],-1)),p("div",ds,g((null==(a=A(s).stats)?void 0:a.invoices.unpaid)||0),1),p("div",hs,g(e.$t("dashboard.unpaid_invoices")),1)]),p("div",ps,[n[2]||(n[2]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-headset"})],-1)),p("div",fs,g(A(s).openTicketsCount),1),p("div",vs,g(e.$t("dashboard.open_tickets")),1)]),p("div",ms,[n[3]||(n[3]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-credit-card"})],-1)),p("div",gs,g(i(A(s).totalUnpaidAmount)),1),p("div",ys,g(e.$t("dashboard.total_due")),1)])]),p("div",bs,[p("div",ws,[p("div",_s,[p("h3",ks,[n[4]||(n[4]=p("i",{class:"fas fa-key"},null,-1)),L(" "+g(e.$t("dashboard.my_licenses")),1)]),p("button",{onClick:f,class:"btn btn-sm btn-outline"},g(e.$t("dashboard.view_all")),1)]),p("div",Cs,[A(s).loading?(h(),d("div",Ss,n[5]||(n[5]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):(h(),d("div",Es,[(h(!0),d(w,null,_(A(s).recentLicenses,s=>(h(),d("div",{key:s.id,class:"license-item"},[p("div",Rs,[n[6]||(n[6]=p("div",{class:"license-icon"},[p("i",{class:"fas fa-key"})],-1)),p("div",Ts,[p("div",As,[p("a",{onClick:k(e=>{return n=s.id,void t.push(`/licenses/${n}`);var n},["prevent"]),href:"#",class:"license-link"},g(s.license_key),9,Os)]),p("div",Is,g(s.allowed_domains),1)])]),p("div",Ms,[p("div",Ls,g(r(s.created_at)),1),p("div",Ps,[p("span",{class:m(l(s.status))},g(e.$t("status."+s.status)),3)])])]))),128)),0===A(s).recentLicenses.length?(h(),d("div",Ns,g(e.$t("licenses.no_licenses")),1)):v("",!0)]))])]),p("div",Us,[p("div",xs,[p("h3",Ds,[n[7]||(n[7]=p("i",{class:"fas fa-file-invoice"},null,-1)),L(" "+g(e.$t("dashboard.recent_invoices")),1)]),p("button",{onClick:y,class:"btn btn-sm btn-outline"},g(e.$t("dashboard.view_all")),1)]),p("div",Bs,[A(s).loading?(h(),d("div",Vs,n[8]||(n[8]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):(h(),d("div",qs,[(h(!0),d(w,null,_(A(s).recentInvoices,s=>(h(),d("div",{key:s.id,class:"invoice-item"},[p("div",Hs,[n[9]||(n[9]=p("div",{class:"invoice-icon"},[p("i",{class:"fas fa-file-invoice"})],-1)),p("div",Gs,[p("div",js,[p("a",{onClick:k(e=>{return n=s.id,void t.push(`/billing/invoice/${n}`);var n},["prevent"]),href:"#",class:"invoice-link"},g(e.$t("billing.invoice_number"))+" #"+g(s.number),9,Fs)]),p("div",$s,g(r(s.created_at)),1)])]),p("div",Ws,[p("div",zs,g(i(s.amount)),1),p("div",Js,[p("span",{class:m(l(s.status))},g(e.$t("status."+s.status)),3)])])]))),128)),0===A(s).recentInvoices.length?(h(),d("div",Ks,g(e.$t("billing.no_invoices")),1)):v("",!0)]))])])]),p("div",Ys,[p("div",Qs,[p("h3",Xs,[n[10]||(n[10]=p("i",{class:"fas fa-headset"},null,-1)),L(" "+g(e.$t("dashboard.recent_tickets")),1)]),p("button",{onClick:b,class:"btn btn-sm btn-outline"},g(e.$t("dashboard.view_all")),1)]),p("div",Zs,[A(s).loading?(h(),d("div",en,n[11]||(n[11]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1)]))):(h(),d("div",tn,[(h(!0),d(w,null,_(A(s).recentTickets,s=>(h(),d("div",{key:s.id,class:"ticket-item"},[p("div",sn,[n[12]||(n[12]=p("div",{class:"ticket-icon"},[p("i",{class:"fas fa-headset"})],-1)),p("div",nn,[p("div",an,[p("a",{onClick:k(e=>{return n=s.id,void t.push(`/support/ticket/${n}`);var n},["prevent"]),href:"#",class:"ticket-link"},g(s.title),9,rn)]),p("div",on," #"+g(s.id),1)])]),p("div",ln,[p("div",cn,g(r(s.created_at)),1),p("div",un,[p("span",{class:m(l(s.status))},g(e.$t("status."+s.status)),3)])])]))),128)),0===A(s).recentTickets.length?(h(),d("div",dn,g(e.$t("support.no_tickets")),1)):v("",!0)]))])])])}}}),pn=He(hn,[["__scopeId","data-v-dcf5c35a"]]),fn=n("invoices",()=>{const e=a([]),t=a(!1),s=a(null),n=a(null),r=a(!1),o=i(()=>t=>e.value.find(e=>e.id===t)||null),l=i(()=>t=>e.value.filter(e=>e.status===t)),c=i(()=>e.value.filter(e=>"unpaid"===e.status)),u=i(()=>e.value.filter(e=>"paid"===e.status)),d=i(()=>e.value.filter(e=>"overdue"===e.status)),h=i(()=>c.value.reduce((e,t)=>e+t.amount,0)),p=i(()=>u.value.reduce((e,t)=>e+t.amount,0)),f=i(()=>e.value.length),v=async()=>{var a,i;t.value=!0,s.value=null;try{const t=await $.routes.client.invoice.list();return e.value=t.data,n.value=(new Date).toISOString(),J.info("[INVOICES STORE] Factures chargées",{count:e.value.length,unpaid:c.value.length,paid:u.value.length,overdue:d.value.length,totalDue:h.value}),t.data}catch(r){throw J.error("[INVOICES STORE] Erreur lors du chargement des factures",{error:r}),s.value=(null==(i=null==(a=r.response)?void 0:a.data)?void 0:i.message)||"Erreur lors du chargement des factures",r}finally{t.value=!1}};return{invoices:e,loading:t,error:s,lastUpdate:n,isUpdating:r,getInvoiceById:o,getInvoicesByStatus:l,unpaidInvoices:c,paidInvoices:u,overdueInvoices:d,totalDue:h,totalPaid:p,totalInvoices:f,fetchInvoices:v,refreshInvoices:async()=>(J.info("[INVOICES STORE] Actualisation des factures"),await v()),handleInvoiceUpdate:async t=>{if(J.info("[INVOICES STORE] Mise à jour facture reçue",{event:t}),J.info("[INVOICES STORE] 📊 ANALYSE STRUCTURE EVENT REÇU:",{event_keys:Object.keys(t),"event.action":t.action,"event.entity_type":t.entity_type,"event.data_exists":!!t.data,"event.data_keys":t.data?Object.keys(t.data):null,"event.data.invoice_exists":t.data?!!t.data.invoice:null,"event.invoice_exists":!!t.invoice,structure_complete:JSON.stringify(t,null,2)}),!t.data.invoice)return void J.error("[INVOICES STORE] ❌ AUCUNE DONNÉE FACTURE TROUVÉE dans event.data.invoice");r.value=!0;const s=t.data.invoice,a=t.action||t.data.action,i={id:s.id,client_id:s.client_id,number:s.number||`INV-${s.id}`,amount:parseFloat(s.amount||0),status:s.status||"unpaid",created_at:s.created_at,due_date:s.due_date,notes:s.notes||"",updated_at:s.updated_at};switch(J.info("[INVOICES STORE] Traitement événement facture",{action:a,invoiceId:i.id,invoiceNumber:i.number,invoiceStatus:i.status,invoiceAmount:i.amount}),a){case"invoice_create":case"create":case"created":J.info("[INVOICES STORE] Facture créée - récupération liste complète prévue",{invoiceId:i.id,invoiceNumber:i.number});try{await v(),J.info("[INVOICES STORE] Liste des factures mise à jour après création")}catch(o){J.error("[INVOICES STORE] Erreur lors de la récupération après création",{error:o});-1===e.value.findIndex(e=>e.id===i.id)&&e.value.unshift(i)}break;case"invoice_update":case"update":case"updated":const t=e.value.findIndex(e=>e.id===i.id);if(-1!==t){const s=e.value[t].status;e.value[t]={...e.value[t],...i},J.info("[INVOICES STORE] Facture mise à jour",{invoiceId:i.id,oldStatus:s,newStatus:i.status,invoiceNumber:e.value[t].number})}break;default:const s=e.value.findIndex(e=>e.id===i.id);-1!==s&&(e.value[s]={...e.value[s],...i},J.info("[INVOICES STORE] Facture mise à jour (action inconnue)",{invoiceId:i.id,action:a,invoiceNumber:e.value[s].number}))}n.value=(new Date).toISOString(),setTimeout(()=>{r.value=!1},1e3)},clearError:()=>{s.value=null},reset:()=>{e.value=[],t.value=!1,s.value=null,n.value=null,r.value=!1}}}),vn=e=>{if(!e)return!1;if("string"==typeof e&&""===e.trim())return!1;const t=new Date(e);return!isNaN(t.getTime())},mn=e=>{if(!vn(e))return"Date non définie";try{const t=new Date(e);return new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"short",year:"numeric",timeZone:"Europe/Paris"}).format(t)}catch(t){return J.warn("[DATE_UTILS] Erreur lors du formatage de la date (court)",{date:e,error:t}),"Date invalide"}},gn=e=>{if(!vn(e))return"Date non définie";try{const t=new Date(e);return new Intl.DateTimeFormat("fr-FR",{day:"numeric",month:"long",year:"numeric",timeZone:"Europe/Paris"}).format(t)}catch(t){return J.warn("[DATE_UTILS] Erreur lors du formatage de la date (long)",{date:e,error:t}),"Date invalide"}},yn=(e,t=!0)=>{if(!(e=>{if(null==e)return!1;if("string"==typeof e&&""===e.trim())return!1;const t=Number(e);return!isNaN(t)&&isFinite(t)})(e))return t?"Gratuit":"0,00 €";try{const s=Number(e);return 0===s&&t?"Gratuit":new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(s)}catch(s){return J.warn("[DATE_UTILS] Erreur lors du formatage du prix",{value:e,error:s}),t?"Gratuit":"0,00 €"}},bn={id:"client-billing"},wn={class:"billing-header"},_n={class:"billing-actions"},kn={class:"btn btn-outline btn-sm"},Cn={class:"btn btn-primary btn-sm"},Sn={class:"billing-summary"},En={class:"summary-card"},Rn={class:"summary-value"},Tn={class:"summary-label"},An={class:"summary-card"},On={class:"summary-value"},In={class:"summary-label"},Mn={class:"summary-card"},Ln={class:"summary-value"},Pn={class:"summary-label"},Nn={class:"summary-card"},Un={class:"summary-value"},xn={class:"summary-label"},Dn={class:"billing-filters"},Bn={class:"filter-group"},Vn={class:"filter-label"},qn={value:""},Hn={value:"paid"},Gn={value:"unpaid"},jn={value:"overdue"},Fn={value:"draft"},$n={class:"filter-group"},Wn={class:"filter-label"},zn={value:""},Jn={value:"current-month"},Kn={value:"last-month"},Yn={value:"current-year"},Qn={value:"last-year"},Xn={class:"filter-group"},Zn={class:"filter-label"},ea=["placeholder"],ta={class:"invoices-table-container"},sa={key:0,class:"loading-state"},na={key:1,class:"error-state"},aa={key:2,class:"empty-state"},ia={key:3,class:"invoices-table"},ra=["onClick"],oa={class:"invoice-actions"},la=["onClick"],ca=["onClick"],ua=["onClick"],da=r({__name:"BillingView",setup(e){const t=o(),s=fn(),n=ke(),r=oe(),l=a(""),f=a(""),y=a(""),b=i(()=>s.loading),C=i(()=>s.error),S=i(()=>s.invoices),E=async()=>{try{await s.fetchInvoices(),J.info("[BILLING VIEW] Factures chargées depuis le store",{count:S.value.length})}catch(e){J.error("[BILLING VIEW] Erreur lors du chargement des factures",{error:e})}},I=i(()=>s.totalInvoices),M=i(()=>s.unpaidInvoices),P=i(()=>s.totalDue),U=i(()=>s.totalPaid),x=i(()=>{let e=S.value;if(l.value&&(e=e.filter(e=>e.status===l.value)),f.value){const t=new Date;e=e.filter(e=>{const s=new Date(e.created_at);switch(f.value){case"current-month":return s.getMonth()===t.getMonth()&&s.getFullYear()===t.getFullYear();case"last-month":const e=new Date(t.getFullYear(),t.getMonth()-1);return s.getMonth()===e.getMonth()&&s.getFullYear()===e.getFullYear();case"current-year":return s.getFullYear()===t.getFullYear();case"last-year":return s.getFullYear()===t.getFullYear()-1;default:return!0}})}if(y.value){const t=y.value.toLowerCase();e=e.filter(e=>e.number.toLowerCase().includes(t)||e.notes&&e.notes.toLowerCase().includes(t))}return e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}),D=()=>{},B=e=>yn(e,!1),V=mn,q=e=>({paid:"status-badge status-paid",unpaid:"status-badge status-unpaid",draft:"status-badge status-draft",cancelled:"status-badge status-cancelled"}[e]||"status-badge"),H=e=>({paid:"Payée",unpaid:"Impayée",draft:"Brouillon",cancelled:"Annulée"}[e]||e),G=e=>{t.push(`/billing/invoice/${e}`)};return c(async()=>{var e;await E();const t=null==(e=r.user)?void 0:e.id;if(t)if(n.initialized)await n.subscribeToDashboardEvents(t),n.registerDashboardHandler("invoice-page",s.handleInvoiceUpdate),J.info("[BILLING VIEW] Handler temps réel enregistré avec clé invoice-page",{clientId:t});else{const e=n.$subscribe((a,i)=>{i.initialized&&(n.subscribeToDashboardEvents(t).then(()=>{n.registerDashboardHandler("invoice-page",s.handleInvoiceUpdate),J.info("[BILLING VIEW] Handler temps réel enregistré (après initialisation) avec clé invoice-page",{clientId:t})}),e())})}else J.error("[BILLING VIEW] ID client manquant - abandon initialisation temps réel",{user:r.user})}),u(()=>{n.unregisterDashboardHandler("invoice-page"),J.info("[BILLING VIEW] Handler temps réel supprimé pour clé invoice-page")}),(e,t)=>(h(),d("div",bn,[p("div",wn,[p("h1",null,[t[4]||(t[4]=p("i",{class:"fas fa-file-invoice"},null,-1)),L(" "+g(e.$t("billing.title")),1)]),p("div",_n,[p("button",kn,[t[5]||(t[5]=p("i",{class:"fas fa-download"},null,-1)),L(" "+g(e.$t("common.export")),1)]),p("button",Cn,[t[6]||(t[6]=p("i",{class:"fas fa-credit-card"},null,-1)),L(" "+g(e.$t("billing.pay_unpaid")),1)])])]),p("div",Sn,[p("div",En,[t[7]||(t[7]=p("div",{class:"summary-icon"},[p("i",{class:"fas fa-file-invoice"})],-1)),p("div",Rn,g(I.value),1),p("div",Tn,g(e.$t("billing.total_invoices")),1)]),p("div",An,[t[8]||(t[8]=p("div",{class:"summary-icon"},[p("i",{class:"fas fa-exclamation-triangle"})],-1)),p("div",On,g(M.value.length),1),p("div",In,g(e.$t("billing.unpaid_invoices")),1)]),p("div",Mn,[t[9]||(t[9]=p("div",{class:"summary-icon"},[p("i",{class:"fas fa-euro-sign"})],-1)),p("div",Ln,g(B(P.value)),1),p("div",Pn,g(e.$t("billing.amount_due")),1)]),p("div",Nn,[t[10]||(t[10]=p("div",{class:"summary-icon"},[p("i",{class:"fas fa-chart-line"})],-1)),p("div",Un,g(B(U.value)),1),p("div",xn,g(e.$t("billing.total_paid")),1)])]),p("div",Dn,[p("div",Bn,[p("label",Vn,g(e.$t("common.status")),1),R(p("select",{"onUpdate:modelValue":t[0]||(t[0]=e=>l.value=e),class:"filter-select",onChange:D},[p("option",qn,g(e.$t("billing.all_statuses")),1),p("option",Hn,g(e.$t("status.paid")),1),p("option",Gn,g(e.$t("status.unpaid")),1),p("option",jn,g(e.$t("status.overdue")),1),p("option",Fn,g(e.$t("billing.draft")),1)],544),[[N,l.value]])]),p("div",$n,[p("label",Wn,g(e.$t("billing.period")),1),R(p("select",{"onUpdate:modelValue":t[1]||(t[1]=e=>f.value=e),class:"filter-select",onChange:D},[p("option",zn,g(e.$t("billing.all_periods")),1),p("option",Jn,g(e.$t("billing.current_month")),1),p("option",Kn,g(e.$t("billing.last_month")),1),p("option",Yn,g(e.$t("billing.current_year")),1),p("option",Qn,g(e.$t("billing.last_year")),1)],544),[[N,f.value]])]),p("div",Xn,[p("label",Zn,g(e.$t("common.search")),1),R(p("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>y.value=e),type:"text",class:"filter-select",placeholder:e.$t("billing.search_placeholder"),onInput:D},null,40,ea),[[T,y.value]])])]),p("div",ta,[t[21]||(t[21]=O('<div class="table-header" data-v-fe32b381><h3 class="table-title" data-v-fe32b381><i class="fas fa-list" data-v-fe32b381></i> Historique des Factures </h3><div class="billing-actions" data-v-fe32b381><button class="btn btn-outline btn-sm" data-v-fe32b381><i class="fas fa-filter" data-v-fe32b381></i> Filtres avancés </button></div></div>',1)),b.value?(h(),d("div",sa,t[11]||(t[11]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1),p("p",null,"Chargement de vos factures...",-1)]))):C.value?(h(),d("div",na,[t[13]||(t[13]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),t[14]||(t[14]=p("h3",null,"Erreur de chargement",-1)),p("p",null,g(C.value),1),p("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=e=>E())},t[12]||(t[12]=[p("i",{class:"fas fa-redo"},null,-1),L(" Réessayer ")]))])):0===x.value.length?(h(),d("div",aa,[t[15]||(t[15]=p("i",{class:"fas fa-file-invoice"},null,-1)),t[16]||(t[16]=p("h3",null,"Aucune facture trouvée",-1)),p("p",null,g(y.value||l.value||f.value?"Aucune facture ne correspond à vos critères de recherche.":"Vous n'avez pas encore de factures."),1)])):(h(),d("table",ia,[t[20]||(t[20]=p("thead",null,[p("tr",null,[p("th",null,"Numéro"),p("th",null,"Date"),p("th",null,"Description"),p("th",null,"Montant"),p("th",null,"Statut"),p("th",null,"Échéance"),p("th",null,"Actions")])],-1)),p("tbody",null,[(h(!0),d(w,null,_(x.value,e=>{return h(),d("tr",{key:e.id},[p("td",null,[p("a",{href:"#",class:"invoice-number",onClick:k(t=>G(e.id),["prevent"])}," #"+g(e.number),9,ra)]),p("td",null,g(A(V)(e.created_at)),1),p("td",null,g(e.notes||"Facture #"+e.number),1),p("td",null,[p("span",{class:m(["invoice-amount",(s=e.status,"unpaid"===s?"unpaid":"paid"===s?"paid":"cancelled"===s?"cancelled":"")])},g(B(e.amount)),3)]),p("td",null,[p("span",{class:m(q(e.status))},g(H(e.status)),3)]),p("td",null,g(A(V)(e.due_date)),1),p("td",null,[p("div",oa,[p("button",{class:"btn btn-outline btn-sm",onClick:t=>G(e.id)},t[17]||(t[17]=[p("i",{class:"fas fa-eye"},null,-1),L(" Voir ")]),8,la),p("button",{class:"btn btn-outline btn-sm",onClick:t=>{return s=e.id,void J.info("[BILLING] Téléchargement facture demandé",{invoiceId:s});var s}},t[18]||(t[18]=[p("i",{class:"fas fa-download"},null,-1),L(" PDF ")]),8,ca),"unpaid"===e.status?(h(),d("button",{key:0,class:"btn btn-success btn-sm",onClick:t=>{return s=e.id,void J.info("[BILLING] Paiement facture demandé",{invoiceId:s});var s}},t[19]||(t[19]=[p("i",{class:"fas fa-credit-card"},null,-1),L(" Payer ")]),8,ua)):v("",!0)])])]);var s}),128))])]))])]))}}),ha=He(da,[["__scopeId","data-v-fe32b381"]]),pa=n("tickets",()=>{const e=a([]),t=a(!1),s=a(null),n=a(null),r=a(!1),o=i(()=>t=>e.value.find(e=>e.id===t)||null),l=i(()=>t=>e.value.filter(e=>e.status===t)),c=i(()=>e.value.filter(e=>"open"===e.status)),u=i(()=>e.value.filter(e=>"in-progress"===e.status)),d=i(()=>e.value.filter(e=>"resolved"===e.status)),h=i(()=>e.value.filter(e=>"closed"===e.status)),p=async()=>{var a,i;t.value=!0,s.value=null;try{const t=await $.routes.client.ticket.list();return e.value=t.data.map(e=>({...e,id:parseInt(String(e.id)),client_id:parseInt(String(e.client_id)),title:e.subject||e.title})),n.value=(new Date).toISOString(),J.info("[TICKETS STORE] Tickets chargés avec conversion IDs",{count:e.value.length,ticketIds:e.value.map(e=>({id:e.id,title:e.title})),open:c.value.length,inProgress:u.value.length,resolved:d.value.length,closed:h.value.length}),e.value}catch(r){throw J.error("[TICKETS STORE] Erreur lors du chargement des tickets",{error:r}),s.value=(null==(i=null==(a=r.response)?void 0:a.data)?void 0:i.message)||"Erreur lors du chargement des tickets",r}finally{t.value=!1}};return{tickets:e,loading:t,error:s,lastUpdate:n,isUpdating:r,getTicketById:o,getTicketsByStatus:l,openTickets:c,inProgressTickets:u,resolvedTickets:d,closedTickets:h,fetchTickets:p,refreshTickets:async()=>(J.info("[TICKETS STORE] Actualisation des tickets"),await p()),handleTicketUpdate:async t=>{if(J.info("[TICKETS STORE] Mise à jour ticket reçue",{event:t}),!t.data.ticket)return void J.error("[TICKETS STORE] ❌ AUCUNE DONNÉE TICKET TROUVÉE dans event.data.ticket");const s=t.data.ticket,a=t.action||t.data.action;J.info("[TICKETS STORE] Traitement événement ticket",{action:a,ticketId:s.id,ticketTitle:s.title||s.subject,currentTicketsCount:e.value.length});const i={id:parseInt(String(s.id)),title:s.subject||s.title,status:s.status,priority:s.priority,department_name:s.department_name,created_at:s.created_at,updated_at:s.updated_at,last_reply_at:s.last_reply_at,client_id:parseInt(String(s.client_id))};switch(J.info("[TICKETS STORE] Ticket converti",{originalId:s.id,convertedId:i.id,title:i.title,status:i.status}),a){case"ticket_create":case"create":case"created":-1===e.value.findIndex(e=>e.id===i.id)&&(e.value.unshift(i),J.info("[TICKETS STORE] Nouveau ticket ajouté",{ticketId:i.id,ticketTitle:i.title}));break;case"ticket_update":case"update":case"updated":J.info("[TICKETS STORE] Recherche ticket à mettre à jour",{searchId:i.id,currentTickets:e.value.map(e=>({id:e.id,title:e.title}))});const t=e.value.findIndex(e=>e.id===i.id);if(-1!==t){const s={...e.value[t]};e.value[t]={...e.value[t],...i},J.info("[TICKETS STORE] ✅ Ticket mis à jour avec succès",{ticketId:i.id,oldTitle:s.title,newTitle:e.value[t].title,oldStatus:s.status,newStatus:e.value[t].status,updateIndex:t})}else J.error("[TICKETS STORE] ❌ Ticket non trouvé pour mise à jour",{searchId:i.id,availableIds:e.value.map(e=>e.id)});break;case"ticket_delete":case"delete":case"deleted":const s=e.value.findIndex(e=>e.id===i.id);-1!==s&&(e.value.splice(s,1),J.info("[TICKETS STORE] Ticket supprimé",{ticketId:i.id,ticketTitle:i.title}));break;default:J.warn("[TICKETS STORE] Action non reconnue",{action:a})}n.value=(new Date).toISOString()}}}),fa={id:"client-support"},va={class:"support-header"},ma={class:"support-actions"},ga={class:"quick-actions"},ya={class:"action-title"},ba={class:"action-description"},wa={class:"action-card"},_a={class:"action-title"},ka={class:"action-description"},Ca={class:"action-card"},Sa={class:"action-title"},Ea={class:"action-description"},Ra={class:"action-card"},Ta={class:"action-title"},Aa={class:"action-description"},Oa={class:"tickets-section"},Ia={class:"tickets-container"},Ma={class:"tickets-header"},La={class:"tickets-filters"},Pa={key:0,class:"loading-state"},Na={key:1,class:"error-state"},Ua={key:2,class:"empty-state"},xa={key:3,class:"tickets-list"},Da={class:"ticket-info"},Ba={class:"ticket-icon"},Va={class:"ticket-details"},qa={class:"ticket-title"},Ha=["onClick"],Ga={class:"ticket-id"},ja={class:"ticket-meta"},Fa={class:"ticket-date"},$a={class:"ticket-status"},Wa={class:"form-group"},za={class:"form-group"},Ja=["value"],Ka={class:"form-group"},Ya={class:"form-group"},Qa=r({__name:"SupportView",setup(e){const t=o(),s=pa(),n=ke(),r=oe(),l=a([]),f=a(""),v=a(""),y=a(""),b=a(!1),C=a(!1),S=a(""),E=a(null),I=a({subject:"",department_id:"",priority:"medium",message:""}),M=async()=>{var e,t;try{E.value=null,await s.fetchTickets(),J.info("[SUPPORT] Tickets chargés depuis le store",{count:s.tickets.length})}catch(n){J.error("[SUPPORT] Erreur lors du chargement des tickets",{error:n}),E.value=(null==(t=null==(e=n.response)?void 0:e.data)?void 0:t.message)||"Erreur lors du chargement des tickets"}},P=async()=>{try{const e=await $.routes.client.department.list();l.value=e.data,J.info("[SUPPORT] Départements chargés",{count:l.value.length})}catch(e){J.error("[SUPPORT] Erreur lors du chargement des départements",{error:e}),l.value=[{id:1,name:"Support Technique",description:"Problèmes techniques",email:"<EMAIL>",active:!0,created_at:"",updated_at:""},{id:2,name:"Facturation",description:"Questions de facturation",email:"<EMAIL>",active:!0,created_at:"",updated_at:""},{id:3,name:"Commercial",description:"Questions commerciales",email:"<EMAIL>",active:!0,created_at:"",updated_at:""}]}},U=i(()=>{let e=s.tickets;return f.value&&(e=e.filter(e=>e.status===f.value)),v.value&&(e=e.filter(e=>e.priority===v.value)),e.sort((e,t)=>new Date(t.created_at).getTime()-new Date(e.created_at).getTime())}),x=()=>{},D=e=>({open:"Ouvert",answered:"Répondu","customer-reply":"En attente",closed:"Fermé"}[e]||e),B=mn,V=e=>{if(!e)return"fas fa-ticket-alt";return{"Support Technique":"fas fa-cog",Technique:"fas fa-cog",Facturation:"fas fa-euro-sign",Commercial:"fas fa-handshake","Général":"fas fa-question-circle",Demande:"fas fa-hand-paper"}[e]||"fas fa-ticket-alt"},q=()=>{b.value=!1,S.value="",E.value=null,I.value={subject:"",department_id:"",priority:"medium",message:""}},H=async()=>{var e,t;if(I.value.subject&&I.value.message)try{C.value=!0,E.value=null,J.info("[SUPPORT] Création du ticket",{subject:I.value.subject,priority:I.value.priority});const e={subject:I.value.subject,message:I.value.message,priority:I.value.priority,department_id:I.value.department_id?parseInt(I.value.department_id):void 0};await $.routes.client.ticket.create(e),S.value="Ticket créé avec succès",q(),await s.refreshTickets()}catch(n){J.error("[SUPPORT] Erreur lors de la création du ticket",{error:n,ticketData:I.value}),E.value=(null==(t=null==(e=n.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la création du ticket"}finally{C.value=!1}else E.value="Veuillez remplir tous les champs obligatoires"};return c(async()=>{await Promise.all([M(),P()]),await(async()=>{var e;const t=null==(e=r.user)?void 0:e.id;if(t)if(n.initialized)await n.subscribeToDashboardEvents(t),n.registerDashboardHandler("ticket-page",s.handleTicketUpdate),J.info("[SUPPORT VIEW] Handler temps réel enregistré avec clé ticket-page",{clientId:t});else{const e=n.$subscribe((a,i)=>{i.initialized&&(n.subscribeToDashboardEvents(t).then(()=>{n.registerDashboardHandler("ticket-page",s.handleTicketUpdate),J.info("[SUPPORT VIEW] Handler temps réel enregistré (après initialisation) avec clé ticket-page",{clientId:t})}),e())})}else J.error("[SUPPORT VIEW] ID client manquant - abandon initialisation temps réel",{user:r.user})})()}),u(()=>{n.unregisterDashboardHandler("ticket-page"),J.info("[SUPPORT VIEW] Handler temps réel supprimé avec clé ticket-page")}),(e,n)=>(h(),d("div",fa,[p("div",va,[p("h1",null,[n[11]||(n[11]=p("i",{class:"fas fa-headset"},null,-1)),L(" "+g(e.$t("support.title")),1)]),p("div",ma,[n[13]||(n[13]=p("button",{class:"btn btn-outline btn-sm"},[p("i",{class:"fas fa-book"}),L(" Base de connaissances ")],-1)),p("button",{class:"btn btn-primary btn-sm",onClick:n[0]||(n[0]=e=>b.value=!0)},[n[12]||(n[12]=p("i",{class:"fas fa-plus"},null,-1)),L(" "+g(e.$t("support.new_ticket")),1)])])]),p("div",ga,[p("div",{class:"action-card",onClick:n[1]||(n[1]=e=>b.value=!0)},[n[14]||(n[14]=p("div",{class:"action-icon"},[p("i",{class:"fas fa-plus"})],-1)),p("div",ya,g(e.$t("support.create_ticket")),1),p("div",ba,g(e.$t("support.create_ticket_desc")),1)]),p("div",wa,[n[15]||(n[15]=p("div",{class:"action-icon"},[p("i",{class:"fas fa-book"})],-1)),p("div",_a,g(e.$t("support.knowledge_base")),1),p("div",ka,g(e.$t("support.knowledge_base_desc")),1)]),p("div",Ca,[n[16]||(n[16]=p("div",{class:"action-icon"},[p("i",{class:"fas fa-comments"})],-1)),p("div",Sa,g(e.$t("support.live_chat")),1),p("div",Ea,g(e.$t("support.live_chat_desc")),1)]),p("div",Ra,[n[17]||(n[17]=p("div",{class:"action-icon"},[p("i",{class:"fas fa-phone"})],-1)),p("div",Ta,g(e.$t("support.phone_support")),1),p("div",Aa,g(e.$t("support.phone_support_desc")),1)])]),p("div",Oa,[p("div",Ia,[p("div",Ma,[n[20]||(n[20]=p("h3",{class:"tickets-title"},[p("i",{class:"fas fa-ticket-alt"}),L(" Mes Tickets de Support ")],-1)),p("div",La,[R(p("select",{"onUpdate:modelValue":n[2]||(n[2]=e=>f.value=e),class:"filter-select",onChange:x},n[18]||(n[18]=[O('<option value="" data-v-df8ea63a>Tous les statuts</option><option value="open" data-v-df8ea63a>Ouvert</option><option value="in-progress" data-v-df8ea63a>En cours</option><option value="resolved" data-v-df8ea63a>Résolu</option><option value="closed" data-v-df8ea63a>Fermé</option>',5)]),544),[[N,f.value]]),R(p("select",{"onUpdate:modelValue":n[3]||(n[3]=e=>v.value=e),class:"filter-select",onChange:x},n[19]||(n[19]=[O('<option value="" data-v-df8ea63a>Toutes les priorités</option><option value="low" data-v-df8ea63a>Faible</option><option value="medium" data-v-df8ea63a>Moyenne</option><option value="high" data-v-df8ea63a>Haute</option><option value="urgent" data-v-df8ea63a>Urgente</option>',5)]),544),[[N,v.value]])])]),A(s).loading?(h(),d("div",Pa,n[21]||(n[21]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1),p("p",null,"Chargement de vos tickets...",-1)]))):E.value?(h(),d("div",Na,[n[23]||(n[23]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),n[24]||(n[24]=p("h3",null,"Erreur de chargement",-1)),p("p",null,g(E.value),1),p("button",{class:"btn btn-primary",onClick:n[4]||(n[4]=e=>M())},n[22]||(n[22]=[p("i",{class:"fas fa-redo"},null,-1),L(" Réessayer ")]))])):0===U.value.length?(h(),d("div",Ua,[n[26]||(n[26]=p("i",{class:"fas fa-ticket-alt"},null,-1)),n[27]||(n[27]=p("h3",null,"Aucun ticket trouvé",-1)),p("p",null,g(y.value||f.value||v.value?"Aucun ticket ne correspond à vos critères.":"Vous n'avez pas encore de tickets de support."),1),p("button",{class:"btn btn-primary",onClick:n[5]||(n[5]=e=>b.value=!0)},n[25]||(n[25]=[p("i",{class:"fas fa-plus"},null,-1),L(" Créer votre premier ticket ")]))])):(h(),d("div",xa,[(h(!0),d(w,null,_(U.value,e=>{return h(),d("div",{key:e.id,class:"ticket-item"},[p("div",Da,[p("div",Ba,[p("i",{class:m(V(e.department_name))},null,2)]),p("div",Va,[p("div",qa,[p("a",{href:"#",onClick:k(s=>{return n=e.id,void t.push(`/support/ticket/${n}`);var n},["prevent"])},g(e.title),9,Ha)]),p("div",Ga," #"+g(e.id)+" • "+g(e.department_name||"Général"),1)])]),p("div",ja,[p("div",Fa,g(A(B)(e.created_at)),1),p("div",$a,[p("span",{class:m((s=e.status,{open:"status-badge status-open","in-progress":"status-badge status-in-progress",resolved:"status-badge status-resolved",closed:"status-badge status-closed"}[s]||"status-badge"))},g(D(e.status)),3)])])]);var s}),128))]))])]),p("div",{class:m(["modal-overlay",{show:b.value}]),onClick:q},[p("div",{class:"modal-content",onClick:n[10]||(n[10]=k(()=>{},["stop"]))},[p("div",{class:"modal-header"},[n[29]||(n[29]=p("h3",{class:"modal-title"},"Créer un Nouveau Ticket",-1)),p("button",{class:"modal-close",onClick:q},n[28]||(n[28]=[p("i",{class:"fas fa-times"},null,-1)]))]),p("form",{onSubmit:k(H,["prevent"])},[p("div",Wa,[n[30]||(n[30]=p("label",{class:"form-label"},"Sujet",-1)),R(p("input",{"onUpdate:modelValue":n[6]||(n[6]=e=>I.value.subject=e),type:"text",class:"form-input",placeholder:"Décrivez brièvement votre problème...",required:""},null,512),[[T,I.value.subject]])]),p("div",za,[n[32]||(n[32]=p("label",{class:"form-label"},"Département",-1)),R(p("select",{"onUpdate:modelValue":n[7]||(n[7]=e=>I.value.department_id=e),class:"form-select",required:""},[n[31]||(n[31]=p("option",{value:""},"Sélectionnez un département",-1)),(h(!0),d(w,null,_(l.value,e=>(h(),d("option",{key:e.id,value:e.id.toString()},g(e.name),9,Ja))),128))],512),[[N,I.value.department_id]])]),p("div",Ka,[n[34]||(n[34]=p("label",{class:"form-label"},"Priorité",-1)),R(p("select",{"onUpdate:modelValue":n[8]||(n[8]=e=>I.value.priority=e),class:"form-select",required:""},n[33]||(n[33]=[O('<option value="" data-v-df8ea63a>Sélectionnez une priorité</option><option value="low" data-v-df8ea63a>Faible</option><option value="medium" data-v-df8ea63a>Moyenne</option><option value="high" data-v-df8ea63a>Haute</option><option value="urgent" data-v-df8ea63a>Urgente</option>',5)]),512),[[N,I.value.priority]])]),p("div",Ya,[n[35]||(n[35]=p("label",{class:"form-label"},"Description",-1)),R(p("textarea",{"onUpdate:modelValue":n[9]||(n[9]=e=>I.value.message=e),class:"form-textarea",placeholder:"Décrivez votre problème en détail...",required:""},null,512),[[T,I.value.message]])]),p("div",{class:"modal-actions"},[p("button",{type:"button",class:"btn btn-outline",onClick:q}," Annuler "),n[36]||(n[36]=p("button",{type:"submit",class:"btn btn-primary"},[p("i",{class:"fas fa-paper-plane"}),L(" Créer le Ticket ")],-1))])],32)])],2)]))}}),Xa=He(Qa,[["__scopeId","data-v-df8ea63a"]]),Za={id:"client-account"},ei={class:"account-header"},ti={class:"account-actions"},si={class:"btn btn-outline btn-sm"},ni={key:0,class:"success-message"},ai={key:1,class:"error-message"},ii={class:"account-grid"},ri={class:"account-card"},oi={class:"card-header"},li={class:"card-title"},ci={class:"card-body"},ui={class:"profile-avatar"},di={class:"avatar-container"},hi={key:0,class:"avatar-upload"},pi={class:"profile-info"},fi={class:"form-row"},vi={class:"form-group"},mi=["disabled"],gi={class:"form-group"},yi=["disabled"],bi={class:"form-group"},wi=["disabled"],_i={class:"form-row"},ki={class:"form-group"},Ci=["disabled"],Si={class:"form-group"},Ei=["disabled"],Ri={class:"form-group"},Ti=["disabled"],Ai={class:"form-row"},Oi={class:"form-group"},Ii=["disabled"],Mi={class:"form-group"},Li=["disabled"],Pi={class:"form-group"},Ni=["disabled"],Ui={key:0,class:"form-actions"},xi=["disabled"],Di={key:0,class:"fas fa-spinner fa-spin"},Bi={key:1,class:"fas fa-save"},Vi={class:"account-card"},qi={class:"card-body"},Hi={class:"security-item"},Gi={class:"security-status"},ji={class:"account-card"},Fi={class:"card-body"},$i={class:"notification-item"},Wi={class:"notification-item"},zi={class:"notification-item"},Ji={class:"notification-item"},Ki={class:"modal-header"},Yi={class:"modal-body"},Qi={class:"form-group"},Xi={class:"form-group"},Zi={class:"form-group"},er={class:"modal-actions"},tr=["disabled"],sr={key:0,class:"fas fa-spinner fa-spin"},nr={key:1,class:"fas fa-save"},ar=r({__name:"AccountView",setup(e){const t=a(!0),s=a(!1),n=a(!1),i=a(!1),r=a(""),o=a(""),l=a({current_password:"",new_password:"",confirm_password:""}),u=U({firstName:"",lastName:"",email:"",phone:"",company:"",address:"",city:"",zipCode:"",country:"",createdAt:""}),f=a({...u}),y=U({email:!0,billing:!0,services:!0,marketing:!1}),b=gn,w=e=>{const t={FR:"France",BE:"Belgique",CH:"Suisse",CA:"Canada",US:"États-Unis",LU:"Luxembourg",MC:"Monaco"};return t[e]?t[e]:e},_=async()=>{var e,t;if(s.value){n.value=!0,o.value="";try{const e={firstname:u.firstName,lastname:u.lastName,email:u.email,phone:u.phone,company:u.company,address:u.address,city:u.city,postal_code:u.zipCode,country:u.country};await $.routes.client.profile.update(e),f.value={...u},s.value=!1,r.value="Profil mis à jour avec succès !",setTimeout(()=>{r.value=""},3e3)}catch(a){o.value=(null==(t=null==(e=a.response)?void 0:e.data)?void 0:t.message)||"Erreur lors de la sauvegarde. Veuillez réessayer.",setTimeout(()=>{o.value=""},5e3)}finally{n.value=!1}}},C=()=>{Object.assign(u,f.value),s.value=!1,o.value=""},S=async()=>{var e,t;if(l.value.new_password===l.value.confirm_password)if(l.value.new_password.length<8)o.value="Le mot de passe doit contenir au moins 8 caractères";else{n.value=!0,r.value="",o.value="";try{await $.routes.client.profile.changePassword({current_password:l.value.current_password,new_password:l.value.new_password}),r.value="Mot de passe changé avec succès",i.value=!1,l.value={current_password:"",new_password:"",confirm_password:""},setTimeout(()=>{r.value=""},3e3)}catch(s){o.value=(null==(t=null==(e=s.response)?void 0:e.data)?void 0:t.message)||"Erreur lors du changement de mot de passe"}finally{n.value=!1}}else o.value="Les mots de passe ne correspondent pas"},E=e=>{y[e]=!y[e],r.value="Préférences de notification mises à jour",setTimeout(()=>{r.value=""},2e3)},I=()=>{const e=JSON.stringify(u,null,2),t=new Blob([e],{type:"application/json"}),s=URL.createObjectURL(t),n=document.createElement("a");n.href=s,n.download="mon-profil.json",n.click(),URL.revokeObjectURL(s)};return c(()=>{(async()=>{var e,s;try{t.value=!0,o.value="";const e=(await $.routes.client.profile.get()).data;u.firstName=e.firstname,u.lastName=e.lastname,u.email=e.email,u.phone=e.phone||"",u.company=e.company||"",u.address=e.address||"",u.city=e.city||"",u.zipCode=e.postal_code||"",u.country=w(e.country||""),u.createdAt=e.created_at,f.value={...u}}catch(n){o.value=(null==(s=null==(e=n.response)?void 0:e.data)?void 0:s.message)||"Erreur lors du chargement du profil"}finally{t.value=!1}})()}),(e,t)=>(h(),d("div",Za,[p("div",ei,[p("h1",null,[t[22]||(t[22]=p("i",{class:"fas fa-user-cog"},null,-1)),L(" "+g(e.$t("account.title")),1)]),p("div",ti,[p("button",si,[t[23]||(t[23]=p("i",{class:"fas fa-download"},null,-1)),L(" "+g(e.$t("common.export"))+" mes données ",1)]),p("button",{class:"btn btn-primary btn-sm",onClick:_},[t[24]||(t[24]=p("i",{class:"fas fa-save"},null,-1)),L(" "+g(e.$t("common.save")),1)])])]),r.value?(h(),d("div",ni,[t[25]||(t[25]=p("i",{class:"fas fa-check-circle"},null,-1)),L(" "+g(r.value),1)])):v("",!0),o.value?(h(),d("div",ai,[t[26]||(t[26]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),L(" "+g(o.value),1)])):v("",!0),p("div",ii,[p("div",ri,[p("div",oi,[p("h3",li,[t[27]||(t[27]=p("i",{class:"fas fa-user"},null,-1)),L(" "+g(e.$t("account.personal_info")),1)]),p("button",{class:"btn btn-outline btn-sm",onClick:t[0]||(t[0]=e=>s.value=!s.value)},[p("i",{class:m(s.value?"fas fa-times":"fas fa-edit")},null,2),L(" "+g(s.value?e.$t("common.cancel"):e.$t("common.edit")),1)])]),p("div",ci,[p("div",ui,[p("div",di,[t[29]||(t[29]=p("div",{class:"avatar-image"},[p("i",{class:"fas fa-user"})],-1)),s.value?(h(),d("div",hi,t[28]||(t[28]=[p("i",{class:"fas fa-camera"},null,-1)]))):v("",!0)]),p("div",pi,[p("h3",null,g(u.firstName)+" "+g(u.lastName),1),p("p",null,g(e.$t("account.member_since"))+" "+g(A(b)(u.createdAt)),1)])]),p("form",{onSubmit:k(_,["prevent"])},[p("div",fi,[p("div",vi,[t[30]||(t[30]=p("label",{class:"form-label"},"Prénom",-1)),R(p("input",{"onUpdate:modelValue":t[1]||(t[1]=e=>u.firstName=e),type:"text",class:"form-input",disabled:!s.value,required:""},null,8,mi),[[T,u.firstName]])]),p("div",gi,[t[31]||(t[31]=p("label",{class:"form-label"},"Nom",-1)),R(p("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>u.lastName=e),type:"text",class:"form-input",disabled:!s.value,required:""},null,8,yi),[[T,u.lastName]])])]),p("div",bi,[t[32]||(t[32]=p("label",{class:"form-label"},"Email",-1)),R(p("input",{"onUpdate:modelValue":t[3]||(t[3]=e=>u.email=e),type:"email",class:"form-input",disabled:!s.value,required:""},null,8,wi),[[T,u.email]])]),p("div",_i,[p("div",ki,[t[33]||(t[33]=p("label",{class:"form-label"},"Téléphone",-1)),R(p("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>u.phone=e),type:"tel",class:"form-input",disabled:!s.value},null,8,Ci),[[T,u.phone]])]),p("div",Si,[t[34]||(t[34]=p("label",{class:"form-label"},"Société",-1)),R(p("input",{"onUpdate:modelValue":t[5]||(t[5]=e=>u.company=e),type:"text",class:"form-input",disabled:!s.value},null,8,Ei),[[T,u.company]])])]),p("div",Ri,[t[35]||(t[35]=p("label",{class:"form-label"},"Adresse",-1)),R(p("input",{"onUpdate:modelValue":t[6]||(t[6]=e=>u.address=e),type:"text",class:"form-input",disabled:!s.value},null,8,Ti),[[T,u.address]])]),p("div",Ai,[p("div",Oi,[t[36]||(t[36]=p("label",{class:"form-label"},"Ville",-1)),R(p("input",{"onUpdate:modelValue":t[7]||(t[7]=e=>u.city=e),type:"text",class:"form-input",disabled:!s.value},null,8,Ii),[[T,u.city]])]),p("div",Mi,[t[37]||(t[37]=p("label",{class:"form-label"},"Code Postal",-1)),R(p("input",{"onUpdate:modelValue":t[8]||(t[8]=e=>u.zipCode=e),type:"text",class:"form-input",disabled:!s.value},null,8,Li),[[T,u.zipCode]])])]),p("div",Pi,[t[39]||(t[39]=p("label",{class:"form-label"},"Pays",-1)),R(p("select",{"onUpdate:modelValue":t[9]||(t[9]=e=>u.country=e),class:"form-select",disabled:!s.value},t[38]||(t[38]=[O('<option value="" data-v-bf9f5224>Sélectionnez un pays</option><option value="France" data-v-bf9f5224>France</option><option value="Belgique" data-v-bf9f5224>Belgique</option><option value="Suisse" data-v-bf9f5224>Suisse</option><option value="Canada" data-v-bf9f5224>Canada</option><option value="États-Unis" data-v-bf9f5224>États-Unis</option><option value="Luxembourg" data-v-bf9f5224>Luxembourg</option><option value="Monaco" data-v-bf9f5224>Monaco</option>',8)]),8,Ni),[[N,u.country]])]),s.value?(h(),d("div",Ui,[p("button",{type:"button",class:"btn btn-outline",onClick:C}," Annuler "),p("button",{type:"submit",class:"btn btn-primary",disabled:n.value},[n.value?(h(),d("i",Di)):(h(),d("i",Bi)),L(" "+g(n.value?"Sauvegarde...":"Sauvegarder"),1)],8,xi)])):v("",!0)],32)])]),p("div",Vi,[t[43]||(t[43]=p("div",{class:"card-header"},[p("h3",{class:"card-title"},[p("i",{class:"fas fa-shield-alt"}),L(" Sécurité ")])],-1)),p("div",qi,[p("div",Hi,[t[41]||(t[41]=p("div",{class:"security-info"},[p("div",{class:"security-icon"},[p("i",{class:"fas fa-key"})]),p("div",{class:"security-details"},[p("h4",null,"Mot de passe"),p("p",null,"Dernière modification il y a 3 mois")])],-1)),p("div",Gi,[t[40]||(t[40]=p("div",{class:"status-indicator status-warning"},null,-1)),p("button",{class:"btn btn-outline btn-sm",onClick:t[10]||(t[10]=e=>i.value=!0)}," Modifier ")])]),t[42]||(t[42]=O('<div class="security-item" data-v-bf9f5224><div class="security-info" data-v-bf9f5224><div class="security-icon" data-v-bf9f5224><i class="fas fa-mobile-alt" data-v-bf9f5224></i></div><div class="security-details" data-v-bf9f5224><h4 data-v-bf9f5224>Authentification à deux facteurs</h4><p data-v-bf9f5224>Protection supplémentaire de votre compte</p></div></div><div class="security-status" data-v-bf9f5224><div class="status-indicator status-inactive" data-v-bf9f5224></div><button class="btn btn-primary btn-sm" data-v-bf9f5224> Activer </button></div></div><div class="security-item" data-v-bf9f5224><div class="security-info" data-v-bf9f5224><div class="security-icon" data-v-bf9f5224><i class="fas fa-history" data-v-bf9f5224></i></div><div class="security-details" data-v-bf9f5224><h4 data-v-bf9f5224>Historique de connexion</h4><p data-v-bf9f5224>Dernière connexion: Aujourd&#39;hui à 14:30</p></div></div><div class="security-status" data-v-bf9f5224><div class="status-indicator status-active" data-v-bf9f5224></div><button class="btn btn-outline btn-sm" data-v-bf9f5224> Voir l&#39;historique </button></div></div>',2))])])]),p("div",ji,[t[48]||(t[48]=p("div",{class:"card-header"},[p("h3",{class:"card-title"},[p("i",{class:"fas fa-bell"}),L(" Préférences de Notification ")])],-1)),p("div",Fi,[p("div",$i,[t[44]||(t[44]=p("div",{class:"notification-info"},[p("div",{class:"notification-title"},"Notifications par email"),p("div",{class:"notification-description"},"Recevoir les notifications importantes par email")],-1)),p("div",{class:m(["toggle-switch",{active:y.email}]),onClick:t[11]||(t[11]=e=>E("email"))},null,2)]),p("div",Wi,[t[45]||(t[45]=p("div",{class:"notification-info"},[p("div",{class:"notification-title"},"Alertes de facturation"),p("div",{class:"notification-description"},"Être notifié des nouvelles factures et échéances")],-1)),p("div",{class:m(["toggle-switch",{active:y.billing}]),onClick:t[12]||(t[12]=e=>E("billing"))},null,2)]),p("div",zi,[t[46]||(t[46]=p("div",{class:"notification-info"},[p("div",{class:"notification-title"},"Mises à jour de services"),p("div",{class:"notification-description"},"Recevoir les informations sur vos services")],-1)),p("div",{class:m(["toggle-switch",{active:y.services}]),onClick:t[13]||(t[13]=e=>E("services"))},null,2)]),p("div",Ji,[t[47]||(t[47]=p("div",{class:"notification-info"},[p("div",{class:"notification-title"},"Newsletter marketing"),p("div",{class:"notification-description"},"Recevoir nos offres et actualités")],-1)),p("div",{class:m(["toggle-switch",{active:y.marketing}]),onClick:t[14]||(t[14]=e=>E("marketing"))},null,2)])])]),p("div",{class:"account-card"},[t[52]||(t[52]=p("div",{class:"card-header"},[p("h3",{class:"card-title"},[p("i",{class:"fas fa-exclamation-triangle"}),L(" Zone de Danger ")])],-1)),p("div",{class:"card-body"},[t[51]||(t[51]=p("p",{style:{color:"var(--text-muted)","margin-bottom":"1rem"}}," Ces actions sont irréversibles. Procédez avec prudence. ",-1)),p("div",{style:{display:"flex",gap:"1rem","flex-wrap":"wrap"}},[p("button",{class:"btn btn-outline btn-sm",onClick:I},t[49]||(t[49]=[p("i",{class:"fas fa-download"},null,-1),L(" Exporter toutes mes données ")])),t[50]||(t[50]=p("button",{class:"btn btn-danger btn-sm"},[p("i",{class:"fas fa-user-times"}),L(" Supprimer mon compte ")],-1))])])]),i.value?(h(),d("div",{key:2,class:"modal show",onClick:t[21]||(t[21]=e=>i.value=!1)},[t[59]||(t[59]=p("div",{class:"modal-backdrop"},null,-1)),p("div",{class:"modal-content",onClick:t[20]||(t[20]=k(()=>{},["stop"]))},[p("div",Ki,[t[54]||(t[54]=p("h3",null,[p("i",{class:"fas fa-key"}),L(" Changer le mot de passe ")],-1)),p("button",{class:"modal-close",onClick:t[15]||(t[15]=e=>i.value=!1)},t[53]||(t[53]=[p("i",{class:"fas fa-times"},null,-1)]))]),p("div",Yi,[p("form",{onSubmit:k(S,["prevent"])},[p("div",Qi,[t[55]||(t[55]=p("label",{for:"current_password"},"Mot de passe actuel",-1)),R(p("input",{id:"current_password","onUpdate:modelValue":t[16]||(t[16]=e=>l.value.current_password=e),type:"password",class:"form-input",required:"",autocomplete:"current-password"},null,512),[[T,l.value.current_password]])]),p("div",Xi,[t[56]||(t[56]=p("label",{for:"new_password"},"Nouveau mot de passe",-1)),R(p("input",{id:"new_password","onUpdate:modelValue":t[17]||(t[17]=e=>l.value.new_password=e),type:"password",class:"form-input",required:"",minlength:"8",autocomplete:"new-password"},null,512),[[T,l.value.new_password]]),t[57]||(t[57]=p("small",{class:"form-help"},"Minimum 8 caractères",-1))]),p("div",Zi,[t[58]||(t[58]=p("label",{for:"confirm_password"},"Confirmer le nouveau mot de passe",-1)),R(p("input",{id:"confirm_password","onUpdate:modelValue":t[18]||(t[18]=e=>l.value.confirm_password=e),type:"password",class:"form-input",required:"",autocomplete:"new-password"},null,512),[[T,l.value.confirm_password]])]),p("div",er,[p("button",{type:"button",class:"btn btn-outline",onClick:t[19]||(t[19]=e=>i.value=!1)}," Annuler "),p("button",{type:"submit",class:"btn btn-primary",disabled:n.value},[n.value?(h(),d("i",sr)):(h(),d("i",nr)),L(" "+g(n.value?"Sauvegarde...":"Changer le mot de passe"),1)],8,tr)])],32)])])])):v("",!0)]))}}),ir=He(ar,[["__scopeId","data-v-bf9f5224"]]),rr={class:"auth-container"},or={class:"auth-card"},lr={key:0,class:"form-error"},cr={key:0,class:"form-error"},ur={class:"auth-options"},dr={class:"remember-me"},hr={key:0,class:"form-error"},pr=["disabled"],fr={key:0,class:"loading-spinner"},vr={class:"auth-links"},mr=r({__name:"LoginView",setup(e){const t=o(),s=oe(),n=a(!1),r=a({email:"",password:"",remember:!1}),l=a({email:"",password:"",general:""}),u=i(()=>r.value.email&&r.value.password&&!l.value.email&&!l.value.password),y=()=>{r.value.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r.value.email)?l.value.email="":l.value.email="Format d'email invalide":l.value.email="L'adresse email est requise"},w=()=>{r.value.password?r.value.password.length<6?l.value.password="Le mot de passe doit contenir au moins 6 caractères":l.value.password="":l.value.password="Le mot de passe est requis"},_=e=>{l.value[e]&&(l.value[e]=""),l.value.general&&(l.value.general="")},C=async()=>{var e,a,i;if(y(),w(),u.value){n.value=!0,l.value.general="";try{await s.login(r.value.email,r.value.password,r.value.remember);const e=t.currentRoute.value.query.redirect||"/dashboard";await t.push(e)}catch(o){J.error("[AUTH] Erreur de connexion",{error:o,email:r.value.email}),401===(null==(e=o.response)?void 0:e.status)?l.value.general="Email ou mot de passe incorrect":403===(null==(a=o.response)?void 0:a.status)?l.value.general="Compte suspendu ou inactif":(null==(i=o.response)?void 0:i.status)>=500?l.value.general="Erreur serveur. Veuillez réessayer plus tard.":l.value.general=o.message||"Une erreur est survenue lors de la connexion"}finally{n.value=!1}}};return c(async()=>{s.isAuthenticated&&await t.push("/dashboard")}),(e,t)=>{const s=E("router-link");return h(),d("div",rr,[p("div",or,[t[14]||(t[14]=O('<div class="auth-header"><div class="auth-logo"><i class="fas fa-user-circle"></i></div><h1 class="auth-title">Connexion Client</h1><p class="auth-subtitle">Accédez à votre espace client TechCMS</p></div>',1)),p("form",{onSubmit:k(C,["prevent"]),class:"auth-form"},[p("div",{class:m(["form-group",{error:l.value.email,valid:!l.value.email&&r.value.email}])},[t[6]||(t[6]=p("label",{for:"email",class:"form-label"},[p("i",{class:"fas fa-envelope"}),L(" Adresse email ")],-1)),R(p("input",{id:"email","onUpdate:modelValue":t[0]||(t[0]=e=>r.value.email=e),type:"email",class:m(["form-input",{error:l.value.email}]),placeholder:"<EMAIL>",required:"",autocomplete:"email",onBlur:y,onInput:t[1]||(t[1]=e=>_("email"))},null,34),[[T,r.value.email]]),l.value.email?(h(),d("div",lr,[t[5]||(t[5]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(l.value.email),1)])):v("",!0)],2),p("div",{class:m(["form-group",{error:l.value.password,valid:!l.value.password&&r.value.password}])},[t[8]||(t[8]=p("label",{for:"password",class:"form-label"},[p("i",{class:"fas fa-lock"}),L(" Mot de passe ")],-1)),R(p("input",{id:"password","onUpdate:modelValue":t[2]||(t[2]=e=>r.value.password=e),type:"password",class:m(["form-input",{error:l.value.password}]),placeholder:"Votre mot de passe",required:"",autocomplete:"current-password",onBlur:w,onInput:t[3]||(t[3]=e=>_("password"))},null,34),[[T,r.value.password]]),l.value.password?(h(),d("div",cr,[t[7]||(t[7]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(l.value.password),1)])):v("",!0)],2),p("div",ur,[p("label",dr,[R(p("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>r.value.remember=e),type:"checkbox"},null,512),[[x,r.value.remember]]),t[9]||(t[9]=L(" Se souvenir de moi "))]),f(s,{to:"/forgot-password",class:"forgot-password"},{default:b(()=>t[10]||(t[10]=[L(" Mot de passe oublié ? ")])),_:1,__:[10]})]),l.value.general?(h(),d("div",hr,[t[11]||(t[11]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),L(" "+g(l.value.general),1)])):v("",!0),p("button",{type:"submit",class:m(["auth-button",{loading:n.value}]),disabled:n.value||!u.value},[n.value?(h(),d("span",fr)):v("",!0),L(" "+g(n.value?"Connexion...":"Se connecter"),1)],10,pr)],32),p("div",vr,[p("p",null,[t[13]||(t[13]=L(" Pas encore de compte ? ")),f(s,{to:"/register",class:"auth-link"},{default:b(()=>t[12]||(t[12]=[L(" Créer un compte ")])),_:1,__:[12]})])])])])}}}),gr={class:"auth-container"},yr={class:"auth-card"},br={class:"form-row"},wr={key:0,class:"form-error"},_r={key:0,class:"form-error"},kr={key:0,class:"form-error"},Cr={class:"form-row"},Sr={key:0,class:"form-error"},Er={key:0,class:"form-error"},Rr={class:"form-row"},Tr={key:0,class:"form-error"},Ar={key:0,class:"form-error"},Or={key:0,class:"form-error"},Ir={class:"form-row"},Mr={key:0,class:"form-error"},Lr={key:0,class:"form-error"},Pr={class:"remember-me"},Nr={key:0,class:"form-error"},Ur={key:0,class:"form-error"},xr={key:1,class:"form-success"},Dr=["disabled"],Br={key:0,class:"loading-spinner"},Vr={class:"auth-links"},qr=r({__name:"RegisterView",setup(e){const t=o(),s=oe(),n=a(!1),r=a(""),l=a({firstname:"",lastname:"",email:"",company:"",phone:"",address:"",postal_code:"",city:"",country:"",password:"",passwordConfirmation:"",acceptTerms:!1}),c=a({firstname:"",lastname:"",email:"",company:"",phone:"",address:"",postal_code:"",city:"",country:"",password:"",passwordConfirmation:"",terms:"",general:""}),u=i(()=>l.value.firstname&&l.value.lastname&&l.value.email&&l.value.phone&&l.value.address&&l.value.postal_code&&l.value.city&&l.value.country&&l.value.password&&l.value.passwordConfirmation&&l.value.acceptTerms&&!Object.values(c.value).some(e=>""!==e)),y=()=>{l.value.firstname.trim()?l.value.firstname.trim().length<2?c.value.firstname="Le prénom doit contenir au moins 2 caractères":c.value.firstname="":c.value.firstname="Le prénom est requis"},w=()=>{l.value.lastname.trim()?l.value.lastname.trim().length<2?c.value.lastname="Le nom doit contenir au moins 2 caractères":c.value.lastname="":c.value.lastname="Le nom est requis"},_=()=>{l.value.email?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(l.value.email)?c.value.email="":c.value.email="Format d'email invalide":c.value.email="L'adresse email est requise"},C=()=>{if(l.value.phone.trim()){/^[\+]?[0-9\s\-\(\)]{10,}$/.test(l.value.phone)?c.value.phone="":c.value.phone="Format de téléphone invalide"}else c.value.phone="Le numéro de téléphone est requis"},S=()=>{l.value.address.trim()?l.value.address.trim().length<5?c.value.address="L'adresse doit contenir au moins 5 caractères":c.value.address="":c.value.address="L'adresse est requise"},A=()=>{l.value.postal_code.trim()?/^[0-9]{5}$/.test(l.value.postal_code.trim())?c.value.postal_code="":c.value.postal_code="Le code postal doit contenir 5 chiffres":c.value.postal_code="Le code postal est requis"},I=()=>{l.value.city.trim()?l.value.city.trim().length<2?c.value.city="La ville doit contenir au moins 2 caractères":c.value.city="":c.value.city="La ville est requise"},M=()=>{l.value.country?c.value.country="":c.value.country="Le pays est requis"},P=()=>{l.value.password?l.value.password.length<8?c.value.password="Le mot de passe doit contenir au moins 8 caractères":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(l.value.password)?c.value.password="":c.value.password="Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre":c.value.password="Le mot de passe est requis",l.value.passwordConfirmation&&U()},U=()=>{l.value.passwordConfirmation?l.value.password!==l.value.passwordConfirmation?c.value.passwordConfirmation="Les mots de passe ne correspondent pas":c.value.passwordConfirmation="":c.value.passwordConfirmation="La confirmation du mot de passe est requise"},D=e=>{c.value[e]&&(c.value[e]=""),c.value.general&&(c.value.general="")},B=async()=>{var e,a;if(y(),w(),_(),C(),S(),A(),I(),M(),P(),U(),l.value.acceptTerms?c.value.terms="":c.value.terms="Vous devez accepter les conditions d'utilisation",u.value){n.value=!0,c.value.general="",r.value="";try{J.info("[AUTH] Début inscription",{email:l.value.email,firstName:l.value.firstName}),await s.register(l.value),J.info("[AUTH] Inscription réussie",{email:l.value.email}),r.value="Compte créé avec succès ! Redirection vers la connexion...",setTimeout(()=>{t.push("/client/login")},2e3)}catch(i){J.error("[AUTH] Erreur inscription",{error:i,email:l.value.email}),409===(null==(e=i.response)?void 0:e.status)?c.value.email="Cette adresse email est déjà utilisée":(null==(a=i.response)?void 0:a.status)>=500?c.value.general="Erreur serveur. Veuillez réessayer plus tard.":c.value.general=i.message||"Une erreur est survenue lors de l'inscription"}finally{n.value=!1}}};return(e,t)=>{const s=E("router-link");return h(),d("div",gr,[p("div",yr,[t[53]||(t[53]=O('<div class="auth-header"><div class="auth-logo"><i class="fas fa-user-plus"></i></div><h1 class="auth-title">Créer un compte</h1><p class="auth-subtitle">Rejoignez TechCMS et accédez à nos services</p></div>',1)),p("form",{onSubmit:k(B,["prevent"]),class:"auth-form"},[p("div",br,[p("div",{class:m(["form-group",{error:c.value.firstname,valid:!c.value.firstname&&l.value.firstname}])},[t[23]||(t[23]=p("label",{for:"firstname",class:"form-label"},[p("i",{class:"fas fa-user"}),L(" Prénom ")],-1)),R(p("input",{id:"firstname","onUpdate:modelValue":t[0]||(t[0]=e=>l.value.firstname=e),type:"text",class:m(["form-input",{error:c.value.firstname}]),placeholder:"Votre prénom",required:"",autocomplete:"given-name",onBlur:y,onInput:t[1]||(t[1]=e=>D("firstname"))},null,34),[[T,l.value.firstname]]),c.value.firstname?(h(),d("div",wr,[t[22]||(t[22]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.firstname),1)])):v("",!0)],2),p("div",{class:m(["form-group",{error:c.value.lastname,valid:!c.value.lastname&&l.value.lastname}])},[t[25]||(t[25]=p("label",{for:"lastname",class:"form-label"},[p("i",{class:"fas fa-user"}),L(" Nom ")],-1)),R(p("input",{id:"lastname","onUpdate:modelValue":t[2]||(t[2]=e=>l.value.lastname=e),type:"text",class:m(["form-input",{error:c.value.lastname}]),placeholder:"Votre nom",required:"",autocomplete:"family-name",onBlur:w,onInput:t[3]||(t[3]=e=>D("lastname"))},null,34),[[T,l.value.lastname]]),c.value.lastname?(h(),d("div",_r,[t[24]||(t[24]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.lastname),1)])):v("",!0)],2)]),p("div",{class:m(["form-group",{error:c.value.email,valid:!c.value.email&&l.value.email}])},[t[27]||(t[27]=p("label",{for:"email",class:"form-label"},[p("i",{class:"fas fa-envelope"}),L(" Adresse email ")],-1)),R(p("input",{id:"email","onUpdate:modelValue":t[4]||(t[4]=e=>l.value.email=e),type:"email",class:m(["form-input",{error:c.value.email}]),placeholder:"<EMAIL>",required:"",autocomplete:"email",onBlur:_,onInput:t[5]||(t[5]=e=>D("email"))},null,34),[[T,l.value.email]]),c.value.email?(h(),d("div",kr,[t[26]||(t[26]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.email),1)])):v("",!0)],2),p("div",Cr,[p("div",{class:m(["form-group",{error:c.value.phone,valid:!c.value.phone&&l.value.phone}])},[t[29]||(t[29]=p("label",{for:"phone",class:"form-label"},[p("i",{class:"fas fa-phone"}),L(" Téléphone ")],-1)),R(p("input",{id:"phone","onUpdate:modelValue":t[6]||(t[6]=e=>l.value.phone=e),type:"tel",class:m(["form-input",{error:c.value.phone}]),placeholder:"+33 1 23 45 67 89",required:"",autocomplete:"tel",onBlur:C,onInput:t[7]||(t[7]=e=>D("phone"))},null,34),[[T,l.value.phone]]),c.value.phone?(h(),d("div",Sr,[t[28]||(t[28]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.phone),1)])):v("",!0)],2),p("div",{class:m(["form-group",{error:c.value.company}])},[t[30]||(t[30]=p("label",{for:"company",class:"form-label"},[p("i",{class:"fas fa-building"}),L(" Entreprise (optionnel) ")],-1)),R(p("input",{id:"company","onUpdate:modelValue":t[8]||(t[8]=e=>l.value.company=e),type:"text",class:"form-input",placeholder:"Nom de votre entreprise",autocomplete:"organization"},null,512),[[T,l.value.company]])],2)]),p("div",{class:m(["form-group",{error:c.value.address,valid:!c.value.address&&l.value.address}])},[t[32]||(t[32]=p("label",{for:"address",class:"form-label"},[p("i",{class:"fas fa-map-marker-alt"}),L(" Adresse ")],-1)),R(p("input",{id:"address","onUpdate:modelValue":t[9]||(t[9]=e=>l.value.address=e),type:"text",class:m(["form-input",{error:c.value.address}]),placeholder:"123 Rue de la Paix",required:"",autocomplete:"street-address",onBlur:S,onInput:t[10]||(t[10]=e=>D("address"))},null,34),[[T,l.value.address]]),c.value.address?(h(),d("div",Er,[t[31]||(t[31]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.address),1)])):v("",!0)],2),p("div",Rr,[p("div",{class:m(["form-group",{error:c.value.postal_code,valid:!c.value.postal_code&&l.value.postal_code}])},[t[34]||(t[34]=p("label",{for:"postal_code",class:"form-label"},[p("i",{class:"fas fa-mail-bulk"}),L(" Code postal ")],-1)),R(p("input",{id:"postal_code","onUpdate:modelValue":t[11]||(t[11]=e=>l.value.postal_code=e),type:"text",class:m(["form-input",{error:c.value.postal_code}]),placeholder:"75001",required:"",autocomplete:"postal-code",onBlur:A,onInput:t[12]||(t[12]=e=>D("postal_code"))},null,34),[[T,l.value.postal_code]]),c.value.postal_code?(h(),d("div",Tr,[t[33]||(t[33]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.postal_code),1)])):v("",!0)],2),p("div",{class:m(["form-group",{error:c.value.city,valid:!c.value.city&&l.value.city}])},[t[36]||(t[36]=p("label",{for:"city",class:"form-label"},[p("i",{class:"fas fa-city"}),L(" Ville ")],-1)),R(p("input",{id:"city","onUpdate:modelValue":t[13]||(t[13]=e=>l.value.city=e),type:"text",class:m(["form-input",{error:c.value.city}]),placeholder:"Paris",required:"",autocomplete:"address-level2",onBlur:I,onInput:t[14]||(t[14]=e=>D("city"))},null,34),[[T,l.value.city]]),c.value.city?(h(),d("div",Ar,[t[35]||(t[35]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.city),1)])):v("",!0)],2)]),p("div",{class:m(["form-group",{error:c.value.country,valid:!c.value.country&&l.value.country}])},[t[39]||(t[39]=p("label",{for:"country",class:"form-label"},[p("i",{class:"fas fa-globe"}),L(" Pays ")],-1)),R(p("select",{id:"country","onUpdate:modelValue":t[15]||(t[15]=e=>l.value.country=e),class:m(["form-input",{error:c.value.country}]),required:"",autocomplete:"country",onBlur:M,onChange:t[16]||(t[16]=e=>D("country"))},t[37]||(t[37]=[O('<option value="">Sélectionnez un pays</option><option value="FR">France</option><option value="BE">Belgique</option><option value="CH">Suisse</option><option value="CA">Canada</option><option value="US">États-Unis</option><option value="GB">Royaume-Uni</option><option value="DE">Allemagne</option><option value="ES">Espagne</option><option value="IT">Italie</option><option value="NL">Pays-Bas</option>',11)]),34),[[N,l.value.country]]),c.value.country?(h(),d("div",Or,[t[38]||(t[38]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.country),1)])):v("",!0)],2),p("div",Ir,[p("div",{class:m(["form-group",{error:c.value.password,valid:!c.value.password&&l.value.password}])},[t[41]||(t[41]=p("label",{for:"password",class:"form-label"},[p("i",{class:"fas fa-lock"}),L(" Mot de passe ")],-1)),R(p("input",{id:"password","onUpdate:modelValue":t[17]||(t[17]=e=>l.value.password=e),type:"password",class:m(["form-input",{error:c.value.password}]),placeholder:"Choisissez un mot de passe sécurisé",required:"",autocomplete:"new-password",onBlur:P,onInput:t[18]||(t[18]=e=>D("password"))},null,34),[[T,l.value.password]]),c.value.password?(h(),d("div",Mr,[t[40]||(t[40]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.password),1)])):v("",!0)],2),p("div",{class:m(["form-group",{error:c.value.passwordConfirmation,valid:!c.value.passwordConfirmation&&l.value.passwordConfirmation}])},[t[43]||(t[43]=p("label",{for:"passwordConfirmation",class:"form-label"},[p("i",{class:"fas fa-lock"}),L(" Confirmer le mot de passe ")],-1)),R(p("input",{id:"passwordConfirmation","onUpdate:modelValue":t[19]||(t[19]=e=>l.value.passwordConfirmation=e),type:"password",class:m(["form-input",{error:c.value.passwordConfirmation}]),placeholder:"Confirmez votre mot de passe",required:"",autocomplete:"new-password",onBlur:U,onInput:t[20]||(t[20]=e=>D("passwordConfirmation"))},null,34),[[T,l.value.passwordConfirmation]]),c.value.passwordConfirmation?(h(),d("div",Lr,[t[42]||(t[42]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.passwordConfirmation),1)])):v("",!0)],2)]),p("div",{class:m(["form-group",{error:c.value.terms}])},[p("label",Pr,[R(p("input",{"onUpdate:modelValue":t[21]||(t[21]=e=>l.value.acceptTerms=e),type:"checkbox",required:""},null,512),[[x,l.value.acceptTerms]]),t[44]||(t[44]=L(" J'accepte les ")),t[45]||(t[45]=p("a",{href:"/terms",target:"_blank",class:"auth-link"},"conditions d'utilisation",-1)),t[46]||(t[46]=L(" et la ")),t[47]||(t[47]=p("a",{href:"/privacy",target:"_blank",class:"auth-link"},"politique de confidentialité",-1))]),c.value.terms?(h(),d("div",Nr,[t[48]||(t[48]=p("i",{class:"fas fa-exclamation-circle"},null,-1)),L(" "+g(c.value.terms),1)])):v("",!0)],2),c.value.general?(h(),d("div",Ur,[t[49]||(t[49]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),L(" "+g(c.value.general),1)])):v("",!0),r.value?(h(),d("div",xr,[t[50]||(t[50]=p("i",{class:"fas fa-check-circle"},null,-1)),L(" "+g(r.value),1)])):v("",!0),p("button",{type:"submit",class:m(["auth-button",{loading:n.value}]),disabled:n.value||!u.value},[n.value?(h(),d("span",Br)):v("",!0),L(" "+g(n.value?"Création...":"Créer mon compte"),1)],10,Dr)],32),p("div",Vr,[p("p",null,[t[52]||(t[52]=L(" Déjà un compte ? ")),f(s,{to:"/auth/login",class:"auth-link"},{default:b(()=>t[51]||(t[51]=[L(" Se connecter ")])),_:1,__:[51]})])])])])}}}),Hr=n("licenses",()=>{const e=a([]),t=a(null),s=a([]),n=a(!1),r=a(null),o=a(null),l=a(!1),c=a({total_licenses:0,active_licenses:0,expired_licenses:0,expiring_soon:0}),u=i(()=>t=>e.value.find(e=>e.id===t)||null),d=i(()=>e.value.filter(e=>"active"===e.status)),h=i(()=>e.value.filter(e=>"expired"===e.status)),p=i(()=>{const t=new Date,s=new Date(t.getTime()+2592e6);return e.value.filter(e=>{if(!e.expires_at||"active"!==e.status)return!1;const n=new Date(e.expires_at);return n<=s&&n>t})}),f=i(()=>e.value.length>0),v=()=>{c.value={total_licenses:e.value.length,active_licenses:d.value.length,expired_licenses:h.value.length,expiring_soon:p.value.length}};return{licenses:e,currentLicense:t,licenseUsage:s,loading:n,error:r,lastUpdate:o,isUpdating:l,stats:c,getLicenseById:u,activeLicenses:d,expiredLicenses:h,expiringLicenses:p,hasLicenses:f,fetchLicenses:async()=>{if(!n.value){n.value=!0,r.value=null;try{J.info("[LICENSES STORE] Récupération des licences...");const t=await $.routes.client.license.list();if(!t.data.success)throw new Error(t.data.message||"Erreur lors de la récupération des licences");e.value=t.data.data,v(),o.value=(new Date).toISOString(),J.info("[LICENSES STORE] Licences récupérées avec succès",{count:e.value.length})}catch(t){r.value=t.message||"Erreur lors de la récupération des licences",J.error("[LICENSES STORE] Erreur lors de la récupération des licences",t)}finally{n.value=!1}}},fetchLicenseDetail:async e=>{n.value=!0,r.value=null;try{J.info("[LICENSES STORE] Récupération du détail de la licence",{id:e});const n=await $.routes.client.license.get(e);if(n.data.success)return t.value=n.data.data,s.value=n.data.usage||[],J.info("[LICENSES STORE] Détail de la licence récupéré avec succès",{id:e}),n.data.data;throw new Error(n.data.message||"Erreur lors de la récupération du détail")}catch(a){throw r.value=a.message||"Erreur lors de la récupération du détail",J.error("[LICENSES STORE] Erreur lors de la récupération du détail",a),a}finally{n.value=!1}},verifyLicense:async(e,t)=>{try{J.info("[LICENSES STORE] Vérification de licence",{licenseKey:e,domain:t});const s=await $.routes.client.license.verify(e,t),n={success:s.data.success,message:s.data.message,license:s.data.license,verification_date:(new Date).toISOString()};return J.info("[LICENSES STORE] Vérification terminée",n),n}catch(s){return J.error("[LICENSES STORE] Erreur lors de la vérification",s),{success:!1,message:s.message||"Erreur lors de la vérification",verification_date:(new Date).toISOString()}}},handleRealtimeEvent:s=>{var n;if(!l.value){l.value=!0;try{if(J.debug("[LICENSES STORE] Événement temps réel reçu",s),"license_updated"===s.type&&s.data){const a=s.data,i=e.value.findIndex(e=>e.id===a.id);-1!==i&&(e.value[i]=a,v(),(null==(n=t.value)?void 0:n.id)===a.id&&(t.value=a),J.info("[LICENSES STORE] Licence mise à jour via temps réel",{id:a.id}))}}catch(a){J.error("[LICENSES STORE] Erreur lors du traitement de l'événement temps réel",a)}finally{l.value=!1}}},clearError:()=>{r.value=null},clearCurrentLicense:()=>{t.value=null,s.value=[]},formatExpiryDate:e=>{if(!e)return"Aucune expiration";const t=new Date(e),s=new Date;if(t<s)return"Expirée";const n=t.getTime()-s.getTime(),a=Math.ceil(n/864e5);return a<=30?`Expire dans ${a} jour${a>1?"s":""}`:t.toLocaleDateString("fr-FR")},getStatusColor:e=>{switch(e){case"active":return"success";case"inactive":return"warning";case"expired":return"danger";default:return"secondary"}}}}),Gr={id:"client-licenses"},jr={class:"header-box"},Fr={class:"stats-grid box-grid"},$r={class:"stat-card card-box"},Wr={class:"stat-number"},zr={class:"stat-label"},Jr={class:"stat-card card-box"},Kr={class:"stat-number"},Yr={class:"stat-label"},Qr={class:"stat-card card-box"},Xr={class:"stat-number"},Zr={class:"stat-label"},eo={class:"stat-card card-box"},to={class:"stat-number"},so={class:"stat-label"},no={class:"card card-box"},ao={class:"card-header"},io={class:"card-title"},ro={class:"card-body"},oo={key:0,class:"loading-state"},lo={key:1,class:"error-state"},co={key:2,class:"empty-state"},uo={key:3,class:"licenses-list"},ho=["onClick"],po={class:"license-header"},fo={class:"license-key"},vo={class:"license-status"},mo={class:"license-details"},go={class:"license-info"},yo={class:"info-item"},bo={class:"info-item"},wo={class:"info-item"},_o={class:"license-actions"},ko=["onClick"],Co={key:0,class:"license-usage"},So={class:"usage-bar"},Eo={class:"usage-label"},Ro={class:"progress"},To={class:"usage-text"},Ao=r({__name:"LicensesView",setup(e){const t=o(),{t:s}=S(),n=Hr(),a=ke(),i=async()=>{try{await n.fetchLicenses()}catch(e){J.error("[LicensesView] Erreur lors du chargement des licences",e)}},r=()=>{n.clearError(),i()},l=e=>{t.push(`/licenses/${e}`)},f=e=>{e.type.startsWith("license_")&&n.handleRealtimeEvent(e)};return c(async()=>{J.info("[LicensesView] Composant monté"),await i(),a.subscribe("licenses",f)}),u(()=>{J.info("[LicensesView] Composant démonté"),a.unsubscribe("licenses",f),n.clearError()}),(e,t)=>(h(),d("div",Gr,[p("div",jr,[p("h1",null,g(e.$t("licenses.title")),1)]),p("div",Fr,[p("div",$r,[t[0]||(t[0]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-key"})],-1)),p("div",Wr,g(A(n).stats.total_licenses),1),p("div",zr,g(e.$t("licenses.stats.total")),1)]),p("div",Jr,[t[1]||(t[1]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-check-circle"})],-1)),p("div",Kr,g(A(n).stats.active_licenses),1),p("div",Yr,g(e.$t("licenses.stats.active")),1)]),p("div",Qr,[t[2]||(t[2]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-exclamation-triangle"})],-1)),p("div",Xr,g(A(n).stats.expiring_soon),1),p("div",Zr,g(e.$t("licenses.stats.expiring_soon")),1)]),p("div",eo,[t[3]||(t[3]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-times-circle"})],-1)),p("div",to,g(A(n).stats.expired_licenses),1),p("div",so,g(e.$t("licenses.stats.expired")),1)])]),p("div",no,[p("div",ao,[p("h3",io,[t[4]||(t[4]=p("i",{class:"fas fa-list"},null,-1)),L(" "+g(e.$t("licenses.my_licenses")),1)])]),p("div",ro,[A(n).loading?(h(),d("div",oo,[t[5]||(t[5]=p("div",{class:"spinner"},null,-1)),p("p",null,g(e.$t("common.loading")),1)])):A(n).error?(h(),d("div",lo,[t[6]||(t[6]=p("div",{class:"error-icon"},[p("i",{class:"fas fa-exclamation-triangle"})],-1)),p("p",null,g(A(n).error),1),p("button",{onClick:r,class:"btn btn-primary"},g(e.$t("common.retry")),1)])):A(n).hasLicenses?(h(),d("div",uo,[(h(!0),d(w,null,_(A(n).licenses,s=>(h(),d("div",{key:s.id,class:"license-item",onClick:e=>l(s.id)},[p("div",po,[p("div",fo,[t[8]||(t[8]=p("i",{class:"fas fa-key"},null,-1)),p("code",null,g(s.license_key),1)]),p("div",vo,[p("span",{class:m(["status-badge",`status-${A(n).getStatusColor(s.status)}`])},g(e.$t(`licenses.status.${s.status}`)),3)])]),p("div",mo,[p("div",go,[p("div",yo,[t[9]||(t[9]=p("i",{class:"fas fa-globe"},null,-1)),p("span",null,g(e.$t("licenses.domain_limit"))+": "+g(s.domain_limit),1)]),p("div",bo,[t[10]||(t[10]=p("i",{class:"fas fa-download"},null,-1)),p("span",null,g(e.$t("licenses.installation_limit"))+": "+g(s.installation_limit),1)]),p("div",wo,[t[11]||(t[11]=p("i",{class:"fas fa-calendar"},null,-1)),p("span",null,g(A(n).formatExpiryDate(s.expires_at)),1)])]),p("div",_o,[p("button",{onClick:k(e=>l(s.id),["stop"]),class:"btn btn-sm btn-outline"},g(e.$t("licenses.view_details")),9,ko)])]),void 0!==s.current_installations?(h(),d("div",Co,[p("div",So,[p("div",Eo,g(e.$t("licenses.installations_used")),1),p("div",Ro,[p("div",{class:m(["progress-bar",{"progress-bar-warning":s.current_installations/s.installation_limit>.8,"progress-bar-danger":s.current_installations/s.installation_limit>=1}]),style:D({width:Math.min(100,s.current_installations/s.installation_limit*100)+"%"})},null,6)]),p("div",To,g(s.current_installations)+" / "+g(s.installation_limit),1)])])):v("",!0)],8,ho))),128))])):(h(),d("div",co,[t[7]||(t[7]=p("div",{class:"empty-icon"},[p("i",{class:"fas fa-key"})],-1)),p("h4",null,g(e.$t("licenses.no_licenses.title")),1),p("p",null,g(e.$t("licenses.no_licenses.description")),1)]))])])]))}}),Oo=He(Ao,[["__scopeId","data-v-1d288f12"]]),Io={id:"license-detail"},Mo={class:"header-box"},Lo={class:"header-content"},Po={key:0,class:"loading-state"},No={key:1,class:"error-state"},Uo={key:2,class:"license-detail-content"},xo={class:"card card-box"},Do={class:"card-header"},Bo={class:"card-title"},Vo={class:"card-body"},qo={class:"license-info-grid"},Ho={class:"info-group"},Go={class:"license-key-display"},jo=["title"],Fo={class:"info-group"},$o={class:"info-group"},Wo={class:"info-group"},zo={class:"info-group"},Jo={class:"info-group"},Ko={class:"card card-box"},Yo={class:"card-header"},Qo={class:"card-title"},Xo={class:"card-body"},Zo={class:"restrictions-grid"},el={class:"restriction-group"},tl={key:0,class:"restriction-list"},sl={key:1,class:"no-restrictions"},nl={class:"restriction-group"},al={key:0,class:"restriction-list"},il={key:1,class:"no-restrictions"},rl={key:0,class:"card card-box"},ol={class:"card-header"},ll={class:"card-title"},cl={class:"card-body"},ul={class:"usage-list"},dl={class:"usage-info"},hl={class:"usage-domain"},pl={class:"usage-ip"},fl={class:"usage-count"},vl={class:"usage-date"},ml={class:"card card-box"},gl={class:"card-header"},yl={class:"card-title"},bl={class:"card-body"},wl={class:"actions-grid"},_l=["disabled"],kl={key:0},Cl={key:1},Sl=["disabled"],El=He(r({__name:"LicenseDetailView",setup(e){const t=C(),s=o(),{t:n}=S(),r=Hr(),l=ke(),f=a(!1),y=i(()=>parseInt(t.params.id)),b=i(()=>r.currentLicense),k=i(()=>{var e;return(null==(e=b.value)?void 0:e.allowed_domains)?b.value.allowed_domains.split(",").filter(e=>e.trim()):[]}),E=i(()=>{var e;return(null==(e=b.value)?void 0:e.allowed_ips)?b.value.allowed_ips.split(",").filter(e=>e.trim()):[]}),R=async()=>{try{await r.fetchLicenseDetail(y.value)}catch(e){J.error("[LicenseDetailView] Erreur lors du chargement du détail",e)}},T=()=>{r.clearError(),R()},O=()=>{s.push("/licenses")},I=async()=>{if(b.value)try{await navigator.clipboard.writeText(b.value.license_key)}catch(e){J.error("[LicenseDetailView] Erreur lors de la copie",e)}},M=async()=>{if(b.value){f.value=!0;try{const e=await r.verifyLicense(b.value.license_key,window.location.hostname);J.info("[LicenseDetailView] Résultat de la vérification",e)}catch(e){J.error("[LicenseDetailView] Erreur lors de la vérification",e)}finally{f.value=!1}}},P=()=>{R()},N=e=>new Date(e).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),U=e=>{var t;"license_updated"===e.type&&(null==(t=e.data)?void 0:t.id)===y.value&&r.handleRealtimeEvent(e)};return c(async()=>{J.info("[LicenseDetailView] Composant monté",{licenseId:y.value}),await R(),l.subscribe("license-detail",U)}),u(()=>{J.info("[LicenseDetailView] Composant démonté"),l.unsubscribe("license-detail",U),r.clearError(),r.clearCurrentLicense()}),(e,t)=>(h(),d("div",Io,[p("div",Mo,[p("div",Lo,[p("button",{onClick:O,class:"btn btn-outline btn-sm"},[t[0]||(t[0]=p("i",{class:"fas fa-arrow-left"},null,-1)),L(" "+g(e.$t("common.back")),1)]),p("h1",null,g(e.$t("licenses.license_details")),1)])]),A(r).loading?(h(),d("div",Po,[t[1]||(t[1]=p("div",{class:"spinner"},null,-1)),p("p",null,g(e.$t("common.loading")),1)])):A(r).error?(h(),d("div",No,[t[2]||(t[2]=p("div",{class:"error-icon"},[p("i",{class:"fas fa-exclamation-triangle"})],-1)),p("p",null,g(A(r).error),1),p("button",{onClick:T,class:"btn btn-primary"},g(e.$t("common.retry")),1)])):b.value?(h(),d("div",Uo,[p("div",xo,[p("div",Do,[p("h3",Bo,[t[3]||(t[3]=p("i",{class:"fas fa-key"},null,-1)),L(" "+g(e.$t("licenses.license_information")),1)]),p("span",{class:m(["status-badge",`status-${A(r).getStatusColor(b.value.status)}`])},g(e.$t(`licenses.status.${b.value.status}`)),3)]),p("div",Vo,[p("div",qo,[p("div",Ho,[p("label",null,g(e.$t("licenses.license_key")),1),p("div",Go,[p("code",null,g(b.value.license_key),1),p("button",{onClick:I,class:"btn btn-sm btn-outline",title:e.$t("common.copy")},t[4]||(t[4]=[p("i",{class:"fas fa-copy"},null,-1)]),8,jo)])]),p("div",Fo,[p("label",null,g(e.$t("licenses.status.label")),1),p("span",{class:m(["status-badge",`status-${A(r).getStatusColor(b.value.status)}`])},g(e.$t(`licenses.status.${b.value.status}`)),3)]),p("div",$o,[p("label",null,g(e.$t("licenses.domain_limit")),1),p("span",null,g(b.value.domain_limit),1)]),p("div",Wo,[p("label",null,g(e.$t("licenses.installation_limit")),1),p("span",null,g(b.value.installation_limit),1)]),p("div",zo,[p("label",null,g(e.$t("licenses.expires_at")),1),p("span",null,g(A(r).formatExpiryDate(b.value.expires_at)),1)]),p("div",Jo,[p("label",null,g(e.$t("licenses.created_at")),1),p("span",null,g(N(b.value.created_at)),1)])])])]),p("div",Ko,[p("div",Yo,[p("h3",Qo,[t[5]||(t[5]=p("i",{class:"fas fa-shield-alt"},null,-1)),L(" "+g(e.$t("licenses.restrictions")),1)])]),p("div",Xo,[p("div",Zo,[p("div",el,[p("h4",null,g(e.$t("licenses.allowed_domains")),1),k.value.length>0?(h(),d("div",tl,[(h(!0),d(w,null,_(k.value,e=>(h(),d("div",{key:e,class:"restriction-item"},[t[6]||(t[6]=p("i",{class:"fas fa-globe"},null,-1)),p("span",null,g(e),1)]))),128))])):(h(),d("div",sl,g(e.$t("licenses.no_domain_restrictions")),1))]),p("div",nl,[p("h4",null,g(e.$t("licenses.allowed_ips")),1),E.value.length>0?(h(),d("div",al,[(h(!0),d(w,null,_(E.value,e=>(h(),d("div",{key:e,class:"restriction-item"},[t[7]||(t[7]=p("i",{class:"fas fa-network-wired"},null,-1)),p("span",null,g(e),1)]))),128))])):(h(),d("div",il,g(e.$t("licenses.no_ip_restrictions")),1))])])])]),A(r).licenseUsage.length>0?(h(),d("div",rl,[p("div",ol,[p("h3",ll,[t[8]||(t[8]=p("i",{class:"fas fa-chart-bar"},null,-1)),L(" "+g(e.$t("licenses.current_usage")),1)])]),p("div",cl,[p("div",ul,[(h(!0),d(w,null,_(A(r).licenseUsage,e=>(h(),d("div",{key:`${e.domain}-${e.ip}`,class:"usage-item"},[p("div",dl,[p("div",hl,[t[9]||(t[9]=p("i",{class:"fas fa-globe"},null,-1)),p("span",null,g(e.domain),1)]),p("div",pl,[t[10]||(t[10]=p("i",{class:"fas fa-network-wired"},null,-1)),p("span",null,g(e.ip),1)]),p("div",fl,[t[11]||(t[11]=p("i",{class:"fas fa-download"},null,-1)),p("span",null,g(e.installation_count)+" installation"+g(e.installation_count>1?"s":""),1)])]),p("div",vl,[t[12]||(t[12]=p("i",{class:"fas fa-clock"},null,-1)),p("span",null,g(N(e.last_seen)),1)])]))),128))])])])):v("",!0),p("div",ml,[p("div",gl,[p("h3",yl,[t[13]||(t[13]=p("i",{class:"fas fa-tools"},null,-1)),L(" "+g(e.$t("licenses.actions")),1)])]),p("div",bl,[p("div",wl,[p("button",{onClick:M,class:"btn btn-primary",disabled:f.value},[t[14]||(t[14]=p("i",{class:"fas fa-check-circle"},null,-1)),f.value?(h(),d("span",kl,g(e.$t("licenses.verifying")),1)):(h(),d("span",Cl,g(e.$t("licenses.verify_license")),1))],8,_l),p("button",{onClick:P,class:"btn btn-outline",disabled:A(r).loading},[t[15]||(t[15]=p("i",{class:"fas fa-sync-alt"},null,-1)),L(" "+g(e.$t("licenses.refresh")),1)],8,Sl)])])])])):v("",!0)]))}}),[["__scopeId","data-v-ae104061"]]),Rl=n("updates",()=>{const e=a([]),t=a(null),s=a(null),n=a(null),r=a(null),o=a(!1),l=a(!1),c=a(null),u=a("Stable"),d=a(!1),h=i(()=>{if(!u.value)return[];const t={Stable:"stable",Beta:"beta",Dev:"development","Obsolète":"deprecated"}[u.value]||u.value.toLowerCase();return e.value.filter(e=>e.status===t).sort((e,t)=>new Date(t.release_date).getTime()-new Date(e.release_date).getTime())}),p=i(()=>e.value.filter(e=>"stable"===e.status).sort((e,t)=>new Date(t.release_date).getTime()-new Date(e.release_date).getTime())[0]),f=i(()=>{var e,t;const s=(null==(e=n.value)?void 0:e.update_permissions)||!1;return J.info("[UpdatesStore] hasUpdatePermissions computed",{licenseInfo:n.value,update_permissions:null==(t=n.value)?void 0:t.update_permissions,result:s}),s}),v=i(()=>f.value&&!l.value),m=i(()=>{var e;return(null==(e=s.value)?void 0:e.percentage)||0}),g=async t=>{var s,n;o.value=!0,c.value=null;try{const a=new URLSearchParams;t&&a.append("channel",t);const i=await $.routes.client.updates.getVersions(t?{channel:t}:{});if(!(null==(s=i.data)?void 0:s.versions))throw new Error((null==(n=i.data)?void 0:n.message)||"Erreur lors de la récupération des versions");e.value=i.data.versions,J.info("[UpdatesStore] Versions récupérées",{count:e.value.length,channel:t})}catch(a){c.value=a.message||"Erreur lors de la récupération des versions",J.error("[UpdatesStore] Erreur fetchVersions",{error:a.message})}finally{o.value=!1}},y=async()=>{var e,t,s,a,i,r,o;try{J.info("[UpdatesStore] Début fetchLicenseInfo");const r=await $.routes.client.updates.getLicenseInfo();J.info("[UpdatesStore] Réponse API license-info",{status:r.status,data:r.data}),(null==(e=r.data)?void 0:e.license_info)?(n.value=r.data.license_info,J.info("[UpdatesStore] Informations de licence récupérées",{license_id:null==(t=n.value)?void 0:t.license_id,update_permissions:null==(s=n.value)?void 0:s.update_permissions,channel:null==(a=n.value)?void 0:a.channel})):J.warn("[UpdatesStore] Réponse API license-info invalide",{has_license_info:!!(null==(i=r.data)?void 0:i.license_info),response_data:r.data})}catch(l){J.error("[UpdatesStore] Erreur fetchLicenseInfo",{error:l.message,status:null==(r=l.response)?void 0:r.status,response_data:null==(o=l.response)?void 0:o.data})}},b=async()=>{var e;try{const t=await $.routes.client.updates.getStats();(null==(e=t.data)?void 0:e.success)&&t.data.stats&&(r.value=t.data.stats,J.info("[UpdatesStore] Statistiques récupérées",{stats:t.data.stats}))}catch(t){J.error("[UpdatesStore] Erreur fetchStats",{error:t.message})}},w=(e,t,s)=>new Promise(n=>{const a=new XMLHttpRequest,i=Date.now();a.open("GET",e,!0),a.responseType="blob",a.onprogress=e=>{if(e.lengthComputable){const t=e.loaded,n=e.total,a=Math.round(t/n*100),r=t/((Date.now()-i)/1e3);s({loaded:t,total:n,percentage:a,speed:r,timeRemaining:(n-t)/r})}},a.onload=()=>{if(200===a.status){const e=a.response,s=window.URL.createObjectURL(e),i=document.createElement("a");i.href=s,i.download=t.endsWith(".zip")?t:`${t}.zip`,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(s),n(!0)}else n(!1)},a.onerror=()=>n(!1),a.onabort=()=>n(!1),a.send()}),_=e=>{if(0===e)return"0 B";const t=Math.floor(Math.log(e)/Math.log(1024));return Math.round(e/Math.pow(1024,t)*100)/100+" "+["B","KB","MB","GB"][t]};return{versions:e,currentDownload:t,downloadProgress:s,licenseInfo:n,stats:r,loading:o,downloading:l,error:c,selectedChannel:u,showSecurityOnly:d,availableVersions:h,latestVersion:p,hasUpdatePermissions:f,canDownload:v,downloadProgressPercentage:m,fetchVersions:g,fetchLicenseInfo:y,fetchStats:b,downloadVersion:async t=>{var n,a,i,r,o,u;if(!v.value)return c.value="Téléchargement non autorisé",!1;l.value=!0,s.value=null,c.value=null;try{const i=await $.routes.client.updates.downloadToken({version_id:t});if(!(null==(n=i.data)?void 0:n.download))throw new Error((null==(a=i.data)?void 0:a.message)||"Impossible d'obtenir le token de téléchargement");const{token:r,url:o,filename:l}=i.data.download,c=e.value.find(e=>e.id===t);J.info("[UpdatesStore] Début du téléchargement",{version_id:t,version:null==c?void 0:c.version,filename:l,url:o});if(await w(o,l||`techcms-${(null==c?void 0:c.version)||"update"}.zip`,e=>{s.value=e}))return await b(),J.info("[UpdatesStore] Téléchargement terminé avec succès",{version_id:t,version:null==c?void 0:c.version}),!0;throw new Error("Échec du téléchargement")}catch(d){const e=(null==(r=null==(i=d.response)?void 0:i.data)?void 0:r.message)||d.message;return c.value=e||"Erreur lors du téléchargement",J.error("[UpdatesStore] Erreur downloadVersion",{version_id:t,error:d.message,api_message:e,status:null==(o=d.response)?void 0:o.status,response_data:null==(u=d.response)?void 0:u.data}),!1}finally{l.value=!1,s.value=null}},cancelDownload:()=>{l.value&&(l.value=!1,s.value=null,J.info("[UpdatesStore] Téléchargement annulé"))},checkForUpdates:async()=>{var e,t;o.value=!0,c.value=null;try{const s=await $.routes.client.updates.check();if(null==(e=s.data)?void 0:e.success)return await g(),await y(),J.info("[UpdatesStore] Vérification des mises à jour terminée"),!0;throw new Error((null==(t=s.data)?void 0:t.message)||"Erreur lors de la vérification des mises à jour")}catch(s){return c.value=s.message||"Erreur lors de la vérification des mises à jour",J.error("[UpdatesStore] Erreur checkForUpdates",{error:s.message}),!1}finally{o.value=!1}},setChannel:e=>{u.value=e,g(e)},clearError:()=>{c.value=null},formatFileSize:_,formatSpeed:e=>_(e)+"/s",formatTimeRemaining:e=>e<60?`${Math.round(e)}s`:e<3600?`${Math.round(e/60)}m`:`${Math.round(e/3600)}h`}}),Tl=[{value:"Stable",label:"Stable",description:"Versions stables et testées",color:"success"},{value:"Beta",label:"Beta",description:"Versions en test avec nouvelles fonctionnalités",color:"warning"},{value:"Dev",label:"Développement",description:"Versions de développement (non recommandées en production)",color:"info"},{value:"Obsolète",label:"Obsolète",description:"Anciennes versions non maintenues",color:"danger"}],Al={class:"updates-view"},Ol={class:"header-section"},Il={class:"header-actions"},Ml=["disabled"],Ll={key:0,class:"license-info-card"},Pl={class:"license-header"},Nl={class:"license-details"},Ul={class:"detail-item"},xl={class:"value"},Dl={class:"detail-item"},Bl={class:"value"},Vl={class:"detail-item"},ql={class:"value"},Hl={key:0,class:"detail-item"},Gl={class:"value"},jl={key:1,class:"stats-grid"},Fl={class:"stat-card"},$l={class:"stat-content"},Wl={class:"stat-number"},zl={class:"stat-card"},Jl={class:"stat-content"},Kl={class:"stat-number"},Yl={class:"stat-card"},Ql={class:"stat-content"},Xl={class:"stat-number"},Zl={class:"stat-card"},ec={class:"stat-content"},tc={class:"stat-number"},sc={class:"filters-section"},nc={class:"filters-row"},ac={class:"filter-group"},ic=["value"],rc={class:"filter-group"},oc={class:"checkbox-label"},lc={key:2,class:"alert alert-danger"},cc={key:3,class:"alert alert-warning"},uc={key:4,class:"download-progress-card"},dc={key:0,class:"progress-details"},hc={class:"progress-bar"},pc={class:"progress-info"},fc={class:"versions-section"},vc={key:0,class:"loading-state"},mc={key:1,class:"empty-state"},gc={key:2,class:"versions-grid"},yc={class:"version-header"},bc={class:"version-info"},wc={class:"version-number"},_c={class:"version-date"},kc={class:"version-details"},Cc={class:"detail-row"},Sc={key:0,class:"detail-row"},Ec={class:"detail-row"},Rc={key:0,class:"version-changelog"},Tc=["innerHTML"],Ac={class:"version-actions"},Oc=["onClick","disabled"],Ic=He(r({__name:"UpdatesView",setup(e){const t=Rl(),s=i(()=>t.availableVersions.length>0),n=e=>e&&"N/A"!==e?new Date(e).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"}):"N/A",a=async()=>{await t.checkForUpdates()},r=()=>{t.setChannel(t.selectedChannel)},o=()=>{t.cancelDownload()};return c(async()=>{J.info("[UpdatesView] Vue montée"),await t.fetchLicenseInfo(),await t.fetchStats(),t.licenseInfo&&(t.selectedChannel=t.licenseInfo.channel),await t.fetchVersions(t.selectedChannel)}),(e,i)=>(h(),d("div",Al,[p("div",Ol,[i[4]||(i[4]=p("div",{class:"header-content"},[p("h1",{class:"page-title"},[p("i",{class:"fas fa-download"}),L(" Mises à jour CMS ")]),p("p",{class:"page-description"}," Téléchargez les dernières versions de TechCMS selon votre canal de mise à jour ")],-1)),p("div",Il,[p("button",{onClick:a,disabled:A(t).loading,class:"btn btn-outline"},[p("i",{class:m(["fas fa-sync",{"fa-spin":A(t).loading}])},null,2),i[3]||(i[3]=L(" Vérifier les mises à jour "))],8,Ml)])]),A(t).licenseInfo?(h(),d("div",Ll,[p("div",Pl,[i[5]||(i[5]=p("h3",null,[p("i",{class:"fas fa-key"}),L(" Licence utilisée pour les mises à jour ")],-1)),p("span",{class:m(["permission-badge",A(t).hasUpdatePermissions?"badge-success":"badge-danger"])},[p("i",{class:m(A(t).hasUpdatePermissions?"fas fa-check":"fas fa-times")},null,2),L(" "+g(A(t).hasUpdatePermissions?"Mises à jour autorisées":"Mises à jour non autorisées"),1)],2)]),i[10]||(i[10]=p("div",{class:"auto-selection-info"},[p("i",{class:"fas fa-info-circle"}),p("span",null,"Licence sélectionnée automatiquement selon vos droits de mise à jour")],-1)),p("div",Nl,[p("div",Ul,[i[6]||(i[6]=p("span",{class:"label"},"Licence :",-1)),p("span",xl,g(A(t).licenseInfo.license_key),1)]),p("div",Dl,[i[7]||(i[7]=p("span",{class:"label"},"Domaine :",-1)),p("span",Bl,g(A(t).licenseInfo.domain),1)]),p("div",Vl,[i[8]||(i[8]=p("span",{class:"label"},"Canal :",-1)),p("span",ql,[p("span",{class:m(["channel-badge",`channel-${(A(t).licenseInfo.channel||"stable").toLowerCase()}`])},g(A(t).licenseInfo.channel||"Stable"),3)])]),A(t).licenseInfo.current_version?(h(),d("div",Hl,[i[9]||(i[9]=p("span",{class:"label"},"Version actuelle :",-1)),p("span",Gl,g(A(t).licenseInfo.current_version),1)])):v("",!0)])])):v("",!0),A(t).stats?(h(),d("div",jl,[p("div",Fl,[i[12]||(i[12]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-download"})],-1)),p("div",$l,[p("div",Wl,g(A(t).stats.total_downloads),1),i[11]||(i[11]=p("div",{class:"stat-label"},"Téléchargements",-1))])]),p("div",zl,[i[14]||(i[14]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-code-branch"})],-1)),p("div",Jl,[p("div",Kl,g(A(t).stats.current_version||"N/A"),1),i[13]||(i[13]=p("div",{class:"stat-label"},"Version installée",-1))])]),p("div",Yl,[i[16]||(i[16]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-clock"})],-1)),p("div",Ql,[p("div",Xl,g(n(A(t).stats.last_download)),1),i[15]||(i[15]=p("div",{class:"stat-label"},"Dernier téléchargement",-1))])]),p("div",Zl,[i[18]||(i[18]=p("div",{class:"stat-icon"},[p("i",{class:"fas fa-layer-group"})],-1)),p("div",ec,[p("div",tc,g(A(t).stats.available_versions),1),i[17]||(i[17]=p("div",{class:"stat-label"},"Versions disponibles",-1))])])])):v("",!0),p("div",sc,[p("div",nc,[p("div",ac,[i[19]||(i[19]=p("label",null,"Canal de mise à jour :",-1)),R(p("select",{"onUpdate:modelValue":i[0]||(i[0]=e=>A(t).selectedChannel=e),onChange:r,class:"form-select"},[(h(!0),d(w,null,_(A(Tl),e=>(h(),d("option",{key:e.value,value:e.value},g(e.label)+" - "+g(e.description),9,ic))),128))],544),[[N,A(t).selectedChannel]])]),p("div",rc,[p("label",oc,[R(p("input",{"onUpdate:modelValue":i[1]||(i[1]=e=>A(t).showSecurityOnly=e),type:"checkbox",class:"form-checkbox"},null,512),[[x,A(t).showSecurityOnly]]),i[20]||(i[20]=p("span",{class:"checkbox-text"},"Mises à jour de sécurité uniquement",-1))])])])]),A(t).error?(h(),d("div",lc,[i[22]||(i[22]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),L(" "+g(A(t).error)+" ",1),p("button",{onClick:i[2]||(i[2]=e=>A(t).clearError()),class:"alert-close"},i[21]||(i[21]=[p("i",{class:"fas fa-times"},null,-1)]))])):v("",!0),A(t).hasUpdatePermissions?v("",!0):(h(),d("div",cc,i[23]||(i[23]=[p("i",{class:"fas fa-lock"},null,-1),L(" Votre licence ne permet pas le téléchargement de mises à jour. Contactez le support pour plus d'informations. ")]))),A(t).downloading?(h(),d("div",uc,[p("div",{class:"progress-header"},[i[25]||(i[25]=p("h3",null,[p("i",{class:"fas fa-download"}),L(" Téléchargement en cours... ")],-1)),p("button",{onClick:o,class:"btn btn-sm btn-danger"},i[24]||(i[24]=[p("i",{class:"fas fa-times"},null,-1),L(" Annuler ")]))]),A(t).downloadProgress?(h(),d("div",dc,[p("div",hc,[p("div",{class:"progress-fill",style:D({width:A(t).downloadProgressPercentage+"%"})},null,4)]),p("div",pc,[p("span",null,g(A(t).downloadProgressPercentage)+"%",1),p("span",null,g(A(t).formatFileSize(A(t).downloadProgress.loaded))+" / "+g(A(t).formatFileSize(A(t).downloadProgress.total)),1),p("span",null,g(A(t).formatSpeed(A(t).downloadProgress.speed)),1),p("span",null,g(A(t).formatTimeRemaining(A(t).downloadProgress.timeRemaining))+" restant",1)])])):v("",!0)])):v("",!0),p("div",fc,[A(t).loading&&!A(t).downloading?(h(),d("div",vc,i[26]||(i[26]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1),p("span",null,"Chargement des versions...",-1)]))):s.value?(h(),d("div",gc,[(h(!0),d(w,null,_(A(t).availableVersions,e=>{return h(),d("div",{key:e.id,class:"version-card"},[p("div",yc,[p("div",bc,[p("h3",wc,g(e.version),1),p("span",{class:m(["channel-badge",`channel-${e.status.toLowerCase()}`])},g(e.status),3)]),p("div",_c,g(n(e.release_date)),1)]),p("div",kc,[p("div",Cc,[i[28]||(i[28]=p("i",{class:"fas fa-hdd"},null,-1)),p("span",null,"Taille : "+g(e.file_size?A(t).formatFileSize(e.file_size):"Non disponible"),1)]),e.minimum_php_version?(h(),d("div",Sc,[i[29]||(i[29]=p("i",{class:"fas fa-arrow-up"},null,-1)),p("span",null,"PHP minimum requis : "+g(e.minimum_php_version),1)])):v("",!0),p("div",Ec,[i[30]||(i[30]=p("i",{class:"fas fa-fingerprint"},null,-1)),p("span",null,"Checksum : "+g(e.file_hash?e.file_hash.substring(0,16)+"...":"Non disponible"),1)])]),e.changelog?(h(),d("div",Rc,[i[31]||(i[31]=p("h4",null,"Notes de version :",-1)),p("div",{class:"changelog-content",innerHTML:(s=e.changelog,s.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"))},null,8,Tc)])):v("",!0),p("div",Ac,[p("button",{onClick:s=>(async e=>{await t.downloadVersion(e.id)&&J.info("[UpdatesView] Version téléchargée avec succès",{version:e.version,channel:e.channel})})(e),disabled:!A(t).canDownload,class:"btn btn-primary btn-block"},i[32]||(i[32]=[p("i",{class:"fas fa-download"},null,-1),L(" Télécharger ")]),8,Oc)])]);var s}),128))])):(h(),d("div",mc,i[27]||(i[27]=[p("i",{class:"fas fa-box-open"},null,-1),p("h3",null,"Aucune version disponible",-1),p("p",null,"Aucune mise à jour n'est disponible pour le canal sélectionné",-1)])))])]))}}),[["__scopeId","data-v-dc73aab0"]]),Mc=[{value:"monthly",label:"Mensuel",description:"Facturé chaque mois",months:1},{value:"quarterly",label:"Trimestriel",description:"Facturé tous les 3 mois",months:3},{value:"semi_annually",label:"Semestriel",description:"Facturé tous les 6 mois",months:6},{value:"annually",label:"Annuel",description:"Facturé chaque année",months:12},{value:"biennially",label:"Biennal",description:"Facturé tous les 2 ans",months:24},{value:"triennially",label:"Triennal",description:"Facturé tous les 3 ans",months:36}],Lc={class:"shop-view"},Pc={class:"shop-header"},Nc={class:"header-actions"},Uc={key:0,class:"cart-badge"},xc={class:"filters-section"},Dc={class:"filters-row"},Bc={class:"search-group"},Vc={class:"search-input-wrapper"},qc={class:"filter-group"},Hc=["value"],Gc={class:"filter-group"},jc={class:"checkbox-label"},Fc={key:0,class:"alert alert-danger"},$c={class:"shop-content"},Wc={key:0,class:"loading-state"},zc={key:1,class:"empty-state"},Jc={key:2,class:"products-grid"},Kc={key:0,class:"featured-badge"},Yc={class:"product-header"},Qc={class:"product-name"},Xc={class:"product-price"},Zc={class:"price"},eu={class:"period"},tu={key:0,class:"setup-fee"},su={key:1,class:"product-description"},nu={class:"product-highlights"},au={class:"highlight-item"},iu={class:"highlight-item"},ru={key:0,class:"highlight-item"},ou={key:2,class:"product-features"},lu={key:0,class:"more-features"},cu={class:"product-actions"},uu=["onClick","disabled"],du={key:0,class:"fas fa-check"},hu={key:1,class:"fas fa-shopping-cart"},pu={key:1,class:"floating-cart"},fu={class:"cart-count"},vu={class:"cart-total"},mu=He(r({__name:"ShopView",setup(e){const t=Lt(),s=a([]),n=a(!1),r=a(null),o=a(""),l=a(""),u=a(!1),y=i(()=>s.value.length>0),k=i(()=>{let e=s.value;if(o.value){const t=o.value.toLowerCase();e=e.filter(e=>{var s;return e.name.toLowerCase().includes(t)||(null==(s=e.description)?void 0:s.toLowerCase().includes(t))})}return l.value&&(e=e.filter(e=>e.billing_cycle===l.value)),u.value&&(e=e.filter(e=>e.is_featured)),e.sort((e,t)=>e.is_featured&&!t.is_featured?-1:!e.is_featured&&t.is_featured?1:e.sort_order!==t.sort_order?e.sort_order-t.sort_order:e.name.localeCompare(t.name))}),C=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),S=e=>{const t=Mc.find(t=>t.value===e);return(null==t?void 0:t.label.toLowerCase())||e};return c(async()=>{J.info("[ShopView] Vue boutique client montée"),await(async()=>{var e,t;n.value=!0,r.value=null;try{const n=await $.routes.client.shop.getTemplates();if(!(null==(e=n.data)?void 0:e.success)||!n.data.templates)throw new Error((null==(t=n.data)?void 0:t.message)||"Erreur lors du chargement des produits");s.value=n.data.templates,J.info("[ShopView] Templates chargés",{count:s.value.length})}catch(a){r.value=a.message||"Erreur lors du chargement des produits",J.error("[ShopView] Erreur loadTemplates",{error:a.message})}finally{n.value=!1}})()}),(e,s)=>{const a=E("router-link");return h(),d("div",Lc,[p("div",Pc,[s[6]||(s[6]=p("div",{class:"header-content"},[p("h1",{class:"page-title"},[p("i",{class:"fas fa-shopping-bag"}),L(" Boutique TechCMS ")]),p("p",{class:"page-description"}," Découvrez nos licences TechCMS et gérez vos achats depuis votre espace client ")],-1)),p("div",Nc,[f(a,{to:"/client/cart",class:"btn btn-outline"},{default:b(()=>[s[3]||(s[3]=p("i",{class:"fas fa-shopping-cart"},null,-1)),s[4]||(s[4]=L(" Panier ")),A(t).itemCount>0?(h(),d("span",Uc,g(A(t).itemCount),1)):v("",!0)]),_:1,__:[3,4]}),f(a,{to:"/client/orders",class:"btn btn-primary"},{default:b(()=>s[5]||(s[5]=[p("i",{class:"fas fa-history"},null,-1),L(" Mes commandes ")])),_:1,__:[5]})])]),p("div",xc,[p("div",Dc,[p("div",Bc,[p("div",Vc,[s[7]||(s[7]=p("i",{class:"fas fa-search"},null,-1)),R(p("input",{"onUpdate:modelValue":s[0]||(s[0]=e=>o.value=e),type:"text",placeholder:"Rechercher un produit...",class:"search-input"},null,512),[[T,o.value]])])]),p("div",qc,[s[9]||(s[9]=p("label",null,"Cycle de facturation :",-1)),R(p("select",{"onUpdate:modelValue":s[1]||(s[1]=e=>l.value=e),class:"form-select"},[s[8]||(s[8]=p("option",{value:""},"Tous les cycles",-1)),(h(!0),d(w,null,_(A(Mc),e=>(h(),d("option",{key:e.value,value:e.value},g(e.label),9,Hc))),128))],512),[[N,l.value]])]),p("div",Gc,[p("label",jc,[R(p("input",{"onUpdate:modelValue":s[2]||(s[2]=e=>u.value=e),type:"checkbox",class:"form-checkbox"},null,512),[[x,u.value]]),s[10]||(s[10]=p("span",null,"Produits mis en avant uniquement",-1))])])])]),r.value?(h(),d("div",Fc,[s[11]||(s[11]=p("i",{class:"fas fa-exclamation-triangle"},null,-1)),L(" "+g(r.value),1)])):v("",!0),p("div",$c,[n.value?(h(),d("div",Wc,s[12]||(s[12]=[p("i",{class:"fas fa-spinner fa-spin"},null,-1),p("span",null,"Chargement des produits...",-1)]))):y.value?(h(),d("div",Jc,[(h(!0),d(w,null,_(k.value,e=>(h(),d("div",{key:e.id,class:m(["product-card",{featured:e.is_featured}])},[e.is_featured?(h(),d("div",Kc,s[14]||(s[14]=[p("i",{class:"fas fa-star"},null,-1),L(" Populaire ")]))):v("",!0),p("div",Yc,[p("h3",Qc,g(e.name),1),p("div",Xc,[p("span",Zc,g(C(e.price)),1),p("span",eu,"/ "+g(S(e.billing_cycle)),1)]),e.setup_fee&&e.setup_fee>0?(h(),d("div",tu," + "+g(C(e.setup_fee))+" frais d'installation ",1)):v("",!0)]),e.description?(h(),d("div",su,g(e.description),1)):v("",!0),p("div",nu,[p("div",au,[s[15]||(s[15]=p("i",{class:"fas fa-globe"},null,-1)),p("span",null,g(e.domain_limit)+" domaine"+g(e.domain_limit>1?"s":""),1)]),p("div",iu,[s[16]||(s[16]=p("i",{class:"fas fa-download"},null,-1)),p("span",null,g(e.installation_limit)+" installation"+g(e.installation_limit>1?"s":""),1)]),e.update_permissions?(h(),d("div",ru,s[17]||(s[17]=[p("i",{class:"fas fa-sync"},null,-1),p("span",null,"Mises à jour incluses",-1)]))):v("",!0)]),e.features&&e.features.length?(h(),d("div",ou,[s[19]||(s[19]=p("h4",null,"Fonctionnalités :",-1)),p("ul",null,[(h(!0),d(w,null,_(e.features.slice(0,3),e=>(h(),d("li",{key:e},[s[18]||(s[18]=p("i",{class:"fas fa-check"},null,-1)),L(" "+g(e),1)]))),128)),e.features.length>3?(h(),d("li",lu," + "+g(e.features.length-3)+" autres fonctionnalités ",1)):v("",!0)])])):v("",!0),p("div",cu,[f(a,{to:`/client/shop/product/${e.id}`,class:"btn btn-outline btn-sm"},{default:b(()=>s[20]||(s[20]=[p("i",{class:"fas fa-eye"},null,-1),L(" Voir détails ")])),_:2,__:[20]},1032,["to"]),p("button",{onClick:s=>(e=>{t.addItem(e),J.info("[ShopView] Produit ajouté au panier",{template_id:e.id,template_name:e.name})})(e),disabled:A(t).hasItem(e.id),class:"btn btn-primary"},[A(t).hasItem(e.id)?(h(),d("i",du)):(h(),d("i",hu)),L(" "+g(A(t).hasItem(e.id)?"Dans le panier":"Ajouter au panier"),1)],8,uu)])],2))),128))])):(h(),d("div",zc,s[13]||(s[13]=[p("i",{class:"fas fa-box-open"},null,-1),p("h3",null,"Aucun produit disponible",-1),p("p",null,"Aucun produit ne correspond à vos critères de recherche",-1)])))]),A(t).itemCount>0?(h(),d("div",pu,[f(a,{to:"/client/cart",class:"floating-cart-btn"},{default:b(()=>[s[21]||(s[21]=p("i",{class:"fas fa-shopping-cart"},null,-1)),p("span",fu,g(A(t).itemCount),1),p("span",vu,g(C(A(t).total)),1)]),_:1,__:[21]})])):v("",!0)])}}}),[["__scopeId","data-v-6c665bb0"]]),gu={class:"cart-view"},yu={class:"cart-content"},bu={key:0,class:"empty-cart"},wu={key:1,class:"cart-with-items"},_u={class:"cart-items"},ku={class:"cart-items-header"},Cu={class:"items-list"},Su={class:"item-info"},Eu={class:"item-name"},Ru={key:0,class:"item-description"},Tu={class:"item-details"},Au={class:"detail-item"},Ou={class:"detail-item"},Iu={key:0,class:"detail-item"},Mu={class:"item-pricing"},Lu={class:"item-price"},Pu={class:"price"},Nu={class:"period"},Uu={key:0,class:"setup-fee"},xu={class:"item-subtotal"},Du={class:"item-actions"},Bu=["onClick"],Vu={class:"order-summary"},qu={class:"summary-card"},Hu={class:"summary-line"},Gu={key:0,class:"summary-line"},ju={class:"summary-line tax-line"},Fu={class:"summary-line total-line"},$u={class:"checkout-actions"},Wu=He(r({__name:"CartView",setup(e){const t=Lt(),s=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),n=e=>{const t=Mc.find(t=>t.value===e);return(null==t?void 0:t.label.toLowerCase())||e};return(e,a)=>{const i=E("router-link");return h(),d("div",gu,[a[15]||(a[15]=p("div",{class:"cart-header"},[p("h1",{class:"page-title"},[p("i",{class:"fas fa-shopping-cart"}),L(" Votre panier ")]),p("p",{class:"page-description"}," Vérifiez vos articles et procédez au checkout ")],-1)),p("div",yu,[A(t).isEmpty?(h(),d("div",bu,[a[1]||(a[1]=p("div",{class:"empty-cart-icon"},[p("i",{class:"fas fa-shopping-cart"})],-1)),a[2]||(a[2]=p("h3",null,"Votre panier est vide",-1)),a[3]||(a[3]=p("p",null,"Découvrez nos licences TechCMS et ajoutez-les à votre panier",-1)),f(i,{to:"/store",class:"btn btn-primary btn-lg"},{default:b(()=>a[0]||(a[0]=[p("i",{class:"fas fa-shopping-bag"},null,-1),L(" Voir la boutique ")])),_:1,__:[0]})])):(h(),d("div",wu,[p("div",_u,[p("div",ku,[p("h2",null,"Articles dans votre panier ("+g(A(t).itemCount)+")",1)]),p("div",Cu,[(h(!0),d(w,null,_(A(t).items,e=>(h(),d("div",{key:e.id,class:"cart-item"},[p("div",Su,[p("h3",Eu,g(e.template.name),1),e.template.description?(h(),d("p",Ru,g(e.template.description),1)):v("",!0),p("div",Tu,[p("div",Au,[a[4]||(a[4]=p("i",{class:"fas fa-globe"},null,-1)),p("span",null,g(e.template.domain_limit)+" domaine"+g(e.template.domain_limit>1?"s":""),1)]),p("div",Ou,[a[5]||(a[5]=p("i",{class:"fas fa-download"},null,-1)),p("span",null,g(e.template.installation_limit)+" installation"+g(e.template.installation_limit>1?"s":""),1)]),e.template.update_permissions?(h(),d("div",Iu,a[6]||(a[6]=[p("i",{class:"fas fa-sync"},null,-1),p("span",null,"Mises à jour incluses",-1)]))):v("",!0)])]),p("div",Mu,[p("div",Lu,[p("span",Pu,g(s(e.template.price)),1),p("span",Nu,"/ "+g(n(e.template.billing_cycle)),1)]),e.template.setup_fee&&e.template.setup_fee>0?(h(),d("div",Uu," + "+g(s(e.template.setup_fee))+" frais d'installation ",1)):v("",!0),p("div",xu,[p("strong",null,g(s(e.subtotal)),1)])]),p("div",Du,[p("button",{onClick:s=>A(t).removeItem(e.id),class:"remove-btn",title:"Supprimer cet article"},a[7]||(a[7]=[p("i",{class:"fas fa-trash"},null,-1)]),8,Bu)])]))),128))])]),p("div",Vu,[p("div",qu,[a[14]||(a[14]=p("h3",null,"Résumé de la commande",-1)),p("div",Hu,[a[8]||(a[8]=p("span",null,"Sous-total",-1)),p("span",null,g(s(A(t).subtotal)),1)]),A(t).setupFees>0?(h(),d("div",Gu,[a[9]||(a[9]=p("span",null,"Frais d'installation",-1)),p("span",null,g(s(A(t).setupFees)),1)])):v("",!0),p("div",ju,[a[10]||(a[10]=p("span",null,"TVA (20%)",-1)),p("span",null,g(s(A(t).taxAmount)),1)]),p("div",Fu,[a[11]||(a[11]=p("span",null,[p("strong",null,"Total")],-1)),p("span",null,[p("strong",null,g(s(A(t).total)),1)])]),p("div",$u,[f(i,{to:"/store",class:"btn btn-outline"},{default:b(()=>a[12]||(a[12]=[p("i",{class:"fas fa-arrow-left"},null,-1),L(" Continuer mes achats ")])),_:1,__:[12]}),f(i,{to:"/checkout",class:"btn btn-primary btn-lg"},{default:b(()=>a[13]||(a[13]=[p("i",{class:"fas fa-credit-card"},null,-1),L(" Procéder au checkout ")])),_:1,__:[13]})])])])]))])])}}}),[["__scopeId","data-v-7d9e9be0"]]),zu={class:"checkout-view"},Ju={key:0,class:"empty-cart-message"},Ku={class:"message-card"},Yu={key:1,class:"checkout-process"},Qu={class:"checkout-steps"},Xu={class:"checkout-content"},Zu={key:0,class:"checkout-step"},ed={class:"step-content"},td={class:"auth-options"},sd={class:"auth-option"},nd={class:"auth-option"},ad={key:1,class:"authenticated-checkout"},id={class:"checkout-main"},rd={class:"client-info"},od={class:"info-display"},ld={key:0},cd={class:"order-actions"},ud=["disabled"],dd={key:0,class:"fas fa-spinner fa-spin"},hd={key:1,class:"fas fa-credit-card"},pd={class:"order-summary"},fd={class:"summary-card"},vd={class:"order-items"},md={class:"item-info"},gd={class:"item-price"},yd={class:"order-totals"},bd={class:"total-line"},wd={key:0,class:"total-line"},_d={class:"total-line"},kd={class:"total-line final-total"},Cd=He(r({__name:"CheckoutView",setup(e){const t=Lt(),s=oe(),n=o(),r=a(!1),l=i(()=>s.isAuthenticated?2:1),u=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),y=e=>{const t=Mc.find(t=>t.value===e);return(null==t?void 0:t.label)||e},k=async()=>{r.value=!0;try{await new Promise(e=>setTimeout(e,2e3)),J.info("[CheckoutView] Commande traitée avec succès"),t.clearCart(),n.push("/dashboard")}catch(e){J.error("[CheckoutView] Erreur lors du traitement de la commande",{error:e})}finally{r.value=!1}};return c(()=>{J.info("[CheckoutView] Vue checkout montée")}),(e,n)=>{var a,i,o,c,C;const S=E("router-link");return h(),d("div",zu,[n[25]||(n[25]=p("div",{class:"checkout-header"},[p("h1",{class:"page-title"},[p("i",{class:"fas fa-credit-card"}),L(" Finaliser votre commande ")]),p("p",{class:"page-description"}," Vous êtes à une étape de votre licence TechCMS ")],-1)),A(t).isEmpty?(h(),d("div",Ju,[p("div",Ku,[n[1]||(n[1]=p("i",{class:"fas fa-shopping-cart"},null,-1)),n[2]||(n[2]=p("h3",null,"Votre panier est vide",-1)),n[3]||(n[3]=p("p",null,"Ajoutez des produits à votre panier avant de procéder au checkout",-1)),f(S,{to:"/store",class:"btn btn-primary"},{default:b(()=>n[0]||(n[0]=[p("i",{class:"fas fa-shopping-bag"},null,-1),L(" Voir la boutique ")])),_:1,__:[0]})])])):(h(),d("div",Yu,[p("div",Qu,[p("div",{class:m(["step",{active:l.value>=1,completed:l.value>1}])},n[4]||(n[4]=[p("div",{class:"step-number"},"1",-1),p("span",null,"Connexion",-1)]),2),p("div",{class:m(["step",{active:l.value>=2,completed:l.value>2}])},n[5]||(n[5]=[p("div",{class:"step-number"},"2",-1),p("span",null,"Informations",-1)]),2),p("div",{class:m(["step",{active:l.value>=3,completed:l.value>3}])},n[6]||(n[6]=[p("div",{class:"step-number"},"3",-1),p("span",null,"Paiement",-1)]),2),p("div",{class:m(["step",{active:l.value>=4}])},n[7]||(n[7]=[p("div",{class:"step-number"},"4",-1),p("span",null,"Confirmation",-1)]),2)]),p("div",Xu,[1===l.value?(h(),d("div",Zu,[p("div",ed,[n[15]||(n[15]=p("h2",null,"Connexion ou création de compte",-1)),n[16]||(n[16]=p("p",null,"Pour finaliser votre commande, vous devez vous connecter ou créer un compte",-1)),p("div",td,[p("div",sd,[n[9]||(n[9]=p("h3",null,"Vous avez déjà un compte ?",-1)),n[10]||(n[10]=p("p",null,"Connectez-vous pour accéder à votre espace client",-1)),f(S,{to:"/login",class:"btn btn-primary btn-lg"},{default:b(()=>n[8]||(n[8]=[p("i",{class:"fas fa-sign-in-alt"},null,-1),L(" Se connecter ")])),_:1,__:[8]})]),n[14]||(n[14]=p("div",{class:"auth-divider"},[p("span",null,"ou")],-1)),p("div",nd,[n[12]||(n[12]=p("h3",null,"Nouveau client ?",-1)),n[13]||(n[13]=p("p",null,"Créez votre compte pour gérer vos licences",-1)),f(S,{to:"/register",class:"btn btn-outline btn-lg"},{default:b(()=>n[11]||(n[11]=[p("i",{class:"fas fa-user-plus"},null,-1),L(" Créer un compte ")])),_:1,__:[11]})])])])])):(h(),d("div",ad,[p("div",id,[n[18]||(n[18]=p("h2",null,"Finalisation de votre commande",-1)),n[19]||(n[19]=p("p",null,"Vérifiez vos informations et procédez au paiement",-1)),p("div",rd,[n[17]||(n[17]=p("h3",null,"Informations de facturation",-1)),p("div",od,[p("p",null,[p("strong",null,g(null==(a=A(s).user)?void 0:a.firstName)+" "+g(null==(i=A(s).user)?void 0:i.lastName),1)]),p("p",null,g(null==(o=A(s).user)?void 0:o.email),1),(null==(c=A(s).user)?void 0:c.company)?(h(),d("p",ld,g(null==(C=A(s).user)?void 0:C.company),1)):v("",!0)])]),p("div",cd,[p("button",{onClick:k,disabled:r.value,class:"btn btn-primary btn-lg btn-block"},[r.value?(h(),d("i",dd)):(h(),d("i",hd)),L(" "+g(r.value?"Traitement...":`Payer ${u(A(t).total)}`),1)],8,ud)])])])),p("div",pd,[p("div",fd,[n[24]||(n[24]=p("h3",null,"Votre commande",-1)),p("div",vd,[(h(!0),d(w,null,_(A(t).items,e=>(h(),d("div",{key:e.id,class:"order-item"},[p("div",md,[p("h4",null,g(e.template.name),1),p("p",null,g(y(e.template.billing_cycle)),1)]),p("div",gd,g(u(e.template.price)),1)]))),128))]),p("div",yd,[p("div",bd,[n[20]||(n[20]=p("span",null,"Sous-total",-1)),p("span",null,g(u(A(t).subtotal)),1)]),A(t).setupFees>0?(h(),d("div",wd,[n[21]||(n[21]=p("span",null,"Frais d'installation",-1)),p("span",null,g(u(A(t).setupFees)),1)])):v("",!0),p("div",_d,[n[22]||(n[22]=p("span",null,"TVA (20%)",-1)),p("span",null,g(u(A(t).taxAmount)),1)]),p("div",kd,[n[23]||(n[23]=p("span",null,[p("strong",null,"Total")],-1)),p("span",null,[p("strong",null,g(u(A(t).total)),1)])])])])])])]))])}}}),[["__scopeId","data-v-ffe1dcdc"]]),Sd=B({history:V("/client"),routes:[{path:"/",redirect:"/store"},{path:"/login",name:"login",component:mr,meta:{public:!0,requiresAuth:!1,hideForAuthenticated:!0,hideLayout:!0}},{path:"/register",name:"register",component:qr,meta:{public:!0,requiresAuth:!1,hideForAuthenticated:!0,hideLayout:!0}},{path:"/store",name:"store-public",component:mu,meta:{public:!0,requiresAuth:!1}},{path:"/client/store",name:"store-private",component:mu,meta:{public:!1,requiresAuth:!0}},{path:"/cart",name:"cart",component:Wu,meta:{public:!0,requiresAuth:!1}},{path:"/checkout",name:"checkout",component:Cd,meta:{public:!0,requiresAuth:!1}},{path:"/dashboard",name:"dashboard",component:pn,meta:{public:!1,requiresAuth:!0}},{path:"/licenses",name:"licenses",component:Oo,meta:{public:!1,requiresAuth:!0}},{path:"/licenses/:id",name:"license-detail",component:El,meta:{public:!1,requiresAuth:!0}},{path:"/updates",name:"updates",component:Ic,meta:{public:!1,requiresAuth:!0}},{path:"/billing",name:"billing",component:ha,meta:{public:!1,requiresAuth:!0}},{path:"/support",name:"support",component:Xa,meta:{public:!1,requiresAuth:!0}},{path:"/account",name:"account",component:ir,meta:{public:!1,requiresAuth:!0}},{path:"/billing/invoice/:id",name:"invoice-detail",component:()=>ts(()=>import("./InvoiceDetailView-DWf_kU5s.js"),__vite__mapDeps([0,1,2,3])),meta:{public:!1,requiresAuth:!0}},{path:"/support/ticket/:id",name:"ticket-detail",component:()=>ts(()=>import("./TicketDetailView-CdHdZWHy.js"),__vite__mapDeps([4,1,2,5])),meta:{public:!1,requiresAuth:!0}},{path:"/:pathMatch(.*)*",name:"not-found",redirect:"/dashboard"}]});Sd.beforeEach(async(e,t,s)=>{J.debug("[ROUTER] Navigation",{from:t.path,to:e.path,name:e.name});const n=oe();n.initialized||await n.initialize();const a=n.isAuthenticated,i=e.meta.requiresAuth,r=e.meta.hideForAuthenticated;J.debug("[ROUTER] État d'authentification",{isAuthenticated:a,requiresAuth:i,hideForAuthenticated:r,path:e.path}),!i||a?a&&r?s({name:"dashboard"}):(J.debug("[ROUTER] Navigation autorisée vers",{path:e.path}),s()):s({name:"login",query:{redirect:e.fullPath}})});const Ed={common:{loading:"Chargement...",error:"Une erreur est survenue",save:"Enregistrer",cancel:"Annuler",edit:"Modifier",delete:"Supprimer",view:"Voir",export:"Exporter",search:"Rechercher...",filter:"Filtrer",all:"Toutes",active:"Actif",inactive:"Inactif",status:"Statut",date:"Date",amount:"Montant",back:"Retour",refresh:"Actualiser",download:"Télécharger",close:"Fermer",open:"Ouvrir",retry:"Réessayer",send:"Envoyer",reply:"Répondre",month:"mois",year:"année",day:"jour",hour:"heure",minute:"minute",second:"seconde"},dashboard:{title:"Tableau de bord",client_interface:"Interface Client",my_licenses:"Mes Licences",unpaid_invoices:"Factures Impayées",open_tickets:"Tickets Ouverts",total_due:"Montant Dû",recent_services:"Services Récents",recent_invoices:"Factures Récentes",recent_tickets:"Tickets Récents",view_all:"Voir tout",no_data:"Aucune donnée disponible"},notifications:{title:"Notifications",no_notifications:"Aucune notification",mark_read:"Marquer comme lu",mark_all_read:"Tout marquer comme lu",clear_all:"Tout effacer",remove:"Supprimer",view_all:"Voir toutes les notifications",new_service:"Nouveau service activé",service_suspended:"Service suspendu",service_renewed:"Service renouvelé",new_invoice:"Nouvelle facture générée",invoice_paid:"Facture payée",invoice_overdue:"Facture en retard",new_ticket_reply:"Nouvelle réponse à votre ticket",ticket_status_changed:"Statut du ticket modifié",system_maintenance:"Maintenance système programmée"},services:{title:"Mes Services",new_service:"Nouveau Service",manage:"Gérer",filter_by_status:"Filtrer par statut",no_services:"Aucun service trouvé",service_name:"Nom du service",service_type:"Type de service",created_date:"Date de création",last_update:"Dernière mise à jour",monthly_price:"Prix mensuel",next_billing:"Prochaine facturation",billing_cycle:"Cycle de facturation",configuration:"Configuration",hostname:"Nom d'hôte",username:"Nom d'utilisateur",port:"Port",database:"Base de données",general_info:"Informations générales",recent_history:"Historique récent",no_history:"Aucun historique disponible",back_to_services:"Retour aux services",manage_service:"Gérer le service",all_statuses:"Tous les statuts",all_types:"Tous les types",type:"Type",web_hosting:"Hébergement Web",vps:"Serveur Virtuel",domain:"Nom de Domaine",email:"Email",ssl:"Certificat SSL",search_placeholder:"Rechercher un service...",loading_services:"Chargement de vos services...",no_results:"Aucun service ne correspond à vos critères de recherche.",no_active_services:"Vous n'avez pas encore de services actifs.",order_service:"Commander un service",expires_on:"Expire le",statistics:"Statistiques",renew:"Renouveler"},billing:{title:"Facturation",pay_unpaid:"Payer les impayées",unpaid:"Impayées",paid:"Payées",cancelled:"Annulées",overdue:"En retard",download:"Télécharger",pay:"Payer",pay_now:"Payer maintenant",invoice_number:"Numéro de facture",issue_date:"Date d'émission",due_date:"Date d'échéance",payment_date:"Date de paiement",total_amount:"Montant total",subtotal:"Sous-total",tax:"TVA",invoice_summary:"Résumé de la facture",service_details:"Détail des services",payment_history:"Historique des paiements",description:"Description",period:"Période",quantity:"Quantité",unit_price:"Prix unitaire",total:"Total",download_pdf:"Télécharger PDF",back_to_billing:"Retour à la facturation",payment_method:"Méthode de paiement",payment_status:"Statut du paiement",no_invoices:"Aucune facture récente",total_invoices:"Total Factures",unpaid_invoices:"Factures Impayées",amount_due:"Montant Dû",total_paid:"Total Payé",all_statuses:"Tous les statuts",draft:"Brouillon",all_periods:"Toutes les périodes",current_month:"Ce mois",last_month:"Mois dernier",current_year:"Cette année",last_year:"Année dernière",search_placeholder:"Numéro de facture..."},support:{title:"Support",new_ticket:"Nouveau Ticket",my_tickets:"Mes Tickets",priority:"Priorité",open:"Ouvert",closed:"Fermé",pending:"En attente",reply:"Répondre",subject:"Sujet",message:"Message",department:"Département",assigned_to:"Assigné à",ticket_info:"Informations du ticket",conversation:"Conversation",add_reply:"Ajouter une réponse",your_message:"Votre message",attachments:"Pièces jointes",attachments_optional:"Pièces jointes (optionnel)",file_formats:"Formats acceptés: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (max 10MB par fichier)",selected_files:"Fichiers sélectionnés:",send_reply:"Envoyer la réponse",sending:"Envoi...",close_ticket:"Fermer le ticket",reopen_ticket:"Rouvrir le ticket",ticket_closed:"Ticket fermé",ticket_closed_message:"Ce ticket a été fermé. Pour ajouter une réponse, vous devez d'abord le rouvrir.",back_to_support:"Retour au support",you:"Vous",support_team:"Support",priority_low:"Faible",priority_medium:"Moyenne",priority_high:"Élevée",priority_urgent:"Urgente",no_tickets:"Aucun ticket de support",create_ticket:"Créer un Ticket",create_ticket_desc:"Signaler un problème ou demander de l'aide",knowledge_base:"Base de Connaissances",knowledge_base_desc:"Consultez nos guides et tutoriels",live_chat:"Chat en Direct",live_chat_desc:"Discutez avec notre équipe support",phone_support:"Support Téléphonique",phone_support_desc:"Appelez-nous au +33 1 23 45 67 89"},account:{title:"Mon Compte",personal_info:"Informations personnelles",security:"Sécurité",change_password:"Changer le mot de passe",current_password:"Mot de passe actuel",new_password:"Nouveau mot de passe",confirm_password:"Confirmer le mot de passe",first_name:"Prénom",last_name:"Nom",email:"Email",company:"Entreprise",phone:"Téléphone",address:"Adresse",city:"Ville",postal_code:"Code postal",country:"Pays",last_login:"Dernière connexion",member_since:"Membre depuis",password_strength:"Force du mot de passe",last_password_change:"Dernière modification",two_factor_auth:"Authentification à deux facteurs",login_history:"Historique de connexion",edit_profile:"Modifier le profil",save_changes:"Enregistrer les modifications"},auth:{login:"Connexion",register:"Inscription",logout:"Déconnexion",email:"Email",password:"Mot de passe",remember_me:"Se souvenir de moi",forgot_password:"Mot de passe oublié ?",no_account:"Pas de compte ?",already_account:"Déjà un compte ?",sign_in:"Se connecter",sign_up:"S'inscrire",welcome_back:"Bon retour !",create_account:"Créer un compte",not_connected:"Non connecté"},status:{active:"Actif",inactive:"Inactif",suspended:"Suspendu",terminated:"Terminé",pending:"En attente",paid:"Payée",unpaid:"Impayée",cancelled:"Annulée",overdue:"En retard",open:"Ouvert",closed:"Fermé",completed:"Terminé",failed:"Échoué",expired:"Expiré"},licenses:{title:"Mes Licences",my_licenses:"Mes Licences",license_details:"Détails de la Licence",license_information:"Informations de la Licence",license_key:"Clé de Licence",domain_limit:"Limite de Domaines",installation_limit:"Limite d'Installations",installations_used:"Installations utilisées",expires_at:"Expire le",created_at:"Créée le",view_details:"Voir les détails",verify_license:"Vérifier la Licence",verifying:"Vérification...",refresh:"Actualiser",restrictions:"Restrictions",allowed_domains:"Domaines Autorisés",allowed_ips:"IPs Autorisées",no_domain_restrictions:"Aucune restriction de domaine",no_ip_restrictions:"Aucune restriction d'IP",current_usage:"Utilisation Actuelle",actions:"Actions",stats:{total:"Total",active:"Actives",expired:"Expirées",expiring_soon:"Expirent Bientôt"},status:{label:"Statut",active:"Active",inactive:"Inactive",expired:"Expirée"},no_licenses:{title:"Aucune licence",description:"Vous n'avez actuellement aucune licence associée à votre compte."}}},Rd={common:{loading:"Loading...",error:"An error occurred",save:"Save",cancel:"Cancel",edit:"Edit",delete:"Delete",view:"View",export:"Export",search:"Search...",filter:"Filter",all:"All",active:"Active",inactive:"Inactive",status:"Status",date:"Date",amount:"Amount",back:"Back",refresh:"Refresh",download:"Download",close:"Close",open:"Open",retry:"Retry",send:"Send",reply:"Reply",month:"month",year:"year",day:"day",hour:"hour",minute:"minute",second:"second"},dashboard:{title:"Dashboard",client_interface:"Client Interface",my_licenses:"My Licenses",unpaid_invoices:"Unpaid Invoices",open_tickets:"Open Tickets",total_due:"Total Due",recent_services:"Recent Services",recent_invoices:"Recent Invoices",recent_tickets:"Recent Tickets",view_all:"View All",no_data:"No data available"},services:{title:"My Services",new_service:"New Service",manage:"Manage",filter_by_status:"Filter by status",no_services:"No services found",service_name:"Service name",service_type:"Service type",created_date:"Created date",last_update:"Last update",monthly_price:"Monthly price",next_billing:"Next billing",billing_cycle:"Billing cycle",configuration:"Configuration",hostname:"Hostname",username:"Username",port:"Port",database:"Database",general_info:"General information",recent_history:"Recent history",no_history:"No history available",back_to_services:"Back to services",manage_service:"Manage service",all_statuses:"All statuses",all_types:"All types",type:"Type",web_hosting:"Web Hosting",vps:"Virtual Server",domain:"Domain Name",email:"Email",ssl:"SSL Certificate",search_placeholder:"Search for a service...",loading_services:"Loading your services...",no_results:"No services match your search criteria.",no_active_services:"You don't have any active services yet.",order_service:"Order a service",expires_on:"Expires on",statistics:"Statistics",renew:"Renew"},billing:{title:"Billing",pay_unpaid:"Pay Unpaid",unpaid:"Unpaid",paid:"Paid",cancelled:"Cancelled",overdue:"Overdue",download:"Download",pay:"Pay",pay_now:"Pay now",invoice_number:"Invoice number",issue_date:"Issue date",due_date:"Due date",payment_date:"Payment date",total_amount:"Total amount",subtotal:"Subtotal",tax:"Tax",invoice_summary:"Invoice summary",service_details:"Service details",payment_history:"Payment history",description:"Description",period:"Period",quantity:"Quantity",unit_price:"Unit price",total:"Total",download_pdf:"Download PDF",back_to_billing:"Back to billing",payment_method:"Payment method",payment_status:"Payment status",no_invoices:"No recent invoices",total_invoices:"Total Invoices",unpaid_invoices:"Unpaid Invoices",amount_due:"Amount Due",total_paid:"Total Paid",all_statuses:"All statuses",draft:"Draft",all_periods:"All periods",current_month:"This month",last_month:"Last month",current_year:"This year",last_year:"Last year",search_placeholder:"Invoice number..."},support:{title:"Support",new_ticket:"New Ticket",my_tickets:"My Tickets",priority:"Priority",open:"Open",closed:"Closed",pending:"Pending",reply:"Reply",subject:"Subject",message:"Message",department:"Department",assigned_to:"Assigned to",ticket_info:"Ticket information",conversation:"Conversation",add_reply:"Add reply",your_message:"Your message",attachments:"Attachments",attachments_optional:"Attachments (optional)",file_formats:"Accepted formats: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (max 10MB per file)",selected_files:"Selected files:",send_reply:"Send reply",sending:"Sending...",close_ticket:"Close ticket",reopen_ticket:"Reopen ticket",ticket_closed:"Ticket closed",ticket_closed_message:"This ticket has been closed. To add a reply, you must first reopen it.",back_to_support:"Back to support",you:"You",support_team:"Support",priority_low:"Low",priority_medium:"Medium",priority_high:"High",priority_urgent:"Urgent",no_tickets:"No support tickets",create_ticket:"Create Ticket",create_ticket_desc:"Report an issue or request help",knowledge_base:"Knowledge Base",knowledge_base_desc:"Browse our guides and tutorials",live_chat:"Live Chat",live_chat_desc:"Chat with our support team",phone_support:"Phone Support",phone_support_desc:"Call us at +33 1 23 45 67 89"},account:{title:"My Account",personal_info:"Personal information",security:"Security",change_password:"Change password",current_password:"Current password",new_password:"New password",confirm_password:"Confirm password",first_name:"First name",last_name:"Last name",email:"Email",company:"Company",phone:"Phone",address:"Address",city:"City",postal_code:"Postal code",country:"Country",last_login:"Last login",member_since:"Member since",password_strength:"Password strength",last_password_change:"Last password change",two_factor_auth:"Two-factor authentication",login_history:"Login history",edit_profile:"Edit profile",save_changes:"Save changes"},auth:{login:"Login",register:"Register",logout:"Logout",email:"Email",password:"Password",remember_me:"Remember me",forgot_password:"Forgot password?",no_account:"No account?",already_account:"Already have an account?",sign_in:"Sign in",sign_up:"Sign up",welcome_back:"Welcome back!",create_account:"Create account",not_connected:"Not connected"},status:{active:"Active",inactive:"Inactive",suspended:"Suspended",terminated:"Terminated",pending:"Pending",paid:"Paid",unpaid:"Unpaid",cancelled:"Cancelled",overdue:"Overdue",open:"Open",closed:"Closed",completed:"Completed",failed:"Failed",expired:"Expired"},licenses:{title:"My Licenses",my_licenses:"My Licenses",license_details:"License Details",license_information:"License Information",license_key:"License Key",domain_limit:"Domain Limit",installation_limit:"Installation Limit",installations_used:"Installations used",expires_at:"Expires on",created_at:"Created on",view_details:"View details",verify_license:"Verify License",verifying:"Verifying...",refresh:"Refresh",restrictions:"Restrictions",allowed_domains:"Allowed Domains",allowed_ips:"Allowed IPs",no_domain_restrictions:"No domain restrictions",no_ip_restrictions:"No IP restrictions",current_usage:"Current Usage",actions:"Actions",stats:{total:"Total",active:"Active",expired:"Expired",expiring_soon:"Expiring Soon"},status:{label:"Status",active:"Active",inactive:"Inactive",expired:"Expired"},no_licenses:{title:"No licenses",description:"You currently have no licenses associated with your account."}}},Td=q({legacy:!1,locale:localStorage.getItem("language")||"fr",fallbackLocale:"en",messages:{fr:Ed,en:Rd}});J.info("Démarrage de l'application"),J.info("Création de l'application Vue");const Ad=H(Zt);Ad.config.errorHandler=(e,t,s)=>{const n=t?t.$.type.name:"UnknownComponent";J.error(`Erreur non capturée dans le composant: ${n}`,{error:e.toString(),stack:e.stack,info:s})},J.info("Initialisation de Pinia");const Od=G();Od.use(re),Ad.use(Od),J.info("Initialisation du Router"),Ad.use(Sd),J.info("Initialisation de i18n"),Ad.use(Td),J.info("FormKit a été retiré car nous utilisons maintenant des composants standard"),J.info("Initialisation du store auth");oe().initialize(),J.info("Montage de l'application"),Ad.mount("#vue-app"),J.info("Application montée avec succès");export{$ as A,He as _,oe as a,J as l,ke as u};
