import{a as e,c as a,e as t,i as s,g as l,t as n,B as i,F as r,k as u,n as o,f as c,r as d,w as v,o as p,b as m,A as g,l as f,s as h,v as k,u as y,m as b,N as T}from"./vendor-hWiorkjf.js";import{_ as I,u as E,A as C,l as P}from"./index-0n5RskIj.js";import"./utils-CG2kLDjF.js";const _={key:0,class:"pagination-container"},w={class:"pagination-info"},D={class:"page-indicator"},A={class:"total-items"},K={class:"pagination-controls"},S=["disabled"],j={class:"page-numbers"},R=["onClick"],M=["disabled"],L=I(e({__name:"PaginationComponent",props:{currentPage:{},totalPages:{},total:{},perPage:{default:7}},emits:["pageChange"],setup(e,{emit:d}){const v=e,p=d,m=a(()=>{const e=[];if(v.totalPages<=5)for(let a=1;a<=v.totalPages;a++)e.push(a);else{let a=Math.max(1,v.currentPage-2),t=Math.min(v.totalPages,v.currentPage+2);v.currentPage<=3&&(t=Math.min(v.totalPages,5)),v.currentPage>=v.totalPages-2&&(a=Math.max(1,v.totalPages-4));for(let s=a;s<=t;s++)e.push(s)}return e}),g=e=>{e>=1&&e<=v.totalPages&&e!==v.currentPage&&p("pageChange",e)};return(e,a)=>e.totalPages>1?(c(),t("div",_,[l("div",w,[l("span",D," Page "+n(e.currentPage)+" sur "+n(e.totalPages),1),l("span",A,n(e.total)+" "+n(e.total>1?"éléments":"élément")+" au total ",1)]),l("div",K,[l("button",{class:"btn btn-outline btn-sm pagination-btn",disabled:e.currentPage<=1,onClick:a[0]||(a[0]=a=>g(e.currentPage-1))},a[2]||(a[2]=[l("i",{class:"fas fa-chevron-left"},null,-1),i(" Précédent ")]),8,S),l("div",j,[(c(!0),t(r,null,u(m.value,a=>(c(),t("button",{key:a,class:o(["btn","btn-sm","page-btn",{"btn-primary":a===e.currentPage,"btn-outline":a!==e.currentPage}]),onClick:e=>g(a)},n(a),11,R))),128))]),l("button",{class:"btn btn-outline btn-sm pagination-btn",disabled:e.currentPage>=e.totalPages,onClick:a[1]||(a[1]=a=>g(e.currentPage+1))},a[3]||(a[3]=[i(" Suivant "),l("i",{class:"fas fa-chevron-right"},null,-1)]),8,M)])])):s("",!0)}}),[["__scopeId","data-v-f5fcb472"]]),N={id:"ticket-detail"},q={class:"ticket-detail-header"},x={class:"header-content"},F={class:"ticket-title-section"},V={class:"ticket-info"},O={class:"ticket-meta"},$={class:"ticket-id"},B={class:"ticket-actions"},z={key:0,class:"loading-container"},G={key:1,class:"error-container"},U={class:"error-message"},X={key:2,class:"ticket-content"},J={class:"ticket-info-section"},Z={class:"info-card"},H={class:"info-grid"},Q={class:"info-item"},W={class:"ticket-number"},Y={class:"info-item"},ee={class:"info-item"},ae={class:"info-item"},te={class:"info-item"},se={key:0,class:"info-item"},le={key:1,class:"info-item"},ne={class:"ticket-conversation"},ie={class:"conversation-card"},re={class:"conversation-list"},ue={class:"message-item client-message"},oe={class:"message-content"},ce={class:"message-header"},de={class:"message-date"},ve={class:"message-body"},pe={class:"message-avatar"},me={class:"message-content"},ge={class:"message-header"},fe={class:"message-author"},he={class:"message-date"},ke={class:"message-body"},ye={key:0,class:"message-attachments"},be=["href"],Te={key:0,class:"reply-form-section"},Ie={class:"reply-card"},Ee={class:"form-group"},Ce={class:"form-group"},Pe={key:0,class:"selected-files"},_e=["onClick"],we={class:"form-actions"},De=["disabled"],Ae={key:0,class:"fas fa-spinner fa-spin"},Ke={key:1,class:"fas fa-paper-plane"},Se={key:1,class:"closed-notice"},je={key:3,class:"realtime-notification"},Re={class:"realtime-notification-content"},Me=I(e({__name:"TicketDetailView",setup(e){const I=b(),_=y(),w=E(),D=d(!0),A=d(null),K=d(!1),S=d(null),j=d([]),R=d([]),M=d(1),Me=d(1),Le=d(0),Ne=d(7),qe=d({message:"",attachments:[]}),xe=d(null),Fe=d(!1),Ve=d(""),Oe=a(()=>!!S.value&&["open","answered","customer-reply"].includes(S.value.status)),$e=()=>{_.push("/support")},Be=async(e=1)=>{var a,t,s,l,n,i,r;try{D.value=!0,A.value=null;const i=parseInt(I.params.id);if(!i)throw new Error("ID de ticket invalide");const r=await C.routes.client.ticket.getById(i);S.value=r.data;try{const l=await C.routes.client.ticket.getMessages(i,{page:e,per_page:Ne.value});j.value=(null==(a=l.data)?void 0:a.data)||[],(null==(t=l.data)?void 0:t.pagination)&&(M.value=l.data.pagination.current_page,Me.value=l.data.pagination.total_pages,Le.value=l.data.pagination.total),P.info("[TICKET] Messages chargés avec succès",{ticketId:i,messagesCount:j.value.length,pagination:null==(s=l.data)?void 0:s.pagination})}catch(u){P.warn("[TICKET] Erreur lors du chargement des messages",{ticketId:i,error:u}),j.value=[]}P.info("[TICKET] Ticket chargé avec succès",{ticketId:null==(l=S.value)?void 0:l.id,title:null==(n=S.value)?void 0:n.title})}catch(o){P.error("[TICKET] Erreur lors du chargement du ticket",{ticketId:I.params.id,error:o}),A.value=(null==(r=null==(i=o.response)?void 0:i.data)?void 0:r.message)||"Erreur lors du chargement du ticket"}finally{D.value=!1}},ze=async()=>{var e,a,t,s,l,n,i,r;try{K.value=!0;const e=parseInt(I.params.id),a={message:qe.value.message.trim()};if(!a.message)return void(A.value="Le message ne peut pas être vide");P.info("[TICKET] Envoi de la réponse",{ticketId:e,messageLength:a.message.length});const t=await C.routes.client.ticket.addReply(e,a);P.info("[TICKET] Réponse envoyée avec succès",{ticketId:e}),Ue(),P.info("[TICKET] Réponse ajoutée avec succès",{ticketId:e}),t.data&&t.data.reply&&S.value&&j.value?(j.value.unshift(t.data.reply),P.info("[TICKET] Message ajouté à la liste locale pour affichage immédiat (ordre chronologique)",{ticketId:e,replyId:t.data.reply.id,totalReplies:j.value.length,insertionMethod:"unshift (début de liste)"})):(P.warn("[TICKET] Pas de données de réponse, rechargement nécessaire"),await Be(M.value))}catch(u){P.error("[TICKET] Erreur lors de l'envoi de la réponse",{ticketId:null==(e=S.value)?void 0:e.id,error:u}),401===(null==(a=u.response)?void 0:a.status)?A.value="Vous devez être connecté pour répondre à un ticket":404===(null==(t=u.response)?void 0:t.status)?A.value="Ticket non trouvé":400===(null==(s=u.response)?void 0:s.status)?A.value=(null==(n=null==(l=u.response)?void 0:l.data)?void 0:n.message)||"Données invalides":A.value=(null==(r=null==(i=u.response)?void 0:i.data)?void 0:r.message)||"Erreur lors de l'envoi de la réponse"}finally{K.value=!1}},Ge=e=>{const a=e.target;a.files&&(R.value=Array.from(a.files))},Ue=()=>{qe.value.message="",R.value=[];const e=document.getElementById("reply-attachments");e&&(e.value="")},Xe=async()=>{try{const e=parseInt(I.params.id);await C.routes.client.ticket.close(e),await Be()}catch(e){P.error("[TICKET] Erreur lors de la fermeture du ticket",{ticketId:I.params.id,error:e})}},Je=async()=>{try{const e=parseInt(I.params.id);await C.routes.client.ticket.reopen(e),await Be()}catch(e){P.error("[TICKET] Erreur lors de la réouverture du ticket",{ticketId:I.params.id,error:e})}},Ze=e=>({open:"status-open",answered:"status-answered","customer-reply":"status-customer-reply",closed:"status-closed"}[e||""]||"status-unknown"),He=e=>({open:"Ouvert",answered:"Répondu","customer-reply":"En attente de réponse",closed:"Fermé"}[e||""]||"Inconnu"),Qe=e=>({low:"priority-low",medium:"priority-medium",high:"priority-high",urgent:"priority-urgent"}[e||""]||"priority-unknown"),We=e=>({low:"Faible",medium:"Moyenne",high:"Élevée",urgent:"Urgente"}[e||""]||"Inconnue"),Ye=async e=>{await Be(e);const a=document.querySelector(".ticket-conversation");a&&a.scrollIntoView({behavior:"smooth"})},ea=e=>e?new Date(e).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit",timeZone:"Europe/Paris"}):"N/A";v(()=>w.lastRealtimeEvent,e=>{var a,t;if(!e||!S.value)return;const s=S.value.id,l=(null==(a=e.ticket)?void 0:a.ticket)||e.ticket,n=null==l?void 0:l.id;if(P.info("[TICKET DETAIL] Diagnostic comparaison ID événement",{eventStructure:{hasTicket:!!e.ticket,hasNestedTicket:!!(null==(t=e.ticket)?void 0:t.ticket),eventTicketId:n,eventTicketIdType:typeof n},currentTicketId:s,currentTicketIdType:typeof s,idsEqual:n==s,idsStrictEqual:n===s,willProcess:n==s,fullEventStructure:Object.keys(e)}),n==s)switch(P.info("[TICKET DETAIL] Événement temps réel reçu et traité",{event:e}),e.action){case"reply":aa(e);break;case"status_change":ta(e);break;case"update":sa(e)}else P.warn("[TICKET DETAIL] Événement ignoré - ID ticket différent",{eventTicketId:n,currentTicketId:s,eventStructure:"Structure imbriquée détectée"})});const aa=async e=>{var a,t,s,l,n,i,r,u;if(P.info("[TICKET DETAIL] handleRealtimeReply appelé",{eventAuthor:e.author,authorType:null==(a=e.author)?void 0:a.type,authorName:null==(t=e.author)?void 0:t.name,currentPage:M.value,totalPages:Me.value,hasTicketReplies:!!j.value,repliesCount:(null==(s=j.value)?void 0:s.length)||0,fullEvent:e}),"client"!==e.author.type){if(P.info("[TICKET DETAIL] Événement admin détecté, traitement en cours",{authorType:null==(n=e.author)?void 0:n.type,authorName:null==(i=e.author)?void 0:i.name,willProcess:!0}),j.value){const a=(null==(r=e.ticket)?void 0:r.reply)||e.reply,t=a?{id:"string"==typeof a.id?parseInt(a.id):a.id,ticket_id:parseInt(I.params.id),user_id:"string"==typeof a.user_id?parseInt(a.user_id):a.user_id,message:a.message,user_type:"admin",author_name:a.author_name||e.author.name,is_staff:!0,created_at:a.created_at}:{id:Date.now(),ticket_id:parseInt(I.params.id),user_id:e.author.id||0,message:`Nouvelle réponse de ${e.author.name}`,user_type:"admin",author_name:e.author.name,is_staff:!0,created_at:(new Date).toISOString()};j.value.unshift(t),Le.value+=1,await T(),P.info("[TICKET DETAIL] Message admin ajouté localement sans rechargement (toutes pages)",{authorName:e.author.name,totalMessages:Le.value,newRepliesCount:j.value.length,adminMessageAdded:t,ticketRepliesAfter:j.value.slice(0,3),reactivityForced:!0,currentPage:M.value,totalPages:Me.value,correctionApplied:"Suppression condition pagination restrictive",hasReplyData:!!a,replyData:a||"Pas de données réponse",messageSource:a?"Vraies données API (structure imbriquée)":"Message temporaire générique",structureDetected:a===(null==(u=e.ticket)?void 0:u.reply)?"Structure imbriquée event.ticket.reply":"Structure directe event.reply"})}else Le.value+=1,P.info("[TICKET DETAIL] Nouveau message admin détecté, total mis à jour sans rechargement");la(`Nouvelle réponse de ${e.author.name}`),P.info("[TICKET DETAIL] Mise à jour après réponse admin via temps réel")}else P.debug("[TICKET DETAIL] Événement client ignoré (éviter boucle)",{event:e,authorType:null==(l=e.author)?void 0:l.type,shouldIgnore:!0})},ta=e=>{var a;"client"!==e.author.type&&(S.value&&e.ticket&&(S.value.status=e.ticket.status),la(`Statut changé en "${(null==(a=e.ticket)?void 0:a.status)||"nouveau statut"}" par ${e.author.name}`))},sa=e=>{"client"!==e.author.type&&(e.ticket&&S.value?(Object.assign(S.value,e.ticket),P.info("[TICKET DETAIL] Mise à jour dynamique données ticket sans rechargement")):Be(M.value),la(`Ticket mis à jour par ${e.author.name}`))},la=e=>{Ve.value=e,Fe.value=!0,setTimeout(()=>{Fe.value=!1},5e3)};return p(()=>{Be(),setTimeout(()=>{(()=>{const e=parseInt(I.params.id);e&&w.isReady&&(xe.value&&(xe.value(),xe.value=null),xe.value=w.subscribeToTicket(e),P.info("[TICKET DETAIL] Abonnement temps réel activé",{ticketId:e}))})()},1e3)}),m(()=>{xe.value&&(xe.value(),xe.value=null)}),(e,a)=>{var d,v,p,m,y,b,T,I;return c(),t("div",N,[l("div",q,[l("div",x,[l("button",{class:"btn btn-outline btn-sm back-btn",onClick:$e},a[3]||(a[3]=[l("i",{class:"fas fa-arrow-left"},null,-1),i(" Retour au support ")])),l("div",F,[a[4]||(a[4]=l("div",{class:"ticket-icon-large"},[l("i",{class:"fas fa-headset"})],-1)),l("div",V,[l("h1",null,n((null==(d=S.value)?void 0:d.title)||"Chargement..."),1),l("div",O,[l("span",$,"Ticket #"+n(null==(v=S.value)?void 0:v.id),1),l("span",{class:o(Ze(null==(p=S.value)?void 0:p.status))},n(He(null==(m=S.value)?void 0:m.status)),3),l("span",{class:o(Qe(null==(y=S.value)?void 0:y.priority))},n(We(null==(b=S.value)?void 0:b.priority)),3)])])])]),l("div",B,["open"===(null==(T=S.value)?void 0:T.status)?(c(),t("button",{key:0,class:"btn btn-outline btn-sm",onClick:Xe},a[5]||(a[5]=[l("i",{class:"fas fa-times"},null,-1),i(" Fermer le ticket ")]))):s("",!0),"closed"===(null==(I=S.value)?void 0:I.status)?(c(),t("button",{key:1,class:"btn btn-outline btn-sm",onClick:Je},a[6]||(a[6]=[l("i",{class:"fas fa-redo"},null,-1),i(" Rouvrir le ticket ")]))):s("",!0)])]),D.value?(c(),t("div",z,a[7]||(a[7]=[l("div",{class:"loading-spinner"},[l("i",{class:"fas fa-spinner fa-spin"}),l("p",null,"Chargement des détails du ticket...")],-1)]))):A.value?(c(),t("div",G,[l("div",U,[a[9]||(a[9]=l("i",{class:"fas fa-exclamation-triangle"},null,-1)),a[10]||(a[10]=l("h3",null,"Erreur de chargement",-1)),l("p",null,n(A.value),1),l("button",{class:"btn btn-primary",onClick:a[0]||(a[0]=()=>Be())},a[8]||(a[8]=[l("i",{class:"fas fa-refresh"},null,-1),i(" Réessayer ")]))])])):S.value?(c(),t("div",X,[l("div",J,[l("div",Z,[a[19]||(a[19]=l("h2",null,[l("i",{class:"fas fa-info-circle"}),i(" Informations du ticket ")],-1)),l("div",H,[l("div",Q,[a[11]||(a[11]=l("label",null,"Numéro de ticket",-1)),l("span",W,"#"+n(S.value.id),1)]),l("div",Y,[a[12]||(a[12]=l("label",null,"Sujet",-1)),l("span",null,n(S.value.title),1)]),l("div",ee,[a[13]||(a[13]=l("label",null,"Statut",-1)),l("span",{class:o(Ze(S.value.status))},n(He(S.value.status)),3)]),l("div",ae,[a[14]||(a[14]=l("label",null,"Priorité",-1)),l("span",{class:o(Qe(S.value.priority))},n(We(S.value.priority)),3)]),l("div",te,[a[15]||(a[15]=l("label",null,"Date de création",-1)),l("span",null,n(ea(S.value.created_at)),1)]),S.value.updated_at!==S.value.created_at?(c(),t("div",se,[a[16]||(a[16]=l("label",null,"Dernière mise à jour",-1)),l("span",null,n(ea(S.value.updated_at)),1)])):s("",!0),a[18]||(a[18]=l("div",{class:"info-item",style:{display:"none"}},[l("label",null,"Département"),l("span",null,"Support général")],-1)),S.value.assigned_to?(c(),t("div",le,[a[17]||(a[17]=l("label",null,"Assigné à",-1)),l("span",null,n(S.value.assigned_to),1)])):s("",!0)])])]),l("div",ne,[l("div",ie,[a[23]||(a[23]=l("h2",null,[l("i",{class:"fas fa-comments"}),i(" Conversation ")],-1)),l("div",re,[l("div",ue,[a[21]||(a[21]=l("div",{class:"message-avatar"},[l("i",{class:"fas fa-user"})],-1)),l("div",oe,[l("div",ce,[a[20]||(a[20]=l("span",{class:"message-author"},"Vous",-1)),l("span",de,n(ea(S.value.created_at)),1)]),l("div",ve,n(S.value.message),1)])]),(c(!0),t(r,null,u(j.value,e=>(c(),t("div",{key:e.id,class:o(["message-item",e.is_staff?"staff-message":"client-message"])},[l("div",pe,[l("i",{class:o(e.is_staff?"fas fa-user-tie":"fas fa-user")},null,2)]),l("div",me,[l("div",ge,[l("span",fe,n(e.is_staff?e.author_name||"Support":"Vous"),1),l("span",he,n(ea(e.created_at)),1)]),l("div",ke,n(e.message),1),e.attachments&&e.attachments.length>0?(c(),t("div",ye,[(c(!0),t(r,null,u(e.attachments,e=>(c(),t("div",{key:e.id,class:"attachment-item"},[a[22]||(a[22]=l("i",{class:"fas fa-paperclip"},null,-1)),l("a",{href:e.url,target:"_blank"},n(e.filename),9,be)]))),128))])):s("",!0)])],2))),128))]),Me.value>1?(c(),g(L,{key:0,"current-page":M.value,"total-pages":Me.value,total:Le.value,"per-page":Ne.value,onPageChange:Ye},null,8,["current-page","total-pages","total","per-page"])):s("",!0)])]),Oe.value?(c(),t("div",Te,[l("div",Ie,[a[31]||(a[31]=l("h2",null,[l("i",{class:"fas fa-reply"}),i(" Ajouter une réponse ")],-1)),l("form",{onSubmit:f(ze,["prevent"]),class:"reply-form"},[l("div",Ee,[a[24]||(a[24]=l("label",{for:"reply-message"},"Votre message",-1)),h(l("textarea",{id:"reply-message","onUpdate:modelValue":a[1]||(a[1]=e=>qe.value.message=e),class:"form-textarea",rows:"6",placeholder:"Tapez votre réponse ici...",required:""},null,512),[[k,qe.value.message]])]),l("div",Ce,[a[25]||(a[25]=l("label",{for:"reply-attachments"},"Pièces jointes (optionnel)",-1)),l("input",{id:"reply-attachments",type:"file",multiple:"",onChange:Ge,class:"form-file",accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif"},null,32),a[26]||(a[26]=l("div",{class:"file-info"}," Formats acceptés: PDF, DOC, DOCX, TXT, JPG, PNG, GIF (max 10MB par fichier) ",-1))]),R.value.length>0?(c(),t("div",Pe,[a[29]||(a[29]=l("h4",null,"Fichiers sélectionnés:",-1)),(c(!0),t(r,null,u(R.value,(e,s)=>(c(),t("div",{key:s,class:"file-item"},[a[28]||(a[28]=l("i",{class:"fas fa-file"},null,-1)),l("span",null,n(e.name),1),l("button",{type:"button",onClick:e=>(e=>{R.value.splice(e,1)})(s),class:"remove-file"},a[27]||(a[27]=[l("i",{class:"fas fa-times"},null,-1)]),8,_e)]))),128))])):s("",!0),l("div",we,[l("button",{type:"button",class:"btn btn-outline",onClick:Ue},a[30]||(a[30]=[l("i",{class:"fas fa-times"},null,-1),i(" Annuler ")])),l("button",{type:"submit",class:"btn btn-primary",disabled:K.value||!qe.value.message.trim()},[K.value?(c(),t("i",Ae)):(c(),t("i",Ke)),i(" "+n(K.value?"Envoi...":"Envoyer la réponse"),1)],8,De)])],32)])])):(c(),t("div",Se,[l("div",{class:"notice-card"},[a[33]||(a[33]=l("i",{class:"fas fa-info-circle"},null,-1)),a[34]||(a[34]=l("h3",null,"Ticket fermé",-1)),a[35]||(a[35]=l("p",null,"Ce ticket a été fermé. Pour ajouter une réponse, vous devez d'abord le rouvrir.",-1)),l("button",{class:"btn btn-primary",onClick:Je},a[32]||(a[32]=[l("i",{class:"fas fa-redo"},null,-1),i(" Rouvrir le ticket ")]))])]))])):s("",!0),Fe.value?(c(),t("div",je,[l("div",Re,[a[37]||(a[37]=l("i",{class:"fas fa-bell"},null,-1)),l("span",null,n(Ve.value),1),l("button",{onClick:a[2]||(a[2]=e=>Fe.value=!1),class:"close-notification"},a[36]||(a[36]=[l("i",{class:"fas fa-times"},null,-1)]))])])):s("",!0)])}}}),[["__scopeId","data-v-175dcecb"]]);export{Me as default};
