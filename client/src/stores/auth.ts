/**
 * Store d'authentification client
 * 
 * Gère l'état d'authentification, la connexion, la déconnexion et la persistance
 * de l'utilisateur client dans l'application TechCMS.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '../services/api'
import logger from '../services/logger'
import router from '../router'

// Interface pour l'utilisateur client
interface ClientUser {
  id: number
  email: string
  firstName: string
  lastName: string
  company?: string
  role: 'client'
}

// Interface pour les données d'inscription
interface RegisterData {
  firstname: string
  lastname: string
  email: string
  company?: string // Seul champ optionnel
  phone: string
  address: string
  postal_code: string
  city: string
  country: string
  password: string
  passwordConfirmation: string
  acceptTerms: boolean
}



// Fonction utilitaire pour récupérer le token stocké
const getStoredToken = (): string | null => {
  // Essayer localStorage d'abord
  const localToken = localStorage.getItem('techcms-client-token')
  if (localToken) return localToken

  // Essayer de récupérer depuis les cookies
  const cookies = document.cookie.split(';')
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=')
    if (name === 'techcms_client_token' || name === 'auth_token') {
      return value
    }
  }

  return null
}

export const useAuthStore = defineStore('clientAuth', () => {
  // État réactif
  const user = ref<ClientUser | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const initialized = ref(false)

  // Computed
  const isAuthenticated = computed(() => !!user.value)
  const userFullName = computed(() => 
    user.value ? `${user.value.firstName} ${user.value.lastName}` : ''
  )

  /**
   * Connexion client
   */
  const login = async (email: string, password: string, remember: boolean = false) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.auth.login(email, password, remember)

      if (response.data?.user) {
        user.value = response.data.user

        // Configurer le token d'authentification
        if (response.data?.token) {
          ApiService.setAuthToken(response.data.token)
          // Stocker le token pour les prochaines sessions
          localStorage.setItem('techcms-client-token', response.data.token)
          console.log('[AUTH] Token configuré et stocké')
        }

        console.log('[AUTH] Connexion client réussie:', user.value)
        return true
      } else {
        throw new Error('Réponse invalide du serveur')
      }
    } catch (err: any) {
      console.error('[AUTH] Erreur de connexion:', err)
      error.value = err.response?.data?.message || err.message || 'Erreur de connexion'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Inscription client
   */
  const register = async (data: RegisterData) => {
    loading.value = true
    error.value = null

    try {
      console.log('[AUTH] Envoi des données d\'inscription:', {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        company: data.company,
        phone: data.phone,
        address: data.address,
        postal_code: data.postal_code,
        city: data.city,
        country: data.country
      })

      const response = await ApiService.routes.auth.register({
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        company: data.company,
        phone: data.phone,
        address: data.address,
        postal_code: data.postal_code,
        city: data.city,
        country: data.country,
        password: data.password
      })

      console.log('[AUTH] Inscription client réussie:', response.data)
      return response.data
    } catch (err: any) {
      console.error('[AUTH] Erreur d\'inscription:', err)
      error.value = err.response?.data?.message || err.message || 'Erreur d\'inscription'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Déconnexion client
   */
  const logout = async () => {
    loading.value = true

    try {
      await ApiService.routes.auth.logout()
    } catch (err) {
      logger.error('[CLIENT-AUTH] Erreur lors de la déconnexion', { error: err })
    } finally {
      user.value = null
      error.value = null
      loading.value = false

      // Nettoyer le token d'authentification
      ApiService.setAuthToken(null)
      localStorage.removeItem('techcms-client-token')

      logger.info('[CLIENT-AUTH] Déconnexion client effectuée')

      // Rediriger vers la page de connexion
      router.push('/login')
    }
  }

  /**
   * Vérification de l'authentification
   */
  const checkAuth = async () => {
    logger.info('[CLIENT-AUTH] checkAuth() appelée', { initialized: initialized.value, isAuthenticated: isAuthenticated.value })

    // Ne pas skip la vérification même si initialisé - toujours vérifier les cookies
    loading.value = true

    try {
      logger.info('[CLIENT-AUTH] Appel API /me')
      const response = await ApiService.routes.auth.me()
      logger.info('[CLIENT-AUTH] Réponse API /me', { response })

      if (response.data?.user) {
        user.value = response.data.user

        // Configurer le token d'authentification pour les appels API
        if (response.data?.token) {
          ApiService.setAuthToken(response.data.token)
          console.log('[AUTH] Token configuré pour les appels API')
        } else {
          // Si pas de token dans la réponse, essayer de récupérer depuis les cookies/localStorage
          const token = getStoredToken()
          if (token) {
            ApiService.setAuthToken(token)
            console.log('[AUTH] Token récupéré depuis le stockage local')
          }
        }

        logger.info('[CLIENT-AUTH] Client authentifié via cookie', { user: user.value })
        return true
      }

      logger.info('[CLIENT-AUTH] Client non authentifié')
      return false
    } catch (err) {
      logger.error('[CLIENT-AUTH] Erreur lors de la vérification de l\'authentification', { error: err })
      user.value = null
      ApiService.setAuthToken(null) // Nettoyer le token en cas d'erreur
      return false
    } finally {
      loading.value = false
      initialized.value = true
    }
  }

  /**
   * Initialisation du store
   */
  const initialize = async () => {
    logger.info('[CLIENT-AUTH] initialize() appelée', { initialized: initialized.value })
    // Toujours vérifier l'authentification via cookies, même si localStorage dit qu'on est initialisé
    logger.info('[CLIENT-AUTH] Force checkAuth() pour vérifier les cookies')
    await checkAuth()
  }

  /**
   * Réinitialisation de l'état d'erreur
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * Mise à jour des informations utilisateur
   */
  const updateUser = (userData: Partial<ClientUser>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  /**
   * Synchronise le panier lors de la connexion
   * Transfère le panier localStorage vers l'espace client
   */
  const syncCartOnLogin = () => {
    // Cette méthode sera appelée par le composant de connexion
    // pour synchroniser le panier public avec l'espace privé
    console.log('[AUTH] Synchronisation du panier après connexion')
  }

  return {
    // État
    user,
    loading,
    error,
    initialized,
    
    // Computed
    isAuthenticated,
    userFullName,
    
    // Actions
    login,
    register,
    logout,
    checkAuth,
    initialize,
    clearError,
    updateUser,
    syncCartOnLogin
  }
}, {
  persist: {
    key: 'techcms-client-auth',
    storage: localStorage
  }
})
