<template>
  <div class="public-layout">
    <!-- En-tête public -->
    <header class="public-header">
      <div class="header-container">
        <!-- Logo et navigation -->
        <div class="header-left">
          <a href="/pricing" class="logo">
            <img src="@/assets/logo.png" alt="TechCMS" class="logo-img">
            <span class="logo-text">TechCMS</span>
          </a>

          <nav class="main-nav">
            <a href="/pricing" class="nav-link">
              <i class="fas fa-shopping-bag"></i>
              <span>Licences</span>
            </a>
            <a href="/website/features" class="nav-link">
              <i class="fas fa-list"></i>
              <span>Fonctionnalités</span>
            </a>
            <a href="/website/pricing" class="nav-link">
              <i class="fas fa-tags"></i>
              <span>Tarifs</span>
            </a>
            <a href="/website/contact" class="nav-link">
              <i class="fas fa-envelope"></i>
              <span>Contact</span>
            </a>
          </nav>
        </div>

        <!-- Actions utilisateur -->
        <div class="header-right">
          
          <!-- Authentification -->
          <div class="auth-actions">
            <router-link to="/login" class="btn btn-outline btn-sm">
              <i class="fas fa-sign-in-alt"></i>
              <span>Connexion</span>
            </router-link>
            <router-link to="/register" class="btn btn-primary btn-sm">
              <i class="fas fa-user-plus"></i>
              <span>Inscription</span>
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- Contenu principal -->
    <main class="public-content">
      <slot />
    </main>

    <!-- Pied de page -->
    <footer class="public-footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-section">
            <h3>TechCMS</h3>
            <p>Solution CMS professionnelle avec licences flexibles</p>
            <div class="social-links">
              <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="#" class="social-link">
                <i class="fab fa-github"></i>
              </a>
            </div>
          </div>
          
          <div class="footer-section">
            <h4>Produits</h4>
            <ul>
              <li><a href="/pricing">Licences TechCMS</a></li>
              <li><a href="/website/features">Fonctionnalités</a></li>
              <li><a href="/website/pricing">Tarifs</a></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>Support</h4>
            <ul>
              <li><a href="/website/contact">Contact</a></li>
              <li><a href="/website/about">À propos</a></li>
              <li><router-link to="/login">Espace client</router-link></li>
            </ul>
          </div>
          
          <div class="footer-section">
            <h4>Liens utiles</h4>
            <ul>
              <li><a href="/website">Site vitrine</a></li>
              <li><a href="/admin">Administration</a></li>
              <li><router-link to="/register">Créer un compte</router-link></li>
            </ul>
          </div>
        </div>
        
        <div class="footer-bottom">
          <p>&copy; 2024 TechCMS. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { useCartStore } from '@/stores/cart'

// Store
const cartStore = useCartStore()

// Méthodes utilitaires
const formatPrice = (price: number) => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR'
  }).format(price)
}
</script>

<style scoped>
.public-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

/* En-tête */
.public-header {
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  text-decoration: none;
  color: var(--primary-color);
  font-size: 1.25rem;
  font-weight: 700;
}

.logo-img {
  height: 40px;
  width: auto;
}

.main-nav {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  text-decoration: none;
  color: var(--text-secondary);
  border-radius: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: var(--primary-color);
  background: var(--hover-bg);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.cart-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  background: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: 25px;
  transition: all 0.2s ease;
  font-weight: 600;
  position: relative;
}

.cart-btn:hover {
  background: #1e40af;
  transform: translateY(-1px);
}

.cart-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
}

.auth-actions {
  display: flex;
  gap: 0.75rem;
}

/* Contenu principal */
.public-content {
  flex: 1;
  min-height: calc(100vh - 70px - 200px);
}

/* Pied de page */
.public-footer {
  background: var(--card-bg);
  border-top: 1px solid var(--border-color);
  margin-top: auto;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 2rem 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
  margin: 0 0 1rem 0;
  color: var(--text-primary);
}

.footer-section p {
  color: var(--text-secondary);
  margin: 0 0 1rem 0;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 0.5rem;
}

.footer-section a {
  color: var(--text-secondary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-section a:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.social-link {
  width: 40px;
  height: 40px;
  background: var(--hover-bg);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
}

.social-link:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  cursor: pointer;
  font-size: 0.875rem;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #1e40af;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--border-color);
}

.btn-outline:hover {
  background: var(--hover-bg);
  border-color: var(--primary-color);
}

/* Responsive */
@media (max-width: 768px) {
  .header-container {
    padding: 0 1rem;
    height: 60px;
  }
  
  .header-left {
    gap: 1rem;
  }
  
  .main-nav {
    gap: 0.75rem;
  }
  
  .nav-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .nav-link span {
    display: none;
  }
  
  .header-right {
    gap: 1rem;
  }
  
  .auth-actions {
    gap: 0.5rem;
  }
  
  .cart-total {
    display: none;
  }
  
  .footer-container {
    padding: 2rem 1rem 1rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 480px) {
  .logo-text {
    display: none;
  }
  
  .main-nav {
    gap: 0.5rem;
  }
  
  .nav-link {
    padding: 0.25rem 0.5rem;
  }
  
  .auth-actions .btn span {
    display: none;
  }
}
</style>
