# Protection des fichiers système
<Files ~ "^.*\.([Hh][Tt][Aa])">
 order allow,deny
 deny from all
 satisfy all
</Files>

Options -Indexes

<IfModule Litespeed>
    RewriteEngine On
    
    # Ne pas réécrire les fichiers et dossiers qui existent
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]

    # Ne pas réécrire les assets
    RewriteRule ^(templates|assets)/.*\.(css|js|jpg|jpeg|png|gif|ico|woff|woff2|ttf|eot|svg)$ - [L]
    
    # Réécriture pour l'API v1
    RewriteRule ^api/v1(/.*)?$ api/index.php [L,QSA]
    
    # Réécriture pour le panneau d'administration
    RewriteRule ^admin(/.*)?$ admin/dist/index.html [L]
    
    # Réécriture pour les autres URLs
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [L,QSA]
</IfModule>

# Protection des fichiers sensibles
<Files ~ "\.(env|json|lock)$">
    Order deny,allow
    Deny from all
</Files>

# Autoriser l'accès aux ressources statiques
<FilesMatch "\.(jpg|jpeg|png|gif|css|js|ico|woff|woff2|ttf|eot|svg)$">
    Allow from all
</FilesMatch>

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Sécurité
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Permissions-Policy "geolocation=(), microphone=(), camera=()"
</IfModule>

# Performance
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json
</IfModule>

# Protection des fichiers sensibles
<FilesMatch "^(\.env|composer\.json|composer\.lock|package\.json|package-lock\.json)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Réécriture d'URL
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteBase /
    
    # Redirection vers HTTPS
    RewriteCond %{HTTPS} off
    RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
    
    # Redirection du trafic vers index.php (sauf pour les applications Vue.js)
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/(admin|client|store|website)/.*$
    RewriteCond %{REQUEST_URI} !^/(features|pricing|contact|about)/?.*$
    RewriteCond %{REQUEST_URI} !^/$
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

RewriteEngine On

# Rediriger les requêtes API vers le point d'entrée API
RewriteCond %{REQUEST_URI} ^/api/v1/.*$
RewriteRule ^api/v1/(.*)$ /api/index.php [L,QSA]

# Rediriger toutes les autres requêtes /admin/* vers l'application Vue.js
RewriteCond %{REQUEST_URI} ^/admin/.*$
RewriteCond %{REQUEST_URI} !^/admin/dist/.*$
RewriteRule ^admin/.*$ /admin/dist/index.html [L]

# Rediriger toutes les autres requêtes /client/* vers l'application Vue.js
RewriteCond %{REQUEST_URI} ^/client/.*$
RewriteCond %{REQUEST_URI} !^/client/dist/.*$
RewriteRule ^client/.*$ /client/dist/index.html [L]

# Rediriger toutes les requêtes /store/* vers l'application Vue.js website (qui gère la boutique)
RewriteCond %{REQUEST_URI} ^/store/.*$
RewriteCond %{REQUEST_URI} !^/website/dist/.*$
RewriteRule ^store/.*$ /website/dist/index.html [L]

# Rediriger toutes les requêtes /website/* vers l'application Vue.js vitrine
RewriteCond %{REQUEST_URI} ^/website/.*$
RewriteCond %{REQUEST_URI} !^/website/dist/.*$
RewriteRule ^website/.*$ /website/dist/index.html [L]

# Empêcher l'accès direct aux fichiers PHP
<FilesMatch "\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Autoriser l'accès aux points d'entrée spécifiques
<Files "index.php">
    Order Allow,Deny
    Allow from all
</Files>

# Headers CORS pour l'API
<IfModule mod_headers.c>
    SetEnvIf Origin "^http(s)?://(.+\.)?(localhost:3000|tech-tik\.com)$" origin_is=$0
    Header always set Access-Control-Allow-Origin %{origin_is}e env=origin_is
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Authorization, Content-Type"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>

# Compression Gzip
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Cache-Control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# Protection des fichiers sensibles
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "(^#.*#|\.(bak|config|dist|fla|inc|ini|log|psd|sh|sql|sw[op])|~)$">
    Order allow,deny
    Deny from all
</FilesMatch>
