/**
 * Service API centralisé pour la vitrine publique
 *
 * Ce service gère toutes les routes API publiques de la vitrine,
 * incluant les templates, le contact et autres fonctionnalités publiques.
 */

import axios from 'axios';

// Configuration de l'instance axios pour toutes les requêtes API
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  timeout: 30000 // 30 secondes
});

// Configuration pour inclure les cookies dans les requêtes
apiClient.defaults.withCredentials = true;

// Structure modulaire des routes API
const ApiRoutes = {
  auth: {
    /**
     * Connexion client
     */
    login: async (email: string, password: string, remember: boolean = false) => {
      return await ApiService.post('/api/v1/website/auth/login', {
        email,
        password,
        remember
      });
    },

    /**
     * Inscription client
     */
    register: async (data: any) => {
      return await ApiService.post('/api/v1/website/auth/register', data);
    },

    /**
     * Récupérer le profil utilisateur
     */
    me: async () => {
      return await ApiService.get('/api/v1/website/auth/me');
    },

    /**
     * Déconnexion
     */
    logout: async () => {
      return await ApiService.post('/api/v1/website/auth/logout');
    }
  },

  logs: {
    /**
     * Enregistrer un log frontend
     */
    store: async (level: string, message: string, context: any = {}) => {
      return await ApiService.post('/api/v1/website/logs', {
        level,
        message,
        context,
        url: window.location.href,
        timestamp: new Date().toISOString()
      });
    },

    /**
     * Enregistrer plusieurs logs en batch
     */
    batch: async (logs: Array<{level: string, message: string, context?: any}>) => {
      return await ApiService.post('/api/v1/website/logs/batch', {
        logs: logs.map(log => ({
          ...log,
          url: window.location.href,
          timestamp: new Date().toISOString()
        }))
      });
    }
  }
};

/**
 * Service API principal pour la vitrine publique
 */
export class ApiService {
  /**
   * Routes API organisées par domaine fonctionnel
   */
  static routes = ApiRoutes;

  /**
   * Instance axios configurée
   */
  static get client() {
    return apiClient;
  }

  /**
   * Définir le token d'authentification
   */
  static setAuthToken(token: string | null) {
    if (token) {
      apiClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete apiClient.defaults.headers.common['Authorization'];
    }
  }

  /**
   * Supprimer le token d'authentification
   */
  static clearAuthToken() {
    delete apiClient.defaults.headers.common['Authorization'];
  }

  /**
   * Méthode GET générique
   */
  static async get(url: string, params?: any) {
    try {
      const response = await apiClient.get(url, { params });
      return response.data;
    } catch (error: any) {
      console.error('[ApiService] Erreur GET:', error);
      throw error;
    }
  }

  /**
   * Méthode POST générique
   */
  static async post(url: string, data?: any) {
    try {
      const response = await apiClient.post(url, data);
      return response.data;
    } catch (error: any) {
      console.error('[ApiService] Erreur POST:', error);
      throw error;
    }
  }

  /**
   * Méthode PUT générique
   */
  static async put(url: string, data?: any) {
    try {
      const response = await apiClient.put(url, data);
      return response.data;
    } catch (error: any) {
      console.error('[ApiService] Erreur PUT:', error);
      throw error;
    }
  }

  /**
   * Méthode DELETE générique
   */
  static async delete(url: string) {
    try {
      const response = await apiClient.delete(url);
      return response.data;
    } catch (error: any) {
      console.error('[ApiService] Erreur DELETE:', error);
      throw error;
    }
  }



  /**
   * Intercepteur de réponse pour gérer les erreurs
   */
  static setupInterceptors() {
    apiClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('[ApiService] Erreur API:', error);
        return Promise.reject(error);
      }
    );
  }
}

// Configurer les intercepteurs au chargement du module
ApiService.setupInterceptors();

export default ApiService;