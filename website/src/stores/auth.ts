/**
 * Store d'authentification client
 * 
 * Gère l'état d'authentification, la connexion, la déconnexion et la persistance
 * de l'utilisateur client dans l'application TechCMS.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '../services/api'
import logger from '../services/logger'

// Interface pour l'utilisateur client
interface ClientUser {
  id: number
  email: string
  firstName: string
  lastName: string
  company?: string
  phone?: string
  address?: string
  city?: string
  postal_code?: string
  country?: string
  role: 'client'
}

// Interface pour les données d'inscription
interface RegisterData {
  firstname: string
  lastname: string
  email: string
  company?: string // Seul champ optionnel
  phone: string
  address: string
  postal_code: string
  city: string
  country: string
  password: string
  passwordConfirmation: string
  acceptTerms: boolean
}



export const useAuthStore = defineStore('clientAuth', () => {
  // État réactif
  const user = ref<ClientUser | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const initialized = ref(false)

  // Computed
  const isAuthenticated = computed(() => !!user.value)
  const userFullName = computed(() => 
    user.value ? `${user.value.firstName} ${user.value.lastName}` : ''
  )

  /**
   * Connexion client
   */
  const login = async (email: string, password: string, remember: boolean = false) => {
    loading.value = true
    error.value = null

    try {
      const response = await ApiService.routes.auth.login(email, password, remember)

      logger.info('[AUTH] Réponse API complète', { response })
      logger.info('[AUTH] Vérification réponse', {
        success: response.success,
        hasUser: !!response.user,
        hasToken: !!response.token
      })

      if (response.success && response.user && response.token) {
        user.value = {
          id: response.user.id,
          email: response.user.email,
          firstName: response.user.first_name,
          lastName: response.user.last_name,
          company: response.user.company,
          phone: response.user.phone,
          address: response.user.address_line_1,
          city: response.user.city,
          postal_code: response.user.postal_code,
          country: response.user.country,
          role: 'client'
        }

        // Sauvegarder le token pour les requêtes API (pas de localStorage)
        ApiService.setAuthToken(response.token)

        logger.info('[AUTH] Connexion client réussie', { user: user.value })

        // Forcer la réactivité de Vue
        initialized.value = true

        return true
      } else {
        throw new Error(response.message || 'Erreur de connexion')
      }
    } catch (err: any) {
      console.error('[AUTH] Erreur de connexion complète:', err)
      console.error('[AUTH] Type d\'erreur:', typeof err)
      console.error('[AUTH] err.message:', err.message)
      console.error('[AUTH] err.response:', err.response)
      console.error('[AUTH] err.stack:', err.stack)

      error.value = err.response?.data?.message || err.message || 'Erreur de connexion'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Inscription client
   */
  const register = async (data: RegisterData) => {
    loading.value = true
    error.value = null

    try {
      logger.info('[AUTH] Envoi des données d\'inscription', {
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        company: data.company,
        phone: data.phone,
        address: data.address,
        postal_code: data.postal_code,
        city: data.city,
        country: data.country
      })

      const response = await ApiService.routes.auth.register({
        firstname: data.firstname,
        lastname: data.lastname,
        email: data.email,
        company: data.company,
        phone: data.phone,
        address: data.address,
        postal_code: data.postal_code,
        city: data.city,
        country: data.country,
        password: data.password
      })

      logger.info('[AUTH] Inscription client réussie', { response })

      if (response.success && response.user && response.token) {
        user.value = {
          id: response.user.id,
          email: response.user.email,
          firstName: response.user.first_name,
          lastName: response.user.last_name,
          company: response.user.company,
          phone: response.user.phone,
          address: response.user.address_line_1,
          city: response.user.city,
          postal_code: response.user.postal_code,
          country: response.user.country,
          role: 'client'
        }

        // Sauvegarder le token pour les requêtes API (pas de localStorage)
        ApiService.setAuthToken(response.token)

        logger.info('[AUTH] État utilisateur mis à jour après inscription', { user: user.value })

        // Forcer la réactivité de Vue
        initialized.value = true

        return true
      } else {
        throw new Error(response.message || 'Erreur d\'inscription')
      }
    } catch (err: any) {
      console.error('[AUTH] Erreur d\'inscription:', err)
      error.value = err.response?.data?.message || err.message || 'Erreur d\'inscription'
      throw err
    } finally {
      loading.value = false
    }
  }

  /**
   * Déconnexion client
   */
  const logout = async () => {
    loading.value = true

    try {
      // Le serveur supprimera automatiquement les cookies httpOnly
      await ApiService.routes.auth.logout()
    } catch (err) {
      console.error('[AUTH] Erreur lors de la déconnexion:', err)
    } finally {
      user.value = null
      error.value = null
      loading.value = false
      initialized.value = false

      // Nettoyer le token d'authentification des headers
      ApiService.setAuthToken(null)

      logger.info('[AUTH] Déconnexion client effectuée')
    }
  }

  /**
   * Vérification de l'authentification
   */
  const checkAuth = async () => {
    if (initialized.value) {
      return isAuthenticated.value
    }

    loading.value = true

    try {
      // L'authentification se fait uniquement via les cookies httpOnly
      // Le serveur vérifiera automatiquement le cookie client_token
      const response = await ApiService.routes.auth.me()

      // Gérer les deux formats de réponse
      const userData = response.user || response.data?.user

      if (userData) {
        user.value = {
          id: userData.id,
          email: userData.email,
          firstName: userData.first_name || userData.firstName,
          lastName: userData.last_name || userData.lastName,
          company: userData.company,
          phone: userData.phone,
          address: userData.address_line_1 || userData.address,
          city: userData.city,
          postal_code: userData.postal_code,
          country: userData.country,
          role: 'client'
        }
        logger.info('[AUTH] Client authentifié via cookie', { user: user.value })
        return true
      }

      logger.info('[AUTH] Client non authentifié')
      return false
    } catch (err) {
      logger.error('[AUTH] Erreur lors de la vérification de l\'authentification', { error: err })
      user.value = null
      return false
    } finally {
      loading.value = false
      initialized.value = true
    }
  }

  /**
   * Initialisation du store
   */
  const initialize = async () => {
    if (!initialized.value) {
      await checkAuth()
    }
  }

  /**
   * Réinitialisation de l'état d'erreur
   */
  const clearError = () => {
    error.value = null
  }

  /**
   * Mise à jour des informations utilisateur
   */
  const updateUser = (userData: Partial<ClientUser>) => {
    if (user.value) {
      user.value = { ...user.value, ...userData }
    }
  }

  return {
    // État
    user,
    loading,
    error,
    initialized,

    // Computed
    isAuthenticated,
    userFullName,

    // Actions
    login,
    register,
    logout,
    checkAuth,
    initialize,
    clearError,
    updateUser
  }
})
