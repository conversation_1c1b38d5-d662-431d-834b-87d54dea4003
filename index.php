<?php
/**
 * Point d'entrée principal de l'application
 *
 * @package TechCMS
 * @version 1.0.0
 */

// Redirection vers la vitrine pour la racine et les pages vitrine
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$path = parse_url($requestUri, PHP_URL_PATH);

// Si on accède à la racine, aux pages vitrine ou aux routes store, rediriger vers l'application website
if ($path === '/' || preg_match('/^\/(features|pricing|contact|about|store)\/?/', $path)) {
    $websiteIndexPath = __DIR__ . '/website/dist/index.html';

    if (file_exists($websiteIndexPath)) {
        // Servir le fichier HTML de la vitrine
        header('Content-Type: text/html; charset=utf-8');
        readfile($websiteIndexPath);
        exit;
    } else {
        // Fallback si le build de la vitrine n'existe pas
        http_response_code(503);
        echo json_encode([
            'error' => 'Service Unavailable',
            'message' => 'La vitrine n\'est pas encore disponible. Veuillez construire l\'application website.'
        ]);
        exit;
    }
}

// Définition de la constante de base
define('ROOTPATH', __DIR__);

// Chargement de l'autoloader Composer
require_once ROOTPATH . '/vendor/autoload.php';

// Vérification de l'installation
if (!file_exists(ROOTPATH . '/config/config.php')) {
    $baseUrl = rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    header('Location: ' . $protocol . $host . $baseUrl . '/install');
    exit;
}

// Chargement de l'autoloader
require_once ROOTPATH . '/includes/init.php';

// Import des classes nécessaires
use TechCMS\Common\Core\App;
use TechCMS\Common\Core\Logger;
use TechCMS\Common\Core\Session;

// Démarrage de la session de manière sécurisée
Session::getInstance();

// Création et exécution de l'application
App::getInstance()->run();
