<?php
/**
 * Modèle pour la gestion des clients
 * 
 * @package TechCMS
 * @version 1.0.0
 */

namespace TechCMS\Common\Models;

use PDO;
use TechCMS\Common\Models\BaseModel;
use TechCMS\Common\Core\Logger;

class ClientModel extends BaseModel {
    protected $table = 'clients';
    protected $fillable = [
        'firstname', 'lastname', 'email', 'password', 'company', 
        'phone', 'address', 'city', 'country', 'postal_code', 'status',
        'created_at', 'updated_at'
    ];
    protected $hidden = ['password'];

    /**
     * Trouve un client par son email
     */
    public function findByEmail($email) {
        return $this->where('email', $email)[0] ?? null;
    }

    /**
     * Crée un nouveau client
     */
    public function create(array $data) {
        Logger::channel('api')->info('[ClientModel] Création client - Données reçues', [
            'data_keys' => array_keys($data),
            'email' => $data['email'] ?? 'non défini',
            'firstname' => $data['firstname'] ?? 'non défini',
            'lastname' => $data['lastname'] ?? 'non défini',
            'password_present' => isset($data['password']) ? 'yes' : 'no',
            'password_length' => isset($data['password']) ? strlen($data['password']) : 0
        ]);

        if (isset($data['password'])) {
            $originalPassword = $data['password'];
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            Logger::channel('api')->info('[ClientModel] Mot de passe haché', [
                'original_length' => strlen($originalPassword),
                'hash_length' => strlen($data['password']),
                'hash_prefix' => substr($data['password'], 0, 20) . '...'
            ]);
        }

        Logger::channel('api')->info('[ClientModel] Appel parent::create()', [
            'data_prepared' => array_keys($data)
        ]);

        $result = parent::create($data);

        Logger::channel('api')->info('[ClientModel] Résultat parent::create()', [
            'result' => $result,
            'result_type' => gettype($result)
        ]);

        return $result;
    }

    /**
     * Met à jour un client
     */
    public function update($id, array $data) {
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        return parent::update($id, $data);
    }

    /**
     * Authentifie un client
     */
    public function authenticate($email, $password) {
        $client = $this->findByEmail($email);

        // Log détaillé pour le débogage
        Logger::channel('api')->info('[ClientModel] Authentification', [
            'email' => $email,
            'client_found' => $client ? 'yes' : 'no'
        ]);

        if (!$client) {
            Logger::channel('api')->warning('[ClientModel] Échec: Client non trouvé', ['email' => $email]);
            return false;
        }

        Logger::channel('api')->info('[ClientModel] Client trouvé', [
            'client_id' => $client['id'],
            'status' => $client['status'],
            'email' => $client['email']
        ]);

        if ($client['status'] !== 'active') {
            Logger::channel('api')->warning('[ClientModel] Échec: Client inactif', [
                'client_id' => $client['id'],
                'status' => $client['status']
            ]);
            return false;
        }

        Logger::channel('api')->info('[ClientModel] Vérification mot de passe', [
            'client_id' => $client['id'],
            'hash_complet' => $client['password'],
            'password_complet' => $password,
            'password_length' => strlen($password),
            'password_bytes' => bin2hex($password)
        ]);

        $passwordMatch = password_verify($password, $client['password']);
        Logger::channel('api')->info('[ClientModel] Résultat vérification', [
            'client_id' => $client['id'],
            'password_match' => $passwordMatch ? 'success' : 'failed'
        ]);

        if (!$passwordMatch) {
            Logger::channel('api')->warning('[ClientModel] Échec: Mot de passe incorrect', [
                'client_id' => $client['id']
            ]);
            return false;
        }

        $this->update($client['id'], ['last_login' => date('Y-m-d H:i:s')]);
        unset($client['password']);
        Logger::channel('api')->info('[ClientModel] Authentification réussie', [
            'client_id' => $client['id']
        ]);
        return $client;
    }


    /**
     * Obtient les factures d'un client
     */
    public function getInvoices($clientId) {
        $stmt = $this->db->prepare("
            SELECT i.*
            FROM invoices i
            WHERE i.client_id = :client_id
            ORDER BY i.created_at DESC
        ");
        $stmt->execute(['client_id' => $clientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtient les tickets d'un client
     */
    public function getTickets($clientId) {
        $stmt = $this->db->prepare("
            SELECT t.*, 
                   (SELECT COUNT(*) FROM ticket_replies WHERE ticket_id = t.id) as reply_count
            FROM tickets t
            WHERE t.client_id = :client_id
            ORDER BY t.updated_at DESC
        ");
        $stmt->execute(['client_id' => $clientId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Recherche des clients
     */
    public function search($query) {
        $stmt = $this->db->prepare("
            SELECT c.*,
                   COUNT(DISTINCT i.id) as invoices_count,
                   COUNT(DISTINCT t.id) as tickets_count
            FROM {$this->table} c
            LEFT JOIN invoices i ON c.id = i.client_id
            LEFT JOIN tickets t ON c.id = t.client_id
            WHERE c.firstname LIKE :query
            OR c.lastname LIKE :query
            OR c.email LIKE :query
            OR c.company LIKE :query
            GROUP BY c.id
        ");
        $stmt->execute(['query' => "%{$query}%"]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtient les statistiques d'un client
     */
    public function getStats($clientId) {
        return [
            'invoices' => $this->getInvoiceStats($clientId),
            'tickets' => $this->getTicketStats($clientId)
        ];
    }

    private function getInvoiceStats($clientId) {
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid,
                SUM(CASE WHEN status = 'unpaid' THEN 1 ELSE 0 END) as unpaid,
                SUM(total) as total_amount,
                SUM(CASE WHEN status = 'unpaid' THEN total ELSE 0 END) as unpaid_amount
            FROM invoices
            WHERE client_id = :client_id
        ");
        $stmt->execute(['client_id' => $clientId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    private function getTicketStats($clientId) {
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open,
                SUM(CASE WHEN status = 'answered' THEN 1 ELSE 0 END) as answered,
                SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed
            FROM tickets
            WHERE client_id = :client_id
        ");
        $stmt->execute(['client_id' => $clientId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Récupère tous les clients
     * 
     * @param array $columns Les colonnes à récupérer
     * @return array Liste des clients
     */
    public function getAll($columns = ['*']) {
        $sql = "SELECT " . implode(', ', $columns) . " FROM {$this->table}";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtient les clients les plus récents
     * 
     * @param int $limit Nombre de clients à retourner
     * @return array Liste des clients
     */
    public function getRecent($limit = 5) {
        $stmt = $this->db->prepare("
            SELECT * FROM {$this->table}
            ORDER BY created_at DESC
            LIMIT :limit
        ");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Obtient le revenu total
     * 
     * @return float Revenu total
     */
    public function getTotalRevenue() {
        $query = "SELECT COALESCE(SUM(total), 0) as total FROM invoices WHERE status = 'paid'";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        return (float) $result['total'];
    }

    /**
     * Compte les clients actifs
     */
    public function countActive() {
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM {$this->table} WHERE status = :status");
        $stmt->execute(['status' => 'active']);
        return (int) $stmt->fetchColumn();
    }

    /**
     * Compte les nouveaux clients (30 derniers jours)
     */
    public function countNew() {
        $thirtyDaysAgo = date('Y-m-d H:i:s', strtotime('-30 days'));
        $stmt = $this->db->prepare("SELECT COUNT(*) FROM {$this->table} WHERE created_at >= :date");
        $stmt->execute(['date' => $thirtyDaysAgo]);
        return (int) $stmt->fetchColumn();
    }

    /**
     * Récupère la liste des clients avec pagination et filtres
     * 
     * @param int $page Numéro de la page
     * @param int $perPage Nombre d'éléments par page
     * @param array $filters Filtres à appliquer
     * @return array Liste des clients
     */
    public function getClients(int $page = 1, int $perPage = 10, array $filters = []): array 
    {
        $offset = ($page - 1) * $perPage;
        $query = "SELECT * FROM {$this->table} WHERE 1=1";
        $params = [];

        // Application des filtres
        if (!empty($filters['search'])) {
            $searchTerm = "%{$filters['search']}%";
            $query .= " AND (firstname LIKE :search1 OR lastname LIKE :search2 OR email LIKE :search3 OR company LIKE :search4)";
            $params[':search1'] = $searchTerm;
            $params[':search2'] = $searchTerm;
            $params[':search3'] = $searchTerm;
            $params[':search4'] = $searchTerm;
        }

        if (!empty($filters['status'])) {
            $query .= " AND status = :status";
            $params[':status'] = $filters['status'];
        }



        // Ajout de la pagination
        $query .= " ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
        $params[':limit'] = (int) $perPage;
        $params[':offset'] = (int) $offset;

        $stmt = $this->db->prepare($query);
        
        // Liaison des paramètres avec leur type
        foreach ($params as $key => &$value) {
            $type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
            $stmt->bindValue($key, $value, $type);
        }

        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Compte le nombre total de clients avec les filtres appliqués
     * 
     * @param array $filters Filtres à appliquer
     * @return int Nombre total de clients
     */
    public function countClients(array $filters = []): int
    {
        $query = "SELECT COUNT(*) as total FROM {$this->table} WHERE 1=1";
        $params = [];

        if (!empty($filters['search'])) {
            $searchTerm = "%{$filters['search']}%";
            $query .= " AND (firstname LIKE ? OR lastname LIKE ? OR email LIKE ? OR company LIKE ?)";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        if (!empty($filters['status'])) {
            $query .= " AND status = ?";
            $params[] = $filters['status'];
        }



        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return (int) $result['total'];
    }
}
